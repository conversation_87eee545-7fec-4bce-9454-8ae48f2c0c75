import { Codec, Decoder, Encoder, FixedSizeCodec, FixedSizeDecoder, FixedSizeEncoder, VariableSizeCodec, VariableSizeDecoder, VariableSizeEncoder } from '@solana/codecs-core';
type WrapInFixedSizeEncoder<TFrom> = {
    [P in keyof TFrom]: FixedSizeEncoder<TFrom[P]>;
};
type WrapInEncoder<TFrom> = {
    [P in keyof TFrom]: Encoder<TFrom[P]>;
};
type WrapInFixedSizeDecoder<TTo> = {
    [P in keyof TTo]: FixedSizeDecoder<TTo[P]>;
};
type WrapInDecoder<TTo> = {
    [P in keyof TTo]: Decoder<TTo[P]>;
};
type WrapInCodec<TFrom, TTo extends TFrom> = {
    [P in keyof TFrom]: Codec<TFrom[P], TTo[P]>;
};
type WrapInFixedSizeCodec<TFrom, TTo extends TFrom> = {
    [P in keyof TFrom]: FixedSizeCodec<TFrom[P], TTo[P]>;
};
type AnyArray = any[];
/**
 * Creates a encoder for a tuple-like array.
 *
 * @param items - The encoders to use for each item in the tuple.
 */
export declare function getTupleEncoder<TFrom extends AnyArray>(items: WrapInFixedSizeEncoder<[...TFrom]>): FixedSizeEncoder<TFrom>;
export declare function getTupleEncoder<TFrom extends AnyArray>(items: WrapInEncoder<[...TFrom]>): VariableSizeEncoder<TFrom>;
/**
 * Creates a decoder for a tuple-like array.
 *
 * @param items - The decoders to use for each item in the tuple.
 */
export declare function getTupleDecoder<TTo extends AnyArray>(items: WrapInFixedSizeDecoder<[...TTo]>): FixedSizeDecoder<TTo>;
export declare function getTupleDecoder<TTo extends AnyArray>(items: WrapInDecoder<[...TTo]>): VariableSizeDecoder<TTo>;
/**
 * Creates a codec for a tuple-like array.
 *
 * @param items - The codecs to use for each item in the tuple.
 */
export declare function getTupleCodec<TFrom extends AnyArray, TTo extends TFrom = TFrom>(items: WrapInFixedSizeCodec<[...TFrom], [...TTo]>): FixedSizeCodec<TFrom, TTo>;
export declare function getTupleCodec<TFrom extends AnyArray, TTo extends TFrom = TFrom>(items: WrapInCodec<[...TFrom], [...TTo]>): VariableSizeCodec<TFrom, TTo>;
export {};
//# sourceMappingURL=tuple.d.ts.map