export declare function maxCodecSizes(sizes: (number | null)[]): number | null;
export declare function sumCodecSizes(sizes: (number | null)[]): number | null;
export declare function getFixedSize(codec: {
    fixedSize: number;
} | {
    maxSize?: number;
}): number | null;
export declare function getMaxSize(codec: {
    fixedSize: number;
} | {
    maxSize?: number;
}): number | null;
//# sourceMappingURL=utils.d.ts.map