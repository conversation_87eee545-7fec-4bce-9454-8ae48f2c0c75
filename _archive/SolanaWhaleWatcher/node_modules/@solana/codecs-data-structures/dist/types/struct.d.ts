import { Codec, Decoder, Encoder, FixedSizeCodec, FixedSizeDecoder, FixedSizeEncoder, VariableSizeCodec, VariableSizeDecoder, VariableSizeEncoder } from '@solana/codecs-core';
/** Get the name and encoder of each field in a struct. */
export type StructToEncoderTuple<TFrom extends object> = Array<{
    [K in keyof TFrom]: [K, Encoder<TFrom[K]>];
}[keyof TFrom]>;
/** Get the name and fixed-size encoder of each field in a struct. */
export type StructToFixedSizeEncoderTuple<TFrom extends object> = Array<{
    [K in keyof TFrom]: [K, FixedSizeEncoder<TFrom[K]>];
}[keyof TFrom]>;
/** Get the name and decoder of each field in a struct. */
export type StructToDecoderTuple<TTo extends object> = Array<{
    [K in keyof TTo]: [K, Decoder<TTo[K]>];
}[keyof TTo]>;
/** Get the name and fixed-size decoder of each field in a struct. */
export type StructToFixedSizeDecoderTuple<TTo extends object> = Array<{
    [K in keyof TTo]: [K, FixedSizeDecoder<TTo[K]>];
}[keyof TTo]>;
/** Get the name and codec of each field in a struct. */
export type StructToCodecTuple<TFrom extends object, TTo extends TFrom> = Array<{
    [K in keyof TFrom]: [K, Codec<TFrom[K], TTo[K]>];
}[keyof TFrom]>;
/** Get the name and fixed-size codec of each field in a struct. */
export type StructToFixedSizeCodecTuple<TFrom extends object, TTo extends TFrom> = Array<{
    [K in keyof TFrom]: [K, FixedSizeCodec<TFrom[K], TTo[K]>];
}[keyof TFrom]>;
/**
 * Creates a encoder for a custom object.
 *
 * @param fields - The name and encoder of each field.
 */
export declare function getStructEncoder<TFrom extends object>(fields: StructToFixedSizeEncoderTuple<TFrom>): FixedSizeEncoder<TFrom>;
export declare function getStructEncoder<TFrom extends object>(fields: StructToEncoderTuple<TFrom>): VariableSizeEncoder<TFrom>;
/**
 * Creates a decoder for a custom object.
 *
 * @param fields - The name and decoder of each field.
 */
export declare function getStructDecoder<TTo extends object>(fields: StructToFixedSizeDecoderTuple<TTo>): FixedSizeDecoder<TTo>;
export declare function getStructDecoder<TTo extends object>(fields: StructToDecoderTuple<TTo>): VariableSizeDecoder<TTo>;
/**
 * Creates a codec for a custom object.
 *
 * @param fields - The name and codec of each field.
 */
export declare function getStructCodec<TFrom extends object, TTo extends TFrom = TFrom>(fields: StructToFixedSizeCodecTuple<TFrom, TTo>): FixedSizeCodec<TFrom, TTo>;
export declare function getStructCodec<TFrom extends object, TTo extends TFrom = TFrom>(fields: StructToCodecTuple<TFrom, TTo>): VariableSizeCodec<TFrom, TTo>;
//# sourceMappingURL=struct.d.ts.map