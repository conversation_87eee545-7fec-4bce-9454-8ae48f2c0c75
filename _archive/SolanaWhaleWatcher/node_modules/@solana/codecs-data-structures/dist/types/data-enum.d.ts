import { Codec, Decoder, Encoder } from '@solana/codecs-core';
import { NumberCodec, NumberDecoder, NumberEncoder } from '@solana/codecs-numbers';
/**
 * Defines a data enum using discriminated union types.
 *
 * @example
 * ```ts
 * type WebPageEvent =
 *   | { __kind: 'pageview', url: string }
 *   | { __kind: 'click', x: number, y: number };
 * ```
 */
export type DataEnum = {
    __kind: string;
};
/**
 * Extracts a variant from a data enum.
 *
 * @example
 * ```ts
 * type WebPageEvent =
 *   | { __kind: 'pageview', url: string }
 *   | { __kind: 'click', x: number, y: number };
 * type ClickEvent = GetDataEnumKind<WebPageEvent, 'click'>;
 * // -> { __kind: 'click', x: number, y: number }
 * ```
 */
export type GetDataEnumKind<T extends DataEnum, K extends T['__kind']> = Extract<T, {
    __kind: K;
}>;
/**
 * Extracts a variant from a data enum without its discriminator.
 *
 * @example
 * ```ts
 * type WebPageEvent =
 *   | { __kind: 'pageview', url: string }
 *   | { __kind: 'click', x: number, y: number };
 * type ClickEvent = GetDataEnumKindContent<WebPageEvent, 'click'>;
 * // -> { x: number, y: number }
 * ```
 */
export type GetDataEnumKindContent<T extends DataEnum, K extends T['__kind']> = Omit<Extract<T, {
    __kind: K;
}>, '__kind'>;
/** Get the name and encoder of each variant in a data enum. */
export type DataEnumToEncoderTuple<TFrom extends DataEnum> = Array<TFrom extends never ? never : [
    TFrom['__kind'],
    keyof Omit<TFrom, '__kind'> extends never ? Encoder<Omit<TFrom, '__kind'>> | Encoder<void> : Encoder<Omit<TFrom, '__kind'>>
]>;
/** Get the name and decoder of each variant in a data enum. */
export type DataEnumToDecoderTuple<TTo extends DataEnum> = Array<TTo extends never ? never : [
    TTo['__kind'],
    keyof Omit<TTo, '__kind'> extends never ? Decoder<Omit<TTo, '__kind'>> | Decoder<void> : Decoder<Omit<TTo, '__kind'>>
]>;
/** Get the name and codec of each variant in a data enum. */
export type DataEnumToCodecTuple<TFrom extends DataEnum, TTo extends TFrom = TFrom> = Array<TFrom extends never ? never : [
    TFrom['__kind'],
    keyof Omit<TFrom, '__kind'> extends never ? Codec<Omit<TFrom, '__kind'>, Omit<TTo, '__kind'>> | Codec<void> : Codec<Omit<TFrom, '__kind'>, Omit<TTo, '__kind'>>
]>;
/** Defines the config for data enum codecs. */
export type DataEnumCodecConfig<TDiscriminator = NumberCodec | NumberEncoder | NumberDecoder> = {
    /**
     * The codec to use for the enum discriminator prefixing the variant.
     * @defaultValue u8 prefix.
     */
    size?: TDiscriminator;
};
/**
 * Creates a data enum encoder.
 *
 * @param variants - The variant encoders of the data enum.
 * @param config - A set of config for the encoder.
 */
export declare function getDataEnumEncoder<TFrom extends DataEnum>(variants: DataEnumToEncoderTuple<TFrom>, config?: DataEnumCodecConfig<NumberEncoder>): Encoder<TFrom>;
/**
 * Creates a data enum decoder.
 *
 * @param variants - The variant decoders of the data enum.
 * @param config - A set of config for the decoder.
 */
export declare function getDataEnumDecoder<T extends DataEnum>(variants: DataEnumToDecoderTuple<T>, config?: DataEnumCodecConfig<NumberDecoder>): Decoder<T>;
/**
 * Creates a data enum codec.
 *
 * @param variants - The variant codecs of the data enum.
 * @param config - A set of config for the codec.
 */
export declare function getDataEnumCodec<T extends DataEnum, U extends T = T>(variants: DataEnumToCodecTuple<T, U>, config?: DataEnumCodecConfig<NumberCodec>): Codec<T, U>;
//# sourceMappingURL=data-enum.d.ts.map