import { Codec, Decoder, Encoder, FixedSizeCodec, FixedSizeDecoder, FixedSizeEncoder, VariableSizeCodec, VariableSizeDecoder, VariableSizeEncoder } from '@solana/codecs-core';
import { FixedSizeNumberCodec, FixedSizeNumberDecoder, FixedSizeNumberEncoder, NumberCodec, NumberDecoder, NumberEncoder } from '@solana/codecs-numbers';
/** Defines the config for nullable codecs. */
export type NullableCodecConfig<TPrefix extends NumberCodec | NumberEncoder | NumberDecoder> = {
    /**
     * The codec to use for the boolean prefix.
     * @defaultValue u8 prefix.
     */
    prefix?: TPrefix;
    /**
     * Whether the item codec should be of fixed size.
     *
     * When this is true, a `null` value will skip the bytes that would
     * have been used for the item. Note that this will only work if the
     * item codec is of fixed size.
     * @defaultValue `false`
     */
    fixed?: boolean;
};
/**
 * Creates a encoder for an optional value using `null` as the `None` value.
 *
 * @param item - The encoder to use for the value that may be present.
 * @param config - A set of config for the encoder.
 */
export declare function getNullableEncoder<TFrom>(item: FixedSizeEncoder<TFrom>, config: NullableCodecConfig<FixedSizeNumberEncoder> & {
    fixed: true;
}): FixedSizeEncoder<TFrom | null>;
export declare function getNullableEncoder<TFrom>(item: FixedSizeEncoder<TFrom, 0>, config?: NullableCodecConfig<FixedSizeNumberEncoder>): FixedSizeEncoder<TFrom | null>;
export declare function getNullableEncoder<TFrom>(item: Encoder<TFrom>, config?: NullableCodecConfig<NumberEncoder> & {
    fixed?: false;
}): VariableSizeEncoder<TFrom | null>;
/**
 * Creates a decoder for an optional value using `null` as the `None` value.
 *
 * @param item - The decoder to use for the value that may be present.
 * @param config - A set of config for the decoder.
 */
export declare function getNullableDecoder<TTo>(item: FixedSizeDecoder<TTo>, config: NullableCodecConfig<FixedSizeNumberDecoder> & {
    fixed: true;
}): FixedSizeDecoder<TTo | null>;
export declare function getNullableDecoder<TTo>(item: FixedSizeDecoder<TTo, 0>, config?: NullableCodecConfig<FixedSizeNumberDecoder>): FixedSizeDecoder<TTo | null>;
export declare function getNullableDecoder<TTo>(item: Decoder<TTo>, config?: NullableCodecConfig<NumberDecoder> & {
    fixed?: false;
}): VariableSizeDecoder<TTo | null>;
/**
 * Creates a codec for an optional value using `null` as the `None` value.
 *
 * @param item - The codec to use for the value that may be present.
 * @param config - A set of config for the codec.
 */
export declare function getNullableCodec<TFrom, TTo extends TFrom = TFrom>(item: FixedSizeCodec<TFrom, TTo>, config: NullableCodecConfig<FixedSizeNumberCodec> & {
    fixed: true;
}): FixedSizeCodec<TFrom | null, TTo | null>;
export declare function getNullableCodec<TFrom, TTo extends TFrom = TFrom>(item: FixedSizeCodec<TFrom, TTo, 0>, config?: NullableCodecConfig<FixedSizeNumberCodec>): FixedSizeCodec<TFrom | null, TTo | null>;
export declare function getNullableCodec<TFrom, TTo extends TFrom = TFrom>(item: Codec<TFrom, TTo>, config?: NullableCodecConfig<NumberCodec> & {
    fixed?: false;
}): VariableSizeCodec<TFrom | null, TTo | null>;
//# sourceMappingURL=nullable.d.ts.map