{"version": 3, "file": "scalar-enum.d.ts", "sourceRoot": "", "sources": ["../../src/scalar-enum.ts"], "names": [], "mappings": "AAAA,OAAO,EAKH,cAAc,EACd,gBAAgB,EAChB,gBAAgB,EAGhB,iBAAiB,EACjB,mBAAmB,EACnB,mBAAmB,EACtB,MAAM,qBAAqB,CAAC;AAC7B,OAAO,EACH,oBAAoB,EACpB,sBAAsB,EACtB,sBAAsB,EAGtB,WAAW,EACX,aAAa,EACb,aAAa,EAChB,MAAM,wBAAwB,CAAC;AAEhC;;;;;;;;GAQG;AACH,MAAM,MAAM,UAAU,CAAC,CAAC,IAAI,CAAC;IAAE,CAAC,GAAG,EAAE,MAAM,GAAG,MAAM,GAAG,MAAM,GAAG,MAAM,GAAG,CAAC,CAAA;CAAE,GAAG,MAAM,GAAG,CAAC,CAAC,GAAG,WAAW,CAAC,OAAO,CAAC,CAAC;AAElH,iDAAiD;AACjD,MAAM,MAAM,qBAAqB,CAAC,cAAc,SAAS,WAAW,GAAG,aAAa,GAAG,aAAa,IAAI;IACpG;;;OAGG;IACH,IAAI,CAAC,EAAE,cAAc,CAAC;CACzB,CAAC;AAEF;;;;;GAKG;AACH,wBAAgB,oBAAoB,CAAC,KAAK,EAAE,gBAAgB,SAAS,UAAU,CAAC,KAAK,CAAC,EAClF,WAAW,EAAE,gBAAgB,GAC9B,gBAAgB,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;AAC9B,wBAAgB,oBAAoB,CAAC,KAAK,EAAE,gBAAgB,SAAS,UAAU,CAAC,KAAK,CAAC,EAAE,KAAK,SAAS,MAAM,EACxG,WAAW,EAAE,gBAAgB,EAC7B,MAAM,EAAE,qBAAqB,CAAC,aAAa,CAAC,GAAG;IAAE,IAAI,EAAE,sBAAsB,CAAC,KAAK,CAAC,CAAA;CAAE,GACvF,gBAAgB,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AAClC,wBAAgB,oBAAoB,CAAC,KAAK,EAAE,gBAAgB,SAAS,UAAU,CAAC,KAAK,CAAC,EAClF,WAAW,EAAE,gBAAgB,EAC7B,MAAM,CAAC,EAAE,qBAAqB,CAAC,aAAa,CAAC,GAC9C,mBAAmB,CAAC,KAAK,CAAC,CAAC;AA0B9B;;;;;GAKG;AACH,wBAAgB,oBAAoB,CAAC,GAAG,EAAE,cAAc,SAAS,UAAU,CAAC,GAAG,CAAC,EAC5E,WAAW,EAAE,cAAc,GAC5B,gBAAgB,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;AAC5B,wBAAgB,oBAAoB,CAAC,GAAG,EAAE,cAAc,SAAS,UAAU,CAAC,GAAG,CAAC,EAAE,KAAK,SAAS,MAAM,EAClG,WAAW,EAAE,cAAc,EAC3B,MAAM,EAAE,qBAAqB,CAAC,aAAa,CAAC,GAAG;IAAE,IAAI,EAAE,sBAAsB,CAAC,KAAK,CAAC,CAAA;CAAE,GACvF,gBAAgB,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;AAChC,wBAAgB,oBAAoB,CAAC,GAAG,EAAE,cAAc,SAAS,UAAU,CAAC,GAAG,CAAC,EAC5E,WAAW,EAAE,cAAc,EAC3B,MAAM,CAAC,EAAE,qBAAqB,CAAC,aAAa,CAAC,GAC9C,mBAAmB,CAAC,GAAG,CAAC,CAAC;AAoB5B;;;;;GAKG;AACH,wBAAgB,kBAAkB,CAAC,KAAK,EAAE,gBAAgB,SAAS,UAAU,CAAC,KAAK,CAAC,EAChF,WAAW,EAAE,gBAAgB,GAC9B,cAAc,CAAC,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;AACnC,wBAAgB,kBAAkB,CAAC,KAAK,EAAE,gBAAgB,SAAS,UAAU,CAAC,KAAK,CAAC,EAAE,KAAK,SAAS,MAAM,EACtG,WAAW,EAAE,gBAAgB,EAC7B,MAAM,EAAE,qBAAqB,CAAC,WAAW,CAAC,GAAG;IAAE,IAAI,EAAE,oBAAoB,CAAC,KAAK,CAAC,CAAA;CAAE,GACnF,cAAc,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;AACvC,wBAAgB,kBAAkB,CAAC,KAAK,EAAE,gBAAgB,SAAS,UAAU,CAAC,KAAK,CAAC,EAChF,WAAW,EAAE,gBAAgB,EAC7B,MAAM,CAAC,EAAE,qBAAqB,CAAC,WAAW,CAAC,GAC5C,iBAAiB,CAAC,KAAK,CAAC,CAAC"}