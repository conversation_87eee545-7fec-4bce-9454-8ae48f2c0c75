{"version": 3, "file": "tuple.d.ts", "sourceRoot": "", "sources": ["../../src/tuple.ts"], "names": [], "mappings": "AAAA,OAAO,EACH,KAAK,EAIL,OAAO,EACP,OAAO,EACP,cAAc,EACd,gBAAgB,EAChB,gBAAgB,EAEhB,iBAAiB,EACjB,mBAAmB,EACnB,mBAAmB,EACtB,MAAM,qBAAqB,CAAC;AAK7B,KAAK,sBAAsB,CAAC,KAAK,IAAI;KAChC,CAAC,IAAI,MAAM,KAAK,GAAG,gBAAgB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;CACjD,CAAC;AACF,KAAK,aAAa,CAAC,KAAK,IAAI;KACvB,CAAC,IAAI,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;CACxC,CAAC;AACF,KAAK,sBAAsB,CAAC,GAAG,IAAI;KAC9B,CAAC,IAAI,MAAM,GAAG,GAAG,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;CAC7C,CAAC;AACF,KAAK,aAAa,CAAC,GAAG,IAAI;KACrB,CAAC,IAAI,MAAM,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;CACpC,CAAC;AACF,KAAK,WAAW,CAAC,KAAK,EAAE,GAAG,SAAS,KAAK,IAAI;KACxC,CAAC,IAAI,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;CAC9C,CAAC;AACF,KAAK,oBAAoB,CAAC,KAAK,EAAE,GAAG,SAAS,KAAK,IAAI;KACjD,CAAC,IAAI,MAAM,KAAK,GAAG,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;CACvD,CAAC;AAGF,KAAK,QAAQ,GAAG,GAAG,EAAE,CAAC;AAEtB;;;;GAIG;AACH,wBAAgB,eAAe,CAAC,KAAK,SAAS,QAAQ,EAClD,KAAK,EAAE,sBAAsB,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,GAC1C,gBAAgB,CAAC,KAAK,CAAC,CAAC;AAC3B,wBAAgB,eAAe,CAAC,KAAK,SAAS,QAAQ,EAAE,KAAK,EAAE,aAAa,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,mBAAmB,CAAC,KAAK,CAAC,CAAC;AAuBtH;;;;GAIG;AACH,wBAAgB,eAAe,CAAC,GAAG,SAAS,QAAQ,EAAE,KAAK,EAAE,sBAAsB,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,gBAAgB,CAAC,GAAG,CAAC,CAAC;AACtH,wBAAgB,eAAe,CAAC,GAAG,SAAS,QAAQ,EAAE,KAAK,EAAE,aAAa,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,mBAAmB,CAAC,GAAG,CAAC,CAAC;AAmBhH;;;;GAIG;AACH,wBAAgB,aAAa,CAAC,KAAK,SAAS,QAAQ,EAAE,GAAG,SAAS,KAAK,GAAG,KAAK,EAC3E,KAAK,EAAE,oBAAoB,CAAC,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,GAClD,cAAc,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;AAC9B,wBAAgB,aAAa,CAAC,KAAK,SAAS,QAAQ,EAAE,GAAG,SAAS,KAAK,GAAG,KAAK,EAC3E,KAAK,EAAE,WAAW,CAAC,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,GACzC,iBAAiB,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC"}