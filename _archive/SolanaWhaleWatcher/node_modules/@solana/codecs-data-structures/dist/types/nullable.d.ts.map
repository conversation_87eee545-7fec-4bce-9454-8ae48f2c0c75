{"version": 3, "file": "nullable.d.ts", "sourceRoot": "", "sources": ["../../src/nullable.ts"], "names": [], "mappings": "AAAA,OAAO,EAEH,KAAK,EAIL,OAAO,EACP,OAAO,EACP,cAAc,EACd,gBAAgB,EAChB,gBAAgB,EAGhB,iBAAiB,EACjB,mBAAmB,EACnB,mBAAmB,EACtB,MAAM,qBAAqB,CAAC;AAC7B,OAAO,EACH,oBAAoB,EACpB,sBAAsB,EACtB,sBAAsB,EAGtB,WAAW,EACX,aAAa,EACb,aAAa,EAChB,MAAM,wBAAwB,CAAC;AAIhC,8CAA8C;AAC9C,MAAM,MAAM,mBAAmB,CAAC,OAAO,SAAS,WAAW,GAAG,aAAa,GAAG,aAAa,IAAI;IAC3F;;;OAGG;IACH,MAAM,CAAC,EAAE,OAAO,CAAC;IAEjB;;;;;;;OAOG;IACH,KAAK,CAAC,EAAE,OAAO,CAAC;CACnB,CAAC;AAEF;;;;;GAKG;AACH,wBAAgB,kBAAkB,CAAC,KAAK,EACpC,IAAI,EAAE,gBAAgB,CAAC,KAAK,CAAC,EAC7B,MAAM,EAAE,mBAAmB,CAAC,sBAAsB,CAAC,GAAG;IAAE,KAAK,EAAE,IAAI,CAAA;CAAE,GACtE,gBAAgB,CAAC,KAAK,GAAG,IAAI,CAAC,CAAC;AAClC,wBAAgB,kBAAkB,CAAC,KAAK,EACpC,IAAI,EAAE,gBAAgB,CAAC,KAAK,EAAE,CAAC,CAAC,EAChC,MAAM,CAAC,EAAE,mBAAmB,CAAC,sBAAsB,CAAC,GACrD,gBAAgB,CAAC,KAAK,GAAG,IAAI,CAAC,CAAC;AAClC,wBAAgB,kBAAkB,CAAC,KAAK,EACpC,IAAI,EAAE,OAAO,CAAC,KAAK,CAAC,EACpB,MAAM,CAAC,EAAE,mBAAmB,CAAC,aAAa,CAAC,GAAG;IAAE,KAAK,CAAC,EAAE,KAAK,CAAA;CAAE,GAChE,mBAAmB,CAAC,KAAK,GAAG,IAAI,CAAC,CAAC;AAuCrC;;;;;GAKG;AACH,wBAAgB,kBAAkB,CAAC,GAAG,EAClC,IAAI,EAAE,gBAAgB,CAAC,GAAG,CAAC,EAC3B,MAAM,EAAE,mBAAmB,CAAC,sBAAsB,CAAC,GAAG;IAAE,KAAK,EAAE,IAAI,CAAA;CAAE,GACtE,gBAAgB,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC;AAChC,wBAAgB,kBAAkB,CAAC,GAAG,EAClC,IAAI,EAAE,gBAAgB,CAAC,GAAG,EAAE,CAAC,CAAC,EAC9B,MAAM,CAAC,EAAE,mBAAmB,CAAC,sBAAsB,CAAC,GACrD,gBAAgB,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC;AAChC,wBAAgB,kBAAkB,CAAC,GAAG,EAClC,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,EAClB,MAAM,CAAC,EAAE,mBAAmB,CAAC,aAAa,CAAC,GAAG;IAAE,KAAK,CAAC,EAAE,KAAK,CAAA;CAAE,GAChE,mBAAmB,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC;AAkCnC;;;;;GAKG;AACH,wBAAgB,gBAAgB,CAAC,KAAK,EAAE,GAAG,SAAS,KAAK,GAAG,KAAK,EAC7D,IAAI,EAAE,cAAc,CAAC,KAAK,EAAE,GAAG,CAAC,EAChC,MAAM,EAAE,mBAAmB,CAAC,oBAAoB,CAAC,GAAG;IAAE,KAAK,EAAE,IAAI,CAAA;CAAE,GACpE,cAAc,CAAC,KAAK,GAAG,IAAI,EAAE,GAAG,GAAG,IAAI,CAAC,CAAC;AAC5C,wBAAgB,gBAAgB,CAAC,KAAK,EAAE,GAAG,SAAS,KAAK,GAAG,KAAK,EAC7D,IAAI,EAAE,cAAc,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC,EACnC,MAAM,CAAC,EAAE,mBAAmB,CAAC,oBAAoB,CAAC,GACnD,cAAc,CAAC,KAAK,GAAG,IAAI,EAAE,GAAG,GAAG,IAAI,CAAC,CAAC;AAC5C,wBAAgB,gBAAgB,CAAC,KAAK,EAAE,GAAG,SAAS,KAAK,GAAG,KAAK,EAC7D,IAAI,EAAE,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC,EACvB,MAAM,CAAC,EAAE,mBAAmB,CAAC,WAAW,CAAC,GAAG;IAAE,KAAK,CAAC,EAAE,KAAK,CAAA;CAAE,GAC9D,iBAAiB,CAAC,KAAK,GAAG,IAAI,EAAE,GAAG,GAAG,IAAI,CAAC,CAAC"}