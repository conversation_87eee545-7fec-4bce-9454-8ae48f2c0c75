import { FixedSizeCodec, FixedSizeDecoder, FixedSizeEncoder, VariableSizeCodec, VariableSizeDecoder, VariableSizeEncoder } from '@solana/codecs-core';
import { FixedSizeNumberCodec, FixedSizeNumberDecoder, FixedSizeNumberEncoder, NumberCodec, NumberDecoder, NumberEncoder } from '@solana/codecs-numbers';
/**
 * Defines a scalar enum as a type from its constructor.
 *
 * @example
 * ```ts
 * enum Direction { Left, Right };
 * type DirectionType = ScalarEnum<Direction>;
 * ```
 */
export type ScalarEnum<T> = ({
    [key: number | string]: string | number | T;
} | number | T) & NonNullable<unknown>;
/** Defines the config for scalar enum codecs. */
export type ScalarEnumCodecConfig<TDiscriminator extends NumberCodec | NumberEncoder | NumberDecoder> = {
    /**
     * The codec to use for the enum discriminator.
     * @defaultValue u8 discriminator.
     */
    size?: TDiscriminator;
};
/**
 * Creates a scalar enum encoder.
 *
 * @param constructor - The constructor of the scalar enum.
 * @param config - A set of config for the encoder.
 */
export declare function getScalarEnumEncoder<TFrom, TFromConstructor extends ScalarEnum<TFrom>>(constructor: TFromConstructor): FixedSizeEncoder<TFrom, 1>;
export declare function getScalarEnumEncoder<TFrom, TFromConstructor extends ScalarEnum<TFrom>, TSize extends number>(constructor: TFromConstructor, config: ScalarEnumCodecConfig<NumberEncoder> & {
    size: FixedSizeNumberEncoder<TSize>;
}): FixedSizeEncoder<TFrom, TSize>;
export declare function getScalarEnumEncoder<TFrom, TFromConstructor extends ScalarEnum<TFrom>>(constructor: TFromConstructor, config?: ScalarEnumCodecConfig<NumberEncoder>): VariableSizeEncoder<TFrom>;
/**
 * Creates a scalar enum decoder.
 *
 * @param constructor - The constructor of the scalar enum.
 * @param config - A set of config for the decoder.
 */
export declare function getScalarEnumDecoder<TTo, TToConstructor extends ScalarEnum<TTo>>(constructor: TToConstructor): FixedSizeDecoder<TTo, 1>;
export declare function getScalarEnumDecoder<TTo, TToConstructor extends ScalarEnum<TTo>, TSize extends number>(constructor: TToConstructor, config: ScalarEnumCodecConfig<NumberDecoder> & {
    size: FixedSizeNumberDecoder<TSize>;
}): FixedSizeDecoder<TTo, TSize>;
export declare function getScalarEnumDecoder<TTo, TToConstructor extends ScalarEnum<TTo>>(constructor: TToConstructor, config?: ScalarEnumCodecConfig<NumberDecoder>): VariableSizeDecoder<TTo>;
/**
 * Creates a scalar enum codec.
 *
 * @param constructor - The constructor of the scalar enum.
 * @param config - A set of config for the codec.
 */
export declare function getScalarEnumCodec<TFrom, TFromConstructor extends ScalarEnum<TFrom>>(constructor: TFromConstructor): FixedSizeCodec<TFrom, TFrom, 1>;
export declare function getScalarEnumCodec<TFrom, TFromConstructor extends ScalarEnum<TFrom>, TSize extends number>(constructor: TFromConstructor, config: ScalarEnumCodecConfig<NumberCodec> & {
    size: FixedSizeNumberCodec<TSize>;
}): FixedSizeCodec<TFrom, TFrom, TSize>;
export declare function getScalarEnumCodec<TFrom, TFromConstructor extends ScalarEnum<TFrom>>(constructor: TFromConstructor, config?: ScalarEnumCodecConfig<NumberCodec>): VariableSizeCodec<TFrom>;
//# sourceMappingURL=scalar-enum.d.ts.map