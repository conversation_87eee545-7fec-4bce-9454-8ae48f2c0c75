import { FixedSizeCodec, FixedSizeDecoder, FixedSizeEncoder, VariableSizeCodec, VariableSizeDecoder, VariableSizeEncoder } from '@solana/codecs-core';
import { NumberCodec, NumberDecoder, NumberEncoder } from '@solana/codecs-numbers';
/** Defines the config for bytes codecs. */
export type BytesCodecConfig<TSize extends NumberCodec | NumberEncoder | NumberDecoder> = {
    /**
     * The size of the byte array. It can be one of the following:
     * - a {@link NumberSerializer} that prefixes the byte array with its size.
     * - a fixed number of bytes.
     * - or `'variable'` to use the rest of the byte array.
     * @defaultValue `'variable'`
     */
    size?: TSize | number | 'variable';
};
/**
 * Encodes sized bytes.
 *
 * @param config - A set of config for the encoder.
 */
export declare function getBytesEncoder<TSize extends number>(config: BytesCodecConfig<NumberEncoder> & {
    size: TSize;
}): FixedSizeEncoder<Uint8Array, TSize>;
export declare function getBytesEncoder(config?: BytesCodecConfig<NumberEncoder>): VariableSizeEncoder<Uint8Array>;
/**
 * Decodes sized bytes.
 *
 * @param config - A set of config for the decoder.
 */
export declare function getBytesDecoder<TSize extends number>(config: BytesCodecConfig<NumberDecoder> & {
    size: TSize;
}): FixedSizeDecoder<Uint8Array, TSize>;
export declare function getBytesDecoder(config?: BytesCodecConfig<NumberDecoder>): VariableSizeDecoder<Uint8Array>;
/**
 * Creates a sized bytes codec.
 *
 * @param config - A set of config for the codec.
 */
export declare function getBytesCodec<TSize extends number>(config: BytesCodecConfig<NumberCodec> & {
    size: TSize;
}): FixedSizeCodec<Uint8Array, Uint8Array, TSize>;
export declare function getBytesCodec(config?: BytesCodecConfig<NumberCodec>): VariableSizeCodec<Uint8Array>;
//# sourceMappingURL=bytes.d.ts.map