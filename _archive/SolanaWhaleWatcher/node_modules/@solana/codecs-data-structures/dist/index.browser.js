import { assertIsFixedSize, createEncoder, getEncodedSize, createDecoder, combineCodec, assertByteArrayHasEnoughBytesForCodec, mapEncoder, mapDecoder, fixEncoder, fixDecoder, assertByteArrayIsNotEmptyForCodec, isFixedSize } from '@solana/codecs-core';
import { getU32Encoder, getU32Decoder, getU8Encoder, getU8Decoder } from '@solana/codecs-numbers';

// src/array.ts

// src/assertions.ts
function assertValidNumberOfItemsForCodec(codecDescription, expected, actual) {
  if (expected !== actual) {
    throw new Error(`Expected [${codecDescription}] to have ${expected} items, got ${actual}.`);
  }
}
function maxCodecSizes(sizes) {
  return sizes.reduce(
    (all, size) => all === null || size === null ? null : Math.max(all, size),
    0
  );
}
function sumCodecSizes(sizes) {
  return sizes.reduce((all, size) => all === null || size === null ? null : all + size, 0);
}
function getFixedSize(codec) {
  return isFixedSize(codec) ? codec.fixedSize : null;
}
function getMaxSize(codec) {
  return isFixedSize(codec) ? codec.fixedSize : codec.maxSize ?? null;
}

// src/array.ts
function getArrayEncoder(item, config = {}) {
  const size = config.size ?? getU32Encoder();
  if (size === "remainder") {
    assertIsFixedSize(item, 'Codecs of "remainder" size must have fixed-size items.');
  }
  const fixedSize = computeArrayLikeCodecSize(size, getFixedSize(item));
  const maxSize = computeArrayLikeCodecSize(size, getMaxSize(item)) ?? void 0;
  return createEncoder({
    ...fixedSize !== null ? { fixedSize } : {
      getSizeFromValue: (array) => {
        const prefixSize = typeof size === "object" ? getEncodedSize(array.length, size) : 0;
        return prefixSize + [...array].reduce((all, value) => all + getEncodedSize(value, item), 0);
      },
      maxSize
    },
    write: (array, bytes, offset) => {
      if (typeof size === "number") {
        assertValidNumberOfItemsForCodec("array", size, array.length);
      }
      if (typeof size === "object") {
        offset = size.write(array.length, bytes, offset);
      }
      array.forEach((value) => {
        offset = item.write(value, bytes, offset);
      });
      return offset;
    }
  });
}
function getArrayDecoder(item, config = {}) {
  const size = config.size ?? getU32Decoder();
  if (size === "remainder") {
    assertIsFixedSize(item, 'Codecs of "remainder" size must have fixed-size items.');
  }
  const itemSize = getFixedSize(item);
  const fixedSize = computeArrayLikeCodecSize(size, itemSize);
  const maxSize = computeArrayLikeCodecSize(size, getMaxSize(item)) ?? void 0;
  return createDecoder({
    ...fixedSize !== null ? { fixedSize } : { maxSize },
    read: (bytes, offset) => {
      const array = [];
      if (typeof size === "object" && bytes.slice(offset).length === 0) {
        return [array, offset];
      }
      const [resolvedSize, newOffset] = readArrayLikeCodecSize(size, itemSize, bytes, offset);
      offset = newOffset;
      for (let i = 0; i < resolvedSize; i += 1) {
        const [value, newOffset2] = item.read(bytes, offset);
        offset = newOffset2;
        array.push(value);
      }
      return [array, offset];
    }
  });
}
function getArrayCodec(item, config = {}) {
  return combineCodec(getArrayEncoder(item, config), getArrayDecoder(item, config));
}
function readArrayLikeCodecSize(size, itemSize, bytes, offset) {
  if (typeof size === "number") {
    return [size, offset];
  }
  if (typeof size === "object") {
    return size.read(bytes, offset);
  }
  if (size === "remainder") {
    if (itemSize === null) {
      throw new Error('Codecs of "remainder" size must have fixed-size items.');
    }
    const remainder = Math.max(0, bytes.length - offset);
    if (remainder % itemSize !== 0) {
      throw new Error(
        `The remainder of the byte array (${remainder} bytes) cannot be split into chunks of ${itemSize} bytes. Codecs of "remainder" size must have a remainder that is a multiple of its item size. In other words, ${remainder} modulo ${itemSize} should be equal to zero.`
      );
    }
    return [remainder / itemSize, offset];
  }
  throw new Error(`Unrecognized array-like codec size: ${JSON.stringify(size)}`);
}
function computeArrayLikeCodecSize(size, itemSize) {
  if (typeof size !== "number")
    return null;
  if (size === 0)
    return 0;
  return itemSize === null ? null : itemSize * size;
}
function getBitArrayEncoder(size, config = {}) {
  const parsedConfig = typeof config === "boolean" ? { backward: config } : config;
  const backward = parsedConfig.backward ?? false;
  return createEncoder({
    fixedSize: size,
    write(value, bytes, offset) {
      const bytesToAdd = [];
      for (let i = 0; i < size; i += 1) {
        let byte = 0;
        for (let j = 0; j < 8; j += 1) {
          const feature = Number(value[i * 8 + j] ?? 0);
          byte |= feature << (backward ? j : 7 - j);
        }
        if (backward) {
          bytesToAdd.unshift(byte);
        } else {
          bytesToAdd.push(byte);
        }
      }
      bytes.set(bytesToAdd, offset);
      return size;
    }
  });
}
function getBitArrayDecoder(size, config = {}) {
  const parsedConfig = typeof config === "boolean" ? { backward: config } : config;
  const backward = parsedConfig.backward ?? false;
  return createDecoder({
    fixedSize: size,
    read(bytes, offset) {
      assertByteArrayHasEnoughBytesForCodec("bitArray", size, bytes, offset);
      const booleans = [];
      let slice = bytes.slice(offset, offset + size);
      slice = backward ? slice.reverse() : slice;
      slice.forEach((byte) => {
        for (let i = 0; i < 8; i += 1) {
          if (backward) {
            booleans.push(Boolean(byte & 1));
            byte >>= 1;
          } else {
            booleans.push(Boolean(byte & 128));
            byte <<= 1;
          }
        }
      });
      return [booleans, offset + size];
    }
  });
}
function getBitArrayCodec(size, config = {}) {
  return combineCodec(getBitArrayEncoder(size, config), getBitArrayDecoder(size, config));
}
function getBooleanEncoder(config = {}) {
  const size = config.size ?? getU8Encoder();
  assertIsFixedSize(size, "Codec [bool] requires a fixed size.");
  return mapEncoder(size, (value) => value ? 1 : 0);
}
function getBooleanDecoder(config = {}) {
  const size = config.size ?? getU8Decoder();
  assertIsFixedSize(size, "Codec [bool] requires a fixed size.");
  return mapDecoder(size, (value) => Number(value) === 1);
}
function getBooleanCodec(config = {}) {
  return combineCodec(getBooleanEncoder(config), getBooleanDecoder(config));
}
function getBytesEncoder(config = {}) {
  const size = config.size ?? "variable";
  const byteEncoder = createEncoder({
    getSizeFromValue: (value) => value.length,
    write: (value, bytes, offset) => {
      bytes.set(value, offset);
      return offset + value.length;
    }
  });
  if (size === "variable") {
    return byteEncoder;
  }
  if (typeof size === "number") {
    return fixEncoder(byteEncoder, size);
  }
  return createEncoder({
    getSizeFromValue: (value) => getEncodedSize(value.length, size) + value.length,
    write: (value, bytes, offset) => {
      offset = size.write(value.length, bytes, offset);
      return byteEncoder.write(value, bytes, offset);
    }
  });
}
function getBytesDecoder(config = {}) {
  const size = config.size ?? "variable";
  const byteDecoder = createDecoder({
    read: (bytes, offset) => {
      const slice = bytes.slice(offset);
      return [slice, offset + slice.length];
    }
  });
  if (size === "variable") {
    return byteDecoder;
  }
  if (typeof size === "number") {
    return fixDecoder(byteDecoder, size);
  }
  return createDecoder({
    read: (bytes, offset) => {
      assertByteArrayIsNotEmptyForCodec("bytes", bytes, offset);
      const [lengthBigInt, lengthOffset] = size.read(bytes, offset);
      const length = Number(lengthBigInt);
      offset = lengthOffset;
      const contentBytes = bytes.slice(offset, offset + length);
      assertByteArrayHasEnoughBytesForCodec("bytes", length, contentBytes);
      const [value, contentOffset] = byteDecoder.read(contentBytes, 0);
      offset += contentOffset;
      return [value, offset];
    }
  });
}
function getBytesCodec(config = {}) {
  return combineCodec(getBytesEncoder(config), getBytesDecoder(config));
}
function getDataEnumEncoder(variants, config = {}) {
  const prefix = config.size ?? getU8Encoder();
  const fixedSize = getDataEnumFixedSize(variants, prefix);
  return createEncoder({
    ...fixedSize !== null ? { fixedSize } : {
      getSizeFromValue: (variant) => {
        const discriminator = getVariantDiscriminator(variants, variant);
        const variantEncoder = variants[discriminator][1];
        return getEncodedSize(discriminator, prefix) + getEncodedSize(variant, variantEncoder);
      },
      maxSize: getDataEnumMaxSize(variants, prefix)
    },
    write: (variant, bytes, offset) => {
      const discriminator = getVariantDiscriminator(variants, variant);
      offset = prefix.write(discriminator, bytes, offset);
      const variantEncoder = variants[discriminator][1];
      return variantEncoder.write(variant, bytes, offset);
    }
  });
}
function getDataEnumDecoder(variants, config = {}) {
  const prefix = config.size ?? getU8Decoder();
  const fixedSize = getDataEnumFixedSize(variants, prefix);
  return createDecoder({
    ...fixedSize !== null ? { fixedSize } : { maxSize: getDataEnumMaxSize(variants, prefix) },
    read: (bytes, offset) => {
      assertByteArrayIsNotEmptyForCodec("dataEnum", bytes, offset);
      const [discriminator, dOffset] = prefix.read(bytes, offset);
      offset = dOffset;
      const variantField = variants[Number(discriminator)] ?? null;
      if (!variantField) {
        throw new Error(
          `Enum discriminator out of range. Expected a number between 0 and ${variants.length - 1}, got ${discriminator}.`
        );
      }
      const [variant, vOffset] = variantField[1].read(bytes, offset);
      offset = vOffset;
      return [{ __kind: variantField[0], ...variant ?? {} }, offset];
    }
  });
}
function getDataEnumCodec(variants, config = {}) {
  return combineCodec(getDataEnumEncoder(variants, config), getDataEnumDecoder(variants, config));
}
function getDataEnumFixedSize(variants, prefix) {
  if (variants.length === 0)
    return isFixedSize(prefix) ? prefix.fixedSize : null;
  if (!isFixedSize(variants[0][1]))
    return null;
  const variantSize = variants[0][1].fixedSize;
  const sameSizedVariants = variants.every(
    (variant) => isFixedSize(variant[1]) && variant[1].fixedSize === variantSize
  );
  if (!sameSizedVariants)
    return null;
  return isFixedSize(prefix) ? prefix.fixedSize + variantSize : null;
}
function getDataEnumMaxSize(variants, prefix) {
  const maxVariantSize = maxCodecSizes(variants.map(([, codec]) => getMaxSize(codec)));
  return sumCodecSizes([getMaxSize(prefix), maxVariantSize]) ?? void 0;
}
function getVariantDiscriminator(variants, variant) {
  const discriminator = variants.findIndex(([key]) => variant.__kind === key);
  if (discriminator < 0) {
    throw new Error(
      `Invalid data enum variant. Expected one of [${variants.map(([key]) => key).join(", ")}], got "${variant.__kind}".`
    );
  }
  return discriminator;
}
function getTupleEncoder(items) {
  const fixedSize = sumCodecSizes(items.map(getFixedSize));
  const maxSize = sumCodecSizes(items.map(getMaxSize)) ?? void 0;
  return createEncoder({
    ...fixedSize === null ? {
      getSizeFromValue: (value) => items.map((item, index) => getEncodedSize(value[index], item)).reduce((all, one) => all + one, 0),
      maxSize
    } : { fixedSize },
    write: (value, bytes, offset) => {
      assertValidNumberOfItemsForCodec("tuple", items.length, value.length);
      items.forEach((item, index) => {
        offset = item.write(value[index], bytes, offset);
      });
      return offset;
    }
  });
}
function getTupleDecoder(items) {
  const fixedSize = sumCodecSizes(items.map(getFixedSize));
  const maxSize = sumCodecSizes(items.map(getMaxSize)) ?? void 0;
  return createDecoder({
    ...fixedSize === null ? { maxSize } : { fixedSize },
    read: (bytes, offset) => {
      const values = [];
      items.forEach((item) => {
        const [newValue, newOffset] = item.read(bytes, offset);
        values.push(newValue);
        offset = newOffset;
      });
      return [values, offset];
    }
  });
}
function getTupleCodec(items) {
  return combineCodec(
    getTupleEncoder(items),
    getTupleDecoder(items)
  );
}

// src/map.ts
function getMapEncoder(key, value, config = {}) {
  return mapEncoder(
    getArrayEncoder(getTupleEncoder([key, value]), config),
    (map) => [...map.entries()]
  );
}
function getMapDecoder(key, value, config = {}) {
  return mapDecoder(
    getArrayDecoder(getTupleDecoder([key, value]), config),
    (entries) => new Map(entries)
  );
}
function getMapCodec(key, value, config = {}) {
  return combineCodec(getMapEncoder(key, value, config), getMapDecoder(key, value, config));
}
function getNullableEncoder(item, config = {}) {
  const prefix = config.prefix ?? getU8Encoder();
  const fixed = config.fixed ?? false;
  const isZeroSizeItem = isFixedSize(item) && isFixedSize(prefix) && item.fixedSize === 0;
  if (fixed || isZeroSizeItem) {
    assertIsFixedSize(item, "Fixed nullables can only be used with fixed-size codecs.");
    assertIsFixedSize(prefix, "Fixed nullables can only be used with fixed-size prefix.");
    const fixedSize = prefix.fixedSize + item.fixedSize;
    return createEncoder({
      fixedSize,
      write: (option, bytes, offset) => {
        const prefixOffset = prefix.write(Number(option !== null), bytes, offset);
        if (option !== null) {
          item.write(option, bytes, prefixOffset);
        }
        return offset + fixedSize;
      }
    });
  }
  return createEncoder({
    getSizeFromValue: (option) => getEncodedSize(Number(option !== null), prefix) + (option !== null ? getEncodedSize(option, item) : 0),
    maxSize: sumCodecSizes([prefix, item].map(getMaxSize)) ?? void 0,
    write: (option, bytes, offset) => {
      offset = prefix.write(Number(option !== null), bytes, offset);
      if (option !== null) {
        offset = item.write(option, bytes, offset);
      }
      return offset;
    }
  });
}
function getNullableDecoder(item, config = {}) {
  const prefix = config.prefix ?? getU8Decoder();
  const fixed = config.fixed ?? false;
  let fixedSize = null;
  const isZeroSizeItem = isFixedSize(item) && isFixedSize(prefix) && item.fixedSize === 0;
  if (fixed || isZeroSizeItem) {
    assertIsFixedSize(item, "Fixed nullables can only be used with fixed-size codecs.");
    assertIsFixedSize(prefix, "Fixed nullables can only be used with fixed-size prefix.");
    fixedSize = prefix.fixedSize + item.fixedSize;
  }
  return createDecoder({
    ...fixedSize === null ? { maxSize: sumCodecSizes([prefix, item].map(getMaxSize)) ?? void 0 } : { fixedSize },
    read: (bytes, offset) => {
      if (bytes.length - offset <= 0) {
        return [null, offset];
      }
      const [isSome, prefixOffset] = prefix.read(bytes, offset);
      if (isSome === 0) {
        return [null, fixedSize !== null ? offset + fixedSize : prefixOffset];
      }
      const [value, newOffset] = item.read(bytes, prefixOffset);
      return [value, fixedSize !== null ? offset + fixedSize : newOffset];
    }
  });
}
function getNullableCodec(item, config = {}) {
  const configCast = config;
  return combineCodec(getNullableEncoder(item, configCast), getNullableDecoder(item, configCast));
}
function getScalarEnumEncoder(constructor, config = {}) {
  const prefix = config.size ?? getU8Encoder();
  const { minRange, maxRange, stringValues, enumKeys, enumValues } = getScalarEnumStats(constructor);
  return mapEncoder(prefix, (value) => {
    const isInvalidNumber = typeof value === "number" && (value < minRange || value > maxRange);
    const isInvalidString = typeof value === "string" && !stringValues.includes(value);
    if (isInvalidNumber || isInvalidString) {
      throw new Error(
        `Invalid scalar enum variant. Expected one of [${stringValues.join(", ")}] or a number between ${minRange} and ${maxRange}, got "${value}".`
      );
    }
    if (typeof value === "number")
      return value;
    const valueIndex = enumValues.indexOf(value);
    if (valueIndex >= 0)
      return valueIndex;
    return enumKeys.indexOf(value);
  });
}
function getScalarEnumDecoder(constructor, config = {}) {
  const prefix = config.size ?? getU8Decoder();
  const { minRange, maxRange, isNumericEnum, enumValues } = getScalarEnumStats(constructor);
  return mapDecoder(prefix, (value) => {
    const valueAsNumber = Number(value);
    if (valueAsNumber < minRange || valueAsNumber > maxRange) {
      throw new Error(
        `Enum discriminator out of range. Expected a number between ${minRange} and ${maxRange}, got ${valueAsNumber}.`
      );
    }
    return isNumericEnum ? valueAsNumber : enumValues[valueAsNumber];
  });
}
function getScalarEnumCodec(constructor, config = {}) {
  return combineCodec(getScalarEnumEncoder(constructor, config), getScalarEnumDecoder(constructor, config));
}
function getScalarEnumStats(constructor) {
  const enumKeys = Object.keys(constructor);
  const enumValues = Object.values(constructor);
  const isNumericEnum = enumValues.some((v) => typeof v === "number");
  const minRange = 0;
  const maxRange = isNumericEnum ? enumValues.length / 2 - 1 : enumValues.length - 1;
  const stringValues = isNumericEnum ? [...enumKeys] : [.../* @__PURE__ */ new Set([...enumKeys, ...enumValues])];
  return {
    enumKeys,
    enumValues,
    isNumericEnum,
    maxRange,
    minRange,
    stringValues
  };
}
function getSetEncoder(item, config = {}) {
  return mapEncoder(getArrayEncoder(item, config), (set) => [...set]);
}
function getSetDecoder(item, config = {}) {
  return mapDecoder(getArrayDecoder(item, config), (entries) => new Set(entries));
}
function getSetCodec(item, config = {}) {
  return combineCodec(getSetEncoder(item, config), getSetDecoder(item, config));
}
function getStructEncoder(fields) {
  const fieldCodecs = fields.map(([, codec]) => codec);
  const fixedSize = sumCodecSizes(fieldCodecs.map(getFixedSize));
  const maxSize = sumCodecSizes(fieldCodecs.map(getMaxSize)) ?? void 0;
  return createEncoder({
    ...fixedSize === null ? {
      getSizeFromValue: (value) => fields.map(([key, codec]) => getEncodedSize(value[key], codec)).reduce((all, one) => all + one, 0),
      maxSize
    } : { fixedSize },
    write: (struct, bytes, offset) => {
      fields.forEach(([key, codec]) => {
        offset = codec.write(struct[key], bytes, offset);
      });
      return offset;
    }
  });
}
function getStructDecoder(fields) {
  const fieldCodecs = fields.map(([, codec]) => codec);
  const fixedSize = sumCodecSizes(fieldCodecs.map(getFixedSize));
  const maxSize = sumCodecSizes(fieldCodecs.map(getMaxSize)) ?? void 0;
  return createDecoder({
    ...fixedSize === null ? { maxSize } : { fixedSize },
    read: (bytes, offset) => {
      const struct = {};
      fields.forEach(([key, codec]) => {
        const [value, newOffset] = codec.read(bytes, offset);
        offset = newOffset;
        struct[key] = value;
      });
      return [struct, offset];
    }
  });
}
function getStructCodec(fields) {
  return combineCodec(getStructEncoder(fields), getStructDecoder(fields));
}
function getUnitEncoder() {
  return createEncoder({
    fixedSize: 0,
    write: (_value, _bytes, offset) => offset
  });
}
function getUnitDecoder() {
  return createDecoder({
    fixedSize: 0,
    read: (_bytes, offset) => [void 0, offset]
  });
}
function getUnitCodec() {
  return combineCodec(getUnitEncoder(), getUnitDecoder());
}

export { assertValidNumberOfItemsForCodec, getArrayCodec, getArrayDecoder, getArrayEncoder, getBitArrayCodec, getBitArrayDecoder, getBitArrayEncoder, getBooleanCodec, getBooleanDecoder, getBooleanEncoder, getBytesCodec, getBytesDecoder, getBytesEncoder, getDataEnumCodec, getDataEnumDecoder, getDataEnumEncoder, getMapCodec, getMapDecoder, getMapEncoder, getNullableCodec, getNullableDecoder, getNullableEncoder, getScalarEnumCodec, getScalarEnumDecoder, getScalarEnumEncoder, getSetCodec, getSetDecoder, getSetEncoder, getStructCodec, getStructDecoder, getStructEncoder, getTupleCodec, getTupleDecoder, getTupleEncoder, getUnitCodec, getUnitDecoder, getUnitEncoder };
