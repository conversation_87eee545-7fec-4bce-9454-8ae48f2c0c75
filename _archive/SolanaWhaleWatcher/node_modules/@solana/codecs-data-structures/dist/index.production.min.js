this.globalThis = this.globalThis || {};
this.globalThis.solanaWeb3 = (function (exports) {
	'use strict';

	function h(e,o,r=0){if(o.length-r<=0)throw new Error(`Codec [${e}] cannot decode empty byte arrays.`)}function V(e,o,r,n=0){let t=r.length-n;if(t<o)throw new Error(`Codec [${e}] expected ${o} bytes, got ${t}.`)}var J=(e,o)=>{if(e.length>=o)return e;let r=new Uint8Array(o).fill(0);return r.set(e),r},Q=(e,o)=>J(e.length<=o?e:e.slice(0,o),o);function S(e,o){return "fixedSize"in o?o.fixedSize:o.getSizeFromValue(e)}function l(e){return Object.freeze({...e,encode:o=>{let r=new Uint8Array(S(o,e));return e.write(o,r,0),r}})}function x(e){return Object.freeze({...e,decode:(o,r=0)=>e.read(o,r)[0]})}function f(e){return "fixedSize"in e&&typeof e.fixedSize=="number"}function b(e,o){if(!f(e))throw new Error(o!=null?o:"Expected a fixed-size codec, got a variable-size one.")}function X(e){return !f(e)}function m(e,o){if(f(e)!==f(o))throw new Error("Encoder and decoder must either both be fixed-size or variable-size.");if(f(e)&&f(o)&&e.fixedSize!==o.fixedSize)throw new Error(`Encoder and decoder must have the same fixed size, got [${e.fixedSize}] and [${o.fixedSize}].`);if(!f(e)&&!f(o)&&e.maxSize!==o.maxSize)throw new Error(`Encoder and decoder must have the same max size, got [${e.maxSize}] and [${o.maxSize}].`);return {...o,...e,decode:o.decode,encode:e.encode,read:o.read,write:e.write}}function M(e,o){return l({fixedSize:o,write:(r,n,t)=>{let i=e.encode(r),c=i.length>o?i.slice(0,o):i;return n.set(c,t),t+o}})}function _(e,o){return x({fixedSize:o,read:(r,n)=>{V("fixCodec",o,r,n),(n>0||r.length>o)&&(r=r.slice(n,n+o)),f(e)&&(r=Q(r,e.fixedSize));let[t]=e.read(r,0);return [t,n+o]}})}function E(e,o){return l({...X(e)?{...e,getSizeFromValue:r=>e.getSizeFromValue(o(r))}:e,write:(r,n,t)=>e.write(o(r),n,t)})}function y(e,o){return x({...e,read:(r,n)=>{let[t,i]=e.read(r,n);return [o(t,r,n),i]}})}function Y(e,o,r,n){if(n<o||n>r)throw new Error(`Codec [${e}] expected number to be in the range [${o}, ${r}], got ${n}.`)}function O(e){return (e==null?void 0:e.endian)!==1}function j(e){return l({fixedSize:e.size,write(o,r,n){e.range&&Y(e.name,e.range[0],e.range[1],o);let t=new ArrayBuffer(e.size);return e.set(new DataView(t),o,O(e.config)),r.set(new Uint8Array(t),n),n+e.size}})}function $(e){return x({fixedSize:e.size,read(o,r=0){h(e.name,o,r),V(e.name,e.size,o,r);let n=new DataView(ee(o,r,e.size));return [e.get(n,O(e.config)),r+e.size]}})}function ee(e,o,r){let n=e.byteOffset+(o!=null?o:0),t=r!=null?r:e.byteLength;return e.buffer.slice(n,n+t)}var P=(e={})=>j({config:e,name:"u32",range:[0,+"0xffffffff"],set:(o,r,n)=>o.setUint32(0,r,n),size:4}),L=(e={})=>$({config:e,get:(o,r)=>o.getUint32(0,r),name:"u32",size:4});var D=()=>j({name:"u8",range:[0,+"0xff"],set:(e,o)=>e.setUint8(0,o),size:1}),N=()=>$({get:e=>e.getUint8(0),name:"u8",size:1});function B(e,o,r){if(o!==r)throw new Error(`Expected [${e}] to have ${o} items, got ${r}.`)}function W(e){return e.reduce((o,r)=>o===null||r===null?null:Math.max(o,r),0)}function g(e){return e.reduce((o,r)=>o===null||r===null?null:o+r,0)}function p(e){return f(e)?e.fixedSize:null}function z(e){var o;return f(e)?e.fixedSize:(o=e.maxSize)!=null?o:null}function K(e,o={}){var i,c;let r=(i=o.size)!=null?i:P();r==="remainder"&&b(e,'Codecs of "remainder" size must have fixed-size items.');let n=v(r,p(e)),t=(c=v(r,z(e)))!=null?c:void 0;return l({...n!==null?{fixedSize:n}:{getSizeFromValue:a=>(typeof r=="object"?S(a.length,r):0)+[...a].reduce((d,u)=>d+S(u,e),0),maxSize:t},write:(a,T,d)=>(typeof r=="number"&&B("array",r,a.length),typeof r=="object"&&(d=r.write(a.length,T,d)),a.forEach(u=>{d=e.write(u,T,d);}),d)})}function w(e,o={}){var c,a;let r=(c=o.size)!=null?c:L();r==="remainder"&&b(e,'Codecs of "remainder" size must have fixed-size items.');let n=p(e),t=v(r,n),i=(a=v(r,z(e)))!=null?a:void 0;return x({...t!==null?{fixedSize:t}:{maxSize:i},read:(T,d)=>{let u=[];if(typeof r=="object"&&T.slice(d).length===0)return [u,d];let[s,F]=oe(r,n,T,d);d=F;for(let A=0;A<s;A+=1){let[U,Z]=e.read(T,d);d=Z,u.push(U);}return [u,d]}})}function Je(e,o={}){return m(K(e,o),w(e,o))}function oe(e,o,r,n){if(typeof e=="number")return [e,n];if(typeof e=="object")return e.read(r,n);if(e==="remainder"){if(o===null)throw new Error('Codecs of "remainder" size must have fixed-size items.');let t=Math.max(0,r.length-n);if(t%o!==0)throw new Error(`The remainder of the byte array (${t} bytes) cannot be split into chunks of ${o} bytes. Codecs of "remainder" size must have a remainder that is a multiple of its item size. In other words, ${t} modulo ${o} should be equal to zero.`);return [t/o,n]}throw new Error(`Unrecognized array-like codec size: ${JSON.stringify(e)}`)}function v(e,o){return typeof e!="number"?null:e===0?0:o===null?null:o*e}function re(e,o={}){var t;let n=(t=(typeof o=="boolean"?{backward:o}:o).backward)!=null?t:!1;return l({fixedSize:e,write(i,c,a){var d;let T=[];for(let u=0;u<e;u+=1){let s=0;for(let F=0;F<8;F+=1){let A=Number((d=i[u*8+F])!=null?d:0);s|=A<<(n?F:7-F);}n?T.unshift(s):T.push(s);}return c.set(T,a),e}})}function ne(e,o={}){var t;let n=(t=(typeof o=="boolean"?{backward:o}:o).backward)!=null?t:!1;return x({fixedSize:e,read(i,c){V("bitArray",e,i,c);let a=[],T=i.slice(c,c+e);return T=n?T.reverse():T,T.forEach(d=>{for(let u=0;u<8;u+=1)n?(a.push(!!(d&1)),d>>=1):(a.push(!!(d&128)),d<<=1);}),[a,c+e]}})}function no(e,o={}){return m(re(e,o),ne(e,o))}function te(e={}){var r;let o=(r=e.size)!=null?r:D();return b(o,"Codec [bool] requires a fixed size."),E(o,n=>n?1:0)}function ie(e={}){var r;let o=(r=e.size)!=null?r:N();return b(o,"Codec [bool] requires a fixed size."),y(o,n=>Number(n)===1)}function bo(e={}){return m(te(e),ie(e))}function ce(e={}){var n;let o=(n=e.size)!=null?n:"variable",r=l({getSizeFromValue:t=>t.length,write:(t,i,c)=>(i.set(t,c),c+t.length)});return o==="variable"?r:typeof o=="number"?M(r,o):l({getSizeFromValue:t=>S(t.length,o)+t.length,write:(t,i,c)=>(c=o.write(t.length,i,c),r.write(t,i,c))})}function de(e={}){var n;let o=(n=e.size)!=null?n:"variable",r=x({read:(t,i)=>{let c=t.slice(i);return [c,i+c.length]}});return o==="variable"?r:typeof o=="number"?_(r,o):x({read:(t,i)=>{h("bytes",t,i);let[c,a]=o.read(t,i),T=Number(c);i=a;let d=t.slice(i,i+T);V("bytes",T,d);let[u,s]=r.read(d,0);return i+=s,[u,i]}})}function Uo(e={}){return m(ce(e),de(e))}function ae(e,o={}){var t;let r=(t=o.size)!=null?t:D(),n=G(e,r);return l({...n!==null?{fixedSize:n}:{getSizeFromValue:i=>{let c=R(e,i),a=e[c][1];return S(c,r)+S(i,a)},maxSize:H(e,r)},write:(i,c,a)=>{let T=R(e,i);return a=r.write(T,c,a),e[T][1].write(i,c,a)}})}function Te(e,o={}){var t;let r=(t=o.size)!=null?t:N(),n=G(e,r);return x({...n!==null?{fixedSize:n}:{maxSize:H(e,r)},read:(i,c)=>{var F;h("dataEnum",i,c);let[a,T]=r.read(i,c);c=T;let d=(F=e[Number(a)])!=null?F:null;if(!d)throw new Error(`Enum discriminator out of range. Expected a number between 0 and ${e.length-1}, got ${a}.`);let[u,s]=d[1].read(i,c);return c=s,[{__kind:d[0],...u!=null?u:{}},c]}})}function Go(e,o={}){return m(ae(e,o),Te(e,o))}function G(e,o){if(e.length===0)return f(o)?o.fixedSize:null;if(!f(e[0][1]))return null;let r=e[0][1].fixedSize;return e.every(t=>f(t[1])&&t[1].fixedSize===r)&&f(o)?o.fixedSize+r:null}function H(e,o){var n;let r=W(e.map(([,t])=>z(t)));return (n=g([z(o),r]))!=null?n:void 0}function R(e,o){let r=e.findIndex(([n])=>o.__kind===n);if(r<0)throw new Error(`Invalid data enum variant. Expected one of [${e.map(([n])=>n).join(", ")}], got "${o.__kind}".`);return r}function I(e){var n;let o=g(e.map(p)),r=(n=g(e.map(z)))!=null?n:void 0;return l({...o===null?{getSizeFromValue:t=>e.map((i,c)=>S(t[c],i)).reduce((i,c)=>i+c,0),maxSize:r}:{fixedSize:o},write:(t,i,c)=>(B("tuple",e.length,t.length),e.forEach((a,T)=>{c=a.write(t[T],i,c);}),c)})}function k(e){var n;let o=g(e.map(p)),r=(n=g(e.map(z)))!=null?n:void 0;return x({...o===null?{maxSize:r}:{fixedSize:o},read:(t,i)=>{let c=[];return e.forEach(a=>{let[T,d]=a.read(t,i);c.push(T),i=d;}),[c,i]}})}function dr(e){return m(I(e),k(e))}function ue(e,o,r={}){return E(K(I([e,o]),r),n=>[...n.entries()])}function me(e,o,r={}){return y(w(k([e,o]),r),n=>new Map(n))}function Er(e,o,r={}){return m(ue(e,o,r),me(e,o,r))}function fe(e,o={}){var i,c,a;let r=(i=o.prefix)!=null?i:D(),n=(c=o.fixed)!=null?c:!1,t=f(e)&&f(r)&&e.fixedSize===0;if(n||t){b(e,"Fixed nullables can only be used with fixed-size codecs."),b(r,"Fixed nullables can only be used with fixed-size prefix.");let T=r.fixedSize+e.fixedSize;return l({fixedSize:T,write:(d,u,s)=>{let F=r.write(+(d!==null),u,s);return d!==null&&e.write(d,u,F),s+T}})}return l({getSizeFromValue:T=>S(+(T!==null),r)+(T!==null?S(T,e):0),maxSize:(a=g([r,e].map(z)))!=null?a:void 0,write:(T,d,u)=>(u=r.write(+(T!==null),d,u),T!==null&&(u=e.write(T,d,u)),u)})}function le(e,o={}){var c,a,T;let r=(c=o.prefix)!=null?c:N(),n=(a=o.fixed)!=null?a:!1,t=null,i=f(e)&&f(r)&&e.fixedSize===0;return (n||i)&&(b(e,"Fixed nullables can only be used with fixed-size codecs."),b(r,"Fixed nullables can only be used with fixed-size prefix."),t=r.fixedSize+e.fixedSize),x({...t===null?{maxSize:(T=g([r,e].map(z)))!=null?T:void 0}:{fixedSize:t},read:(d,u)=>{if(d.length-u<=0)return [null,u];let[s,F]=r.read(d,u);if(s===0)return [null,t!==null?u+t:F];let[A,U]=e.read(d,F);return [A,t!==null?u+t:U]}})}function Wr(e,o={}){let r=o;return m(fe(e,r),le(e,r))}function xe(e,o={}){var T;let r=(T=o.size)!=null?T:D(),{minRange:n,maxRange:t,stringValues:i,enumKeys:c,enumValues:a}=q(e);return E(r,d=>{let u=typeof d=="number"&&(d<n||d>t),s=typeof d=="string"&&!i.includes(d);if(u||s)throw new Error(`Invalid scalar enum variant. Expected one of [${i.join(", ")}] or a number between ${n} and ${t}, got "${d}".`);if(typeof d=="number")return d;let F=a.indexOf(d);return F>=0?F:c.indexOf(d)})}function se(e,o={}){var a;let r=(a=o.size)!=null?a:N(),{minRange:n,maxRange:t,isNumericEnum:i,enumValues:c}=q(e);return y(r,T=>{let d=Number(T);if(d<n||d>t)throw new Error(`Enum discriminator out of range. Expected a number between ${n} and ${t}, got ${d}.`);return i?d:c[d]})}function mn(e,o={}){return m(xe(e,o),se(e,o))}function q(e){let o=Object.keys(e),r=Object.values(e),n=r.some(a=>typeof a=="number"),t=0,i=n?r.length/2-1:r.length-1,c=n?[...o]:[...new Set([...o,...r])];return {enumKeys:o,enumValues:r,isNumericEnum:n,maxRange:i,minRange:t,stringValues:c}}function Fe(e,o={}){return E(K(e,o),r=>[...r])}function Ce(e,o={}){return y(w(e,o),r=>new Set(r))}function Nn(e,o={}){return m(Fe(e,o),Ce(e,o))}function Se(e){var t;let o=e.map(([,i])=>i),r=g(o.map(p)),n=(t=g(o.map(z)))!=null?t:void 0;return l({...r===null?{getSizeFromValue:i=>e.map(([c,a])=>S(i[c],a)).reduce((c,a)=>c+a,0),maxSize:n}:{fixedSize:r},write:(i,c,a)=>(e.forEach(([T,d])=>{a=d.write(i[T],c,a);}),a)})}function ge(e){var t;let o=e.map(([,i])=>i),r=g(o.map(p)),n=(t=g(o.map(z)))!=null?t:void 0;return x({...r===null?{maxSize:n}:{fixedSize:r},read:(i,c)=>{let a={};return e.forEach(([T,d])=>{let[u,s]=d.read(i,c);c=s,a[T]=u;}),[a,c]}})}function jn(e){return m(Se(e),ge(e))}function ze(){return l({fixedSize:0,write:(e,o,r)=>r})}function be(){return x({fixedSize:0,read:(e,o)=>[void 0,o]})}function Hn(){return m(ze(),be())}

	exports.assertValidNumberOfItemsForCodec = B;
	exports.getArrayCodec = Je;
	exports.getArrayDecoder = w;
	exports.getArrayEncoder = K;
	exports.getBitArrayCodec = no;
	exports.getBitArrayDecoder = ne;
	exports.getBitArrayEncoder = re;
	exports.getBooleanCodec = bo;
	exports.getBooleanDecoder = ie;
	exports.getBooleanEncoder = te;
	exports.getBytesCodec = Uo;
	exports.getBytesDecoder = de;
	exports.getBytesEncoder = ce;
	exports.getDataEnumCodec = Go;
	exports.getDataEnumDecoder = Te;
	exports.getDataEnumEncoder = ae;
	exports.getMapCodec = Er;
	exports.getMapDecoder = me;
	exports.getMapEncoder = ue;
	exports.getNullableCodec = Wr;
	exports.getNullableDecoder = le;
	exports.getNullableEncoder = fe;
	exports.getScalarEnumCodec = mn;
	exports.getScalarEnumDecoder = se;
	exports.getScalarEnumEncoder = xe;
	exports.getSetCodec = Nn;
	exports.getSetDecoder = Ce;
	exports.getSetEncoder = Fe;
	exports.getStructCodec = jn;
	exports.getStructDecoder = ge;
	exports.getStructEncoder = Se;
	exports.getTupleCodec = dr;
	exports.getTupleDecoder = k;
	exports.getTupleEncoder = I;
	exports.getUnitCodec = Hn;
	exports.getUnitDecoder = be;
	exports.getUnitEncoder = ze;

	return exports;

})({});
