{"version": 3, "sources": ["../src/array.ts", "../src/assertions.ts", "../src/utils.ts", "../src/bit-array.ts", "../src/boolean.ts", "../src/bytes.ts", "../src/data-enum.ts", "../src/map.ts", "../src/tuple.ts", "../src/nullable.ts", "../src/scalar-enum.ts", "../src/set.ts", "../src/struct.ts", "../src/unit.ts"], "names": ["newOffset", "combineCodec", "createDecoder", "createEncoder", "assertIsFixedSize", "assertByteArrayHasEnoughBytesForCodec", "getEncodedSize", "assertByteArrayIsNotEmptyForCodec", "isFixedSize", "getU8Decoder", "getU8Encoder", "mapDecoder", "mapEncoder"], "mappings": ";AAAA;AAAA,EACI;AAAA,EAEA;AAAA,EACA;AAAA,EACA;AAAA,EAMA;AAAA,OAKG;AACP,SAAS,eAAe,qBAAgE;;;AChBjF,SAAS,iCACZ,kBACA,UACA,QACF;AACE,MAAI,aAAa,QAAQ;AAErB,UAAM,IAAI,MAAM,aAAa,gBAAgB,aAAa,QAAQ,eAAe,MAAM,GAAG;AAAA,EAC9F;AACJ;;;ACVA,SAAS,mBAAmB;AAErB,SAAS,cAAc,OAAyC;AACnE,SAAO,MAAM;AAAA,IACT,CAAC,KAAK,SAAU,QAAQ,QAAQ,SAAS,OAAO,OAAO,KAAK,IAAI,KAAK,IAAI;AAAA,IACzE;AAAA,EACJ;AACJ;AAEO,SAAS,cAAc,OAAyC;AACnE,SAAO,MAAM,OAAO,CAAC,KAAK,SAAU,QAAQ,QAAQ,SAAS,OAAO,OAAO,MAAM,MAAO,CAAkB;AAC9G;AAEO,SAAS,aAAa,OAAoE;AAC7F,SAAO,YAAY,KAAK,IAAI,MAAM,YAAY;AAClD;AAEO,SAAS,WAAW,OAAoE;AAC3F,SAAO,YAAY,KAAK,IAAI,MAAM,YAAY,MAAM,WAAW;AACnE;;;AFkDO,SAAS,gBACZ,MACA,SAA0C,CAAC,GAC3B;AAChB,QAAM,OAAO,OAAO,QAAQ,cAAc;AAC1C,MAAI,SAAS,aAAa;AACtB,sBAAkB,MAAM,wDAAwD;AAAA,EACpF;AAEA,QAAM,YAAY,0BAA0B,MAAM,aAAa,IAAI,CAAC;AACpE,QAAM,UAAU,0BAA0B,MAAM,WAAW,IAAI,CAAC,KAAK;AAErE,SAAO,cAAc;AAAA,IACjB,GAAI,cAAc,OACZ,EAAE,UAAU,IACZ;AAAA,MACI,kBAAkB,CAAC,UAAmB;AAClC,cAAM,aAAa,OAAO,SAAS,WAAW,eAAe,MAAM,QAAQ,IAAI,IAAI;AACnF,eAAO,aAAa,CAAC,GAAG,KAAK,EAAE,OAAO,CAAC,KAAK,UAAU,MAAM,eAAe,OAAO,IAAI,GAAG,CAAC;AAAA,MAC9F;AAAA,MACA;AAAA,IACJ;AAAA,IACN,OAAO,CAAC,OAAgB,OAAO,WAAW;AACtC,UAAI,OAAO,SAAS,UAAU;AAC1B,yCAAiC,SAAS,MAAM,MAAM,MAAM;AAAA,MAChE;AACA,UAAI,OAAO,SAAS,UAAU;AAC1B,iBAAS,KAAK,MAAM,MAAM,QAAQ,OAAO,MAAM;AAAA,MACnD;AACA,YAAM,QAAQ,WAAS;AACnB,iBAAS,KAAK,MAAM,OAAO,OAAO,MAAM;AAAA,MAC5C,CAAC;AACD,aAAO;AAAA,IACX;AAAA,EACJ,CAAC;AACL;AAwBO,SAAS,gBAAqB,MAAoB,SAA0C,CAAC,GAAmB;AACnH,QAAM,OAAO,OAAO,QAAQ,cAAc;AAC1C,MAAI,SAAS,aAAa;AACtB,sBAAkB,MAAM,wDAAwD;AAAA,EACpF;AAEA,QAAM,WAAW,aAAa,IAAI;AAClC,QAAM,YAAY,0BAA0B,MAAM,QAAQ;AAC1D,QAAM,UAAU,0BAA0B,MAAM,WAAW,IAAI,CAAC,KAAK;AAErE,SAAO,cAAc;AAAA,IACjB,GAAI,cAAc,OAAO,EAAE,UAAU,IAAI,EAAE,QAAQ;AAAA,IACnD,MAAM,CAAC,OAAmB,WAAW;AACjC,YAAM,QAAe,CAAC;AACtB,UAAI,OAAO,SAAS,YAAY,MAAM,MAAM,MAAM,EAAE,WAAW,GAAG;AAC9D,eAAO,CAAC,OAAO,MAAM;AAAA,MACzB;AACA,YAAM,CAAC,cAAc,SAAS,IAAI,uBAAuB,MAAM,UAAU,OAAO,MAAM;AACtF,eAAS;AACT,eAAS,IAAI,GAAG,IAAI,cAAc,KAAK,GAAG;AACtC,cAAM,CAAC,OAAOA,UAAS,IAAI,KAAK,KAAK,OAAO,MAAM;AAClD,iBAASA;AACT,cAAM,KAAK,KAAK;AAAA,MACpB;AACA,aAAO,CAAC,OAAO,MAAM;AAAA,IACzB;AAAA,EACJ,CAAC;AACL;AAwBO,SAAS,cACZ,MACA,SAAwC,CAAC,GACpB;AACrB,SAAO,aAAa,gBAAgB,MAAM,MAAgB,GAAG,gBAAgB,MAAM,MAAgB,CAAC;AACxG;AAEA,SAAS,uBACL,MACA,UACA,OACA,QACyB;AACzB,MAAI,OAAO,SAAS,UAAU;AAC1B,WAAO,CAAC,MAAM,MAAM;AAAA,EACxB;AAEA,MAAI,OAAO,SAAS,UAAU;AAC1B,WAAO,KAAK,KAAK,OAAO,MAAM;AAAA,EAClC;AAEA,MAAI,SAAS,aAAa;AACtB,QAAI,aAAa,MAAM;AAEnB,YAAM,IAAI,MAAM,wDAAwD;AAAA,IAC5E;AACA,UAAM,YAAY,KAAK,IAAI,GAAG,MAAM,SAAS,MAAM;AACnD,QAAI,YAAY,aAAa,GAAG;AAE5B,YAAM,IAAI;AAAA,QACN,oCAAoC,SAAS,0CAA0C,QAAQ,iHAExE,SAAS,WAAW,QAAQ;AAAA,MACvD;AAAA,IACJ;AACA,WAAO,CAAC,YAAY,UAAU,MAAM;AAAA,EACxC;AAGA,QAAM,IAAI,MAAM,uCAAuC,KAAK,UAAU,IAAI,CAAC,EAAE;AACjF;AAEA,SAAS,0BAA0B,MAAqC,UAAwC;AAC5G,MAAI,OAAO,SAAS;AAAU,WAAO;AACrC,MAAI,SAAS;AAAG,WAAO;AACvB,SAAO,aAAa,OAAO,OAAO,WAAW;AACjD;;;AGjOA;AAAA,EACI;AAAA,EACA,gBAAAC;AAAA,EACA,iBAAAC;AAAA,EACA,iBAAAC;AAAA,OAIG;AAiBA,SAAS,mBACZ,MACA,SAAwC,CAAC,GACP;AAClC,QAAM,eAAoC,OAAO,WAAW,YAAY,EAAE,UAAU,OAAO,IAAI;AAC/F,QAAM,WAAW,aAAa,YAAY;AAC1C,SAAOA,eAAc;AAAA,IACjB,WAAW;AAAA,IACX,MAAM,OAAkB,OAAO,QAAQ;AACnC,YAAM,aAAuB,CAAC;AAE9B,eAAS,IAAI,GAAG,IAAI,MAAM,KAAK,GAAG;AAC9B,YAAI,OAAO;AACX,iBAAS,IAAI,GAAG,IAAI,GAAG,KAAK,GAAG;AAC3B,gBAAM,UAAU,OAAO,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC;AAC5C,kBAAQ,YAAY,WAAW,IAAI,IAAI;AAAA,QAC3C;AACA,YAAI,UAAU;AACV,qBAAW,QAAQ,IAAI;AAAA,QAC3B,OAAO;AACH,qBAAW,KAAK,IAAI;AAAA,QACxB;AAAA,MACJ;AAEA,YAAM,IAAI,YAAY,MAAM;AAC5B,aAAO;AAAA,IACX;AAAA,EACJ,CAAC;AACL;AAQO,SAAS,mBACZ,MACA,SAAwC,CAAC,GACP;AAClC,QAAM,eAAoC,OAAO,WAAW,YAAY,EAAE,UAAU,OAAO,IAAI;AAC/F,QAAM,WAAW,aAAa,YAAY;AAC1C,SAAOD,eAAc;AAAA,IACjB,WAAW;AAAA,IACX,KAAK,OAAO,QAAQ;AAChB,4CAAsC,YAAY,MAAM,OAAO,MAAM;AACrE,YAAM,WAAsB,CAAC;AAC7B,UAAI,QAAQ,MAAM,MAAM,QAAQ,SAAS,IAAI;AAC7C,cAAQ,WAAW,MAAM,QAAQ,IAAI;AAErC,YAAM,QAAQ,UAAQ;AAClB,iBAAS,IAAI,GAAG,IAAI,GAAG,KAAK,GAAG;AAC3B,cAAI,UAAU;AACV,qBAAS,KAAK,QAAQ,OAAO,CAAC,CAAC;AAC/B,qBAAS;AAAA,UACb,OAAO;AACH,qBAAS,KAAK,QAAQ,OAAO,GAAW,CAAC;AACzC,qBAAS;AAAA,UACb;AAAA,QACJ;AAAA,MACJ,CAAC;AAED,aAAO,CAAC,UAAU,SAAS,IAAI;AAAA,IACnC;AAAA,EACJ,CAAC;AACL;AAQO,SAAS,iBACZ,MACA,SAAwC,CAAC,GACE;AAC3C,SAAOD,cAAa,mBAAmB,MAAM,MAAM,GAAG,mBAAmB,MAAM,MAAM,CAAC;AAC1F;;;ACvGA;AAAA,EACI,qBAAAG;AAAA,EAEA,gBAAAH;AAAA,EAMA;AAAA,EACA;AAAA,OACG;AACP;AAAA,EAII;AAAA,EACA;AAAA,OAIG;AAqBA,SAAS,kBAAkB,SAA4C,CAAC,GAAqB;AAChG,QAAM,OAAO,OAAO,QAAQ,aAAa;AACzC,EAAAG,mBAAkB,MAAM,qCAAqC;AAC7D,SAAO,WAAW,MAAM,CAAC,UAAoB,QAAQ,IAAI,CAAE;AAC/D;AAYO,SAAS,kBAAkB,SAA4C,CAAC,GAAqB;AAChG,QAAM,OAAO,OAAO,QAAQ,aAAa;AACzC,EAAAA,mBAAkB,MAAM,qCAAqC;AAC7D,SAAO,WAAW,MAAM,CAAC,UAAoC,OAAO,KAAK,MAAM,CAAC;AACpF;AAYO,SAAS,gBAAgB,SAA0C,CAAC,GAAmB;AAC1F,SAAOH,cAAa,kBAAkB,MAAM,GAAG,kBAAkB,MAAM,CAAC;AAC5E;;;AC5EA;AAAA,EACI,yCAAAI;AAAA,EACA;AAAA,EAEA,gBAAAJ;AAAA,EACA,iBAAAC;AAAA,EACA,iBAAAC;AAAA,EAGA;AAAA,EAIA;AAAA,EACA,kBAAAG;AAAA,OAIG;AAwBA,SAAS,gBAAgB,SAA0C,CAAC,GAAwB;AAC/F,QAAM,OAAO,OAAO,QAAQ;AAE5B,QAAM,cAAmCH,eAAc;AAAA,IACnD,kBAAkB,CAAC,UAAsB,MAAM;AAAA,IAC/C,OAAO,CAAC,OAAmB,OAAO,WAAW;AACzC,YAAM,IAAI,OAAO,MAAM;AACvB,aAAO,SAAS,MAAM;AAAA,IAC1B;AAAA,EACJ,CAAC;AAED,MAAI,SAAS,YAAY;AACrB,WAAO;AAAA,EACX;AAEA,MAAI,OAAO,SAAS,UAAU;AAC1B,WAAO,WAAW,aAAa,IAAI;AAAA,EACvC;AAEA,SAAOA,eAAc;AAAA,IACjB,kBAAkB,CAAC,UAAsBG,gBAAe,MAAM,QAAQ,IAAI,IAAI,MAAM;AAAA,IACpF,OAAO,CAAC,OAAmB,OAAO,WAAW;AACzC,eAAS,KAAK,MAAM,MAAM,QAAQ,OAAO,MAAM;AAC/C,aAAO,YAAY,MAAM,OAAO,OAAO,MAAM;AAAA,IACjD;AAAA,EACJ,CAAC;AACL;AAWO,SAAS,gBAAgB,SAA0C,CAAC,GAAwB;AAC/F,QAAM,OAAO,OAAO,QAAQ;AAE5B,QAAM,cAAmCJ,eAAc;AAAA,IACnD,MAAM,CAAC,OAAmB,WAAW;AACjC,YAAM,QAAQ,MAAM,MAAM,MAAM;AAChC,aAAO,CAAC,OAAO,SAAS,MAAM,MAAM;AAAA,IACxC;AAAA,EACJ,CAAC;AAED,MAAI,SAAS,YAAY;AACrB,WAAO;AAAA,EACX;AAEA,MAAI,OAAO,SAAS,UAAU;AAC1B,WAAO,WAAW,aAAa,IAAI;AAAA,EACvC;AAEA,SAAOA,eAAc;AAAA,IACjB,MAAM,CAAC,OAAmB,WAAW;AACjC,wCAAkC,SAAS,OAAO,MAAM;AACxD,YAAM,CAAC,cAAc,YAAY,IAAI,KAAK,KAAK,OAAO,MAAM;AAC5D,YAAM,SAAS,OAAO,YAAY;AAClC,eAAS;AACT,YAAM,eAAe,MAAM,MAAM,QAAQ,SAAS,MAAM;AACxD,MAAAG,uCAAsC,SAAS,QAAQ,YAAY;AACnE,YAAM,CAAC,OAAO,aAAa,IAAI,YAAY,KAAK,cAAc,CAAC;AAC/D,gBAAU;AACV,aAAO,CAAC,OAAO,MAAM;AAAA,IACzB;AAAA,EACJ,CAAC;AACL;AAWO,SAAS,cAAc,SAAwC,CAAC,GAAsB;AACzF,SAAOJ,cAAa,gBAAgB,MAAM,GAAG,gBAAgB,MAAM,CAAC;AACxE;;;AC3HA;AAAA,EACI,qCAAAM;AAAA,EAEA,gBAAAN;AAAA,EACA,iBAAAC;AAAA,EACA,iBAAAC;AAAA,EAGA,kBAAAG;AAAA,EACA,eAAAE;AAAA,OACG;AACP,SAAS,gBAAAC,eAAc,gBAAAC,qBAA+D;AAkG/E,SAAS,mBACZ,UACA,SAA6C,CAAC,GAChC;AACd,QAAM,SAAS,OAAO,QAAQA,cAAa;AAC3C,QAAM,YAAY,qBAAqB,UAAU,MAAM;AACvD,SAAOP,eAAc;AAAA,IACjB,GAAI,cAAc,OACZ,EAAE,UAAU,IACZ;AAAA,MACI,kBAAkB,CAAC,YAAmB;AAClC,cAAM,gBAAgB,wBAAwB,UAAU,OAAO;AAC/D,cAAM,iBAAiB,SAAS,aAAa,EAAE,CAAC;AAChD,eACIG,gBAAe,eAAe,MAAM,IACpCA,gBAAe,SAAyB,cAAc;AAAA,MAE9D;AAAA,MACA,SAAS,mBAAmB,UAAU,MAAM;AAAA,IAChD;AAAA,IACN,OAAO,CAAC,SAAgB,OAAO,WAAW;AACtC,YAAM,gBAAgB,wBAAwB,UAAU,OAAO;AAC/D,eAAS,OAAO,MAAM,eAAe,OAAO,MAAM;AAClD,YAAM,iBAAiB,SAAS,aAAa,EAAE,CAAC;AAChD,aAAO,eAAe,MAAM,SAAyB,OAAO,MAAM;AAAA,IACtE;AAAA,EACJ,CAAC;AACL;AAQO,SAAS,mBACZ,UACA,SAA6C,CAAC,GACpC;AACV,QAAM,SAAS,OAAO,QAAQG,cAAa;AAC3C,QAAM,YAAY,qBAAqB,UAAU,MAAM;AACvD,SAAOP,eAAc;AAAA,IACjB,GAAI,cAAc,OAAO,EAAE,UAAU,IAAI,EAAE,SAAS,mBAAmB,UAAU,MAAM,EAAE;AAAA,IACzF,MAAM,CAAC,OAAmB,WAAW;AACjC,MAAAK,mCAAkC,YAAY,OAAO,MAAM;AAC3D,YAAM,CAAC,eAAe,OAAO,IAAI,OAAO,KAAK,OAAO,MAAM;AAC1D,eAAS;AACT,YAAM,eAAe,SAAS,OAAO,aAAa,CAAC,KAAK;AACxD,UAAI,CAAC,cAAc;AAEf,cAAM,IAAI;AAAA,UACN,oEACuC,SAAS,SAAS,CAAC,SAAS,aAAa;AAAA,QACpF;AAAA,MACJ;AACA,YAAM,CAAC,SAAS,OAAO,IAAI,aAAa,CAAC,EAAE,KAAK,OAAO,MAAM;AAC7D,eAAS;AACT,aAAO,CAAC,EAAE,QAAQ,aAAa,CAAC,GAAG,GAAI,WAAW,CAAC,EAAG,GAAQ,MAAM;AAAA,IACxE;AAAA,EACJ,CAAC;AACL;AAQO,SAAS,iBACZ,UACA,SAA2C,CAAC,GACjC;AACX,SAAON,cAAa,mBAAsB,UAAU,MAAM,GAAG,mBAAsB,UAAU,MAAM,CAAC;AACxG;AAEA,SAAS,qBACL,UACA,QACa;AACb,MAAI,SAAS,WAAW;AAAG,WAAOO,aAAY,MAAM,IAAI,OAAO,YAAY;AAC3E,MAAI,CAACA,aAAY,SAAS,CAAC,EAAE,CAAC,CAAC;AAAG,WAAO;AACzC,QAAM,cAAc,SAAS,CAAC,EAAE,CAAC,EAAE;AACnC,QAAM,oBAAoB,SAAS;AAAA,IAC/B,aAAWA,aAAY,QAAQ,CAAC,CAAC,KAAK,QAAQ,CAAC,EAAE,cAAc;AAAA,EACnE;AACA,MAAI,CAAC;AAAmB,WAAO;AAC/B,SAAOA,aAAY,MAAM,IAAI,OAAO,YAAY,cAAc;AAClE;AAEA,SAAS,mBACL,UACA,QACF;AACE,QAAM,iBAAiB,cAAc,SAAS,IAAI,CAAC,CAAC,EAAE,KAAK,MAAM,WAAW,KAAK,CAAC,CAAC;AACnF,SAAO,cAAc,CAAC,WAAW,MAAM,GAAG,cAAc,CAAC,KAAK;AAClE;AAEA,SAAS,wBAAgD,UAAyC,SAAgB;AAC9G,QAAM,gBAAgB,SAAS,UAAU,CAAC,CAAC,GAAG,MAAM,QAAQ,WAAW,GAAG;AAC1E,MAAI,gBAAgB,GAAG;AAEnB,UAAM,IAAI;AAAA,MACN,+CACwB,SAAS,IAAI,CAAC,CAAC,GAAG,MAAM,GAAG,EAAE,KAAK,IAAI,CAAC,WACnD,QAAQ,MAAM;AAAA,IAC9B;AAAA,EACJ;AACA,SAAO;AACX;;;ACzNA;AAAA,EAEI,gBAAAP;AAAA,EAMA,cAAAU;AAAA,EACA,cAAAC;AAAA,OAIG;;;ACbP;AAAA,EAEI,gBAAAX;AAAA,EACA,iBAAAC;AAAA,EACA,iBAAAC;AAAA,EAMA,kBAAAG;AAAA,OAIG;AAoCA,SAAS,gBAAwC,OAAkD;AACtG,QAAM,YAAY,cAAc,MAAM,IAAI,YAAY,CAAC;AACvD,QAAM,UAAU,cAAc,MAAM,IAAI,UAAU,CAAC,KAAK;AAExD,SAAOH,eAAc;AAAA,IACjB,GAAI,cAAc,OACZ;AAAA,MACI,kBAAkB,CAAC,UACf,MAAM,IAAI,CAAC,MAAM,UAAUG,gBAAe,MAAM,KAAK,GAAG,IAAI,CAAC,EAAE,OAAO,CAAC,KAAK,QAAQ,MAAM,KAAK,CAAC;AAAA,MACpG;AAAA,IACJ,IACA,EAAE,UAAU;AAAA,IAClB,OAAO,CAAC,OAAc,OAAO,WAAW;AACpC,uCAAiC,SAAS,MAAM,QAAQ,MAAM,MAAM;AACpE,YAAM,QAAQ,CAAC,MAAM,UAAU;AAC3B,iBAAS,KAAK,MAAM,MAAM,KAAK,GAAG,OAAO,MAAM;AAAA,MACnD,CAAC;AACD,aAAO;AAAA,IACX;AAAA,EACJ,CAAC;AACL;AASO,SAAS,gBAAsC,OAA8C;AAChG,QAAM,YAAY,cAAc,MAAM,IAAI,YAAY,CAAC;AACvD,QAAM,UAAU,cAAc,MAAM,IAAI,UAAU,CAAC,KAAK;AAExD,SAAOJ,eAAc;AAAA,IACjB,GAAI,cAAc,OAAO,EAAE,QAAQ,IAAI,EAAE,UAAU;AAAA,IACnD,MAAM,CAAC,OAAmB,WAAW;AACjC,YAAM,SAAS,CAAC;AAChB,YAAM,QAAQ,UAAQ;AAClB,cAAM,CAAC,UAAU,SAAS,IAAI,KAAK,KAAK,OAAO,MAAM;AACrD,eAAO,KAAK,QAAQ;AACpB,iBAAS;AAAA,MACb,CAAC;AACD,aAAO,CAAC,QAAQ,MAAM;AAAA,IAC1B;AAAA,EACJ,CAAC;AACL;AAaO,SAAS,cACZ,OACiB;AACjB,SAAOD;AAAA,IACH,gBAAgB,KAAkC;AAAA,IAClD,gBAAgB,KAAgC;AAAA,EACpD;AACJ;;;AD5DO,SAAS,cACZ,KACA,OACA,SAAwC,CAAC,GACP;AAClC,SAAOW;AAAA,IACH,gBAAgB,gBAAgB,CAAC,KAAK,KAAK,CAAC,GAAG,MAAgB;AAAA,IAC/D,CAAC,QAA6D,CAAC,GAAG,IAAI,QAAQ,CAAC;AAAA,EACnF;AACJ;AA6BO,SAAS,cACZ,KACA,OACA,SAAwC,CAAC,GACX;AAC9B,SAAOD;AAAA,IACH,gBAAgB,gBAAgB,CAAC,KAAK,KAAK,CAAC,GAAG,MAAgB;AAAA,IAC/D,CAAC,YAAyD,IAAI,IAAI,OAAO;AAAA,EAC7E;AACJ;AAiDO,SAAS,YAMZ,KACA,OACA,SAAsC,CAAC,GACgB;AACvD,SAAOV,cAAa,cAAc,KAAK,OAAO,MAAgB,GAAG,cAAc,KAAK,OAAO,MAAgB,CAAC;AAChH;;;AElKA;AAAA,EACI,qBAAAG;AAAA,EAEA,gBAAAH;AAAA,EACA,iBAAAC;AAAA,EACA,iBAAAC;AAAA,EAMA,kBAAAG;AAAA,EACA,eAAAE;AAAA,OAIG;AACP;AAAA,EAII,gBAAAC;AAAA,EACA,gBAAAC;AAAA,OAIG;AAyCA,SAAS,mBACZ,MACA,SAA6C,CAAC,GACzB;AACrB,QAAM,SAAS,OAAO,UAAUA,cAAa;AAC7C,QAAM,QAAQ,OAAO,SAAS;AAE9B,QAAM,iBAAiBF,aAAY,IAAI,KAAKA,aAAY,MAAM,KAAK,KAAK,cAAc;AACtF,MAAI,SAAS,gBAAgB;AACzB,IAAAJ,mBAAkB,MAAM,0DAA0D;AAClF,IAAAA,mBAAkB,QAAQ,0DAA0D;AACpF,UAAM,YAAY,OAAO,YAAY,KAAK;AAC1C,WAAOD,eAAc;AAAA,MACjB;AAAA,MACA,OAAO,CAAC,QAAsB,OAAO,WAAW;AAC5C,cAAM,eAAe,OAAO,MAAM,OAAO,WAAW,IAAI,GAAG,OAAO,MAAM;AACxE,YAAI,WAAW,MAAM;AACjB,eAAK,MAAM,QAAQ,OAAO,YAAY;AAAA,QAC1C;AACA,eAAO,SAAS;AAAA,MACpB;AAAA,IACJ,CAAC;AAAA,EACL;AAEA,SAAOA,eAAc;AAAA,IACjB,kBAAkB,CAAC,WACfG,gBAAe,OAAO,WAAW,IAAI,GAAG,MAAM,KAAK,WAAW,OAAOA,gBAAe,QAAQ,IAAI,IAAI;AAAA,IACxG,SAAS,cAAc,CAAC,QAAQ,IAAI,EAAE,IAAI,UAAU,CAAC,KAAK;AAAA,IAC1D,OAAO,CAAC,QAAsB,OAAO,WAAW;AAC5C,eAAS,OAAO,MAAM,OAAO,WAAW,IAAI,GAAG,OAAO,MAAM;AAC5D,UAAI,WAAW,MAAM;AACjB,iBAAS,KAAK,MAAM,QAAQ,OAAO,MAAM;AAAA,MAC7C;AACA,aAAO;AAAA,IACX;AAAA,EACJ,CAAC;AACL;AAoBO,SAAS,mBACZ,MACA,SAA6C,CAAC,GAC3B;AACnB,QAAM,SAAS,OAAO,UAAUG,cAAa;AAC7C,QAAM,QAAQ,OAAO,SAAS;AAE9B,MAAI,YAA2B;AAC/B,QAAM,iBAAiBD,aAAY,IAAI,KAAKA,aAAY,MAAM,KAAK,KAAK,cAAc;AACtF,MAAI,SAAS,gBAAgB;AACzB,IAAAJ,mBAAkB,MAAM,0DAA0D;AAClF,IAAAA,mBAAkB,QAAQ,0DAA0D;AACpF,gBAAY,OAAO,YAAY,KAAK;AAAA,EACxC;AAEA,SAAOF,eAAc;AAAA,IACjB,GAAI,cAAc,OACZ,EAAE,SAAS,cAAc,CAAC,QAAQ,IAAI,EAAE,IAAI,UAAU,CAAC,KAAK,OAAU,IACtE,EAAE,UAAU;AAAA,IAClB,MAAM,CAAC,OAAmB,WAAW;AACjC,UAAI,MAAM,SAAS,UAAU,GAAG;AAC5B,eAAO,CAAC,MAAM,MAAM;AAAA,MACxB;AACA,YAAM,CAAC,QAAQ,YAAY,IAAI,OAAO,KAAK,OAAO,MAAM;AACxD,UAAI,WAAW,GAAG;AACd,eAAO,CAAC,MAAM,cAAc,OAAO,SAAS,YAAY,YAAY;AAAA,MACxE;AACA,YAAM,CAAC,OAAO,SAAS,IAAI,KAAK,KAAK,OAAO,YAAY;AACxD,aAAO,CAAC,OAAO,cAAc,OAAO,SAAS,YAAY,SAAS;AAAA,IACtE;AAAA,EACJ,CAAC;AACL;AAoBO,SAAS,iBACZ,MACA,SAA2C,CAAC,GACb;AAC/B,QAAM,aAAa;AACnB,SAAOD,cAAa,mBAA0B,MAAM,UAAU,GAAG,mBAAwB,MAAM,UAAU,CAAC;AAC9G;;;ACpLA;AAAA,EAEI,gBAAAA;AAAA,EAMA,cAAAU;AAAA,EACA,cAAAC;AAAA,OAIG;AACP;AAAA,EAII,gBAAAH;AAAA,EACA,gBAAAC;AAAA,OAIG;AAuCA,SAAS,qBACZ,aACA,SAA+C,CAAC,GAClC;AACd,QAAM,SAAS,OAAO,QAAQA,cAAa;AAC3C,QAAM,EAAE,UAAU,UAAU,cAAc,UAAU,WAAW,IAAI,mBAAmB,WAAW;AACjG,SAAOE,YAAW,QAAQ,CAAC,UAAyB;AAChD,UAAM,kBAAkB,OAAO,UAAU,aAAa,QAAQ,YAAY,QAAQ;AAClF,UAAM,kBAAkB,OAAO,UAAU,YAAY,CAAC,aAAa,SAAS,KAAK;AACjF,QAAI,mBAAmB,iBAAiB;AAEpC,YAAM,IAAI;AAAA,QACN,iDACwB,aAAa,KAAK,IAAI,CAAC,yBACpB,QAAQ,QAAQ,QAAQ,UACvC,KAAK;AAAA,MACrB;AAAA,IACJ;AACA,QAAI,OAAO,UAAU;AAAU,aAAO;AACtC,UAAM,aAAa,WAAW,QAAQ,KAAK;AAC3C,QAAI,cAAc;AAAG,aAAO;AAC5B,WAAO,SAAS,QAAQ,KAAe;AAAA,EAC3C,CAAC;AACL;AAmBO,SAAS,qBACZ,aACA,SAA+C,CAAC,GACpC;AACZ,QAAM,SAAS,OAAO,QAAQH,cAAa;AAC3C,QAAM,EAAE,UAAU,UAAU,eAAe,WAAW,IAAI,mBAAmB,WAAW;AACxF,SAAOE,YAAW,QAAQ,CAAC,UAAgC;AACvD,UAAM,gBAAgB,OAAO,KAAK;AAClC,QAAI,gBAAgB,YAAY,gBAAgB,UAAU;AAEtD,YAAM,IAAI;AAAA,QACN,8DACiC,QAAQ,QAAQ,QAAQ,SAAS,aAAa;AAAA,MACnF;AAAA,IACJ;AACA,WAAQ,gBAAgB,gBAAgB,WAAW,aAAa;AAAA,EACpE,CAAC;AACL;AAmBO,SAAS,mBACZ,aACA,SAA6C,CAAC,GAClC;AACZ,SAAOV,cAAa,qBAAqB,aAAa,MAAM,GAAG,qBAAqB,aAAa,MAAM,CAAC;AAC5G;AAEA,SAAS,mBAA0B,aAOjC;AACE,QAAM,WAAW,OAAO,KAAK,WAAW;AACxC,QAAM,aAAa,OAAO,OAAO,WAAW;AAC5C,QAAM,gBAAgB,WAAW,KAAK,OAAK,OAAO,MAAM,QAAQ;AAChE,QAAM,WAAW;AACjB,QAAM,WAAW,gBAAgB,WAAW,SAAS,IAAI,IAAI,WAAW,SAAS;AACjF,QAAM,eAAyB,gBAAgB,CAAC,GAAG,QAAQ,IAAI,CAAC,GAAG,oBAAI,IAAI,CAAC,GAAG,UAAU,GAAG,UAAU,CAAC,CAAC;AAExG,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACJ;;;AC1KA;AAAA,EAEI,gBAAAA;AAAA,EAMA,cAAAU;AAAA,EACA,cAAAC;AAAA,OAIG;AAoCA,SAAS,cACZ,MACA,SAAwC,CAAC,GACtB;AACnB,SAAOA,YAAW,gBAAgB,MAAM,MAAgB,GAAG,CAAC,QAA6B,CAAC,GAAG,GAAG,CAAC;AACrG;AAwBO,SAAS,cAAmB,MAAoB,SAAwC,CAAC,GAAsB;AAClH,SAAOD,YAAW,gBAAgB,MAAM,MAAgB,GAAG,CAAC,YAA6B,IAAI,IAAI,OAAO,CAAC;AAC7G;AAwBO,SAAS,YACZ,MACA,SAAsC,CAAC,GACZ;AAC3B,SAAOV,eAAa,cAAc,MAAM,MAAgB,GAAG,cAAc,MAAM,MAAgB,CAAC;AACpG;;;AC7GA;AAAA,EAEI,gBAAAA;AAAA,EACA,iBAAAC;AAAA,EACA,iBAAAC;AAAA,EAMA,kBAAAG;AAAA,OAIG;AAuDA,SAAS,iBAAuC,QAAqD;AACxG,QAAM,cAAc,OAAO,IAAI,CAAC,CAAC,EAAE,KAAK,MAAM,KAAK;AACnD,QAAM,YAAY,cAAc,YAAY,IAAI,YAAY,CAAC;AAC7D,QAAM,UAAU,cAAc,YAAY,IAAI,UAAU,CAAC,KAAK;AAE9D,SAAOH,eAAc;AAAA,IACjB,GAAI,cAAc,OACZ;AAAA,MACI,kBAAkB,CAAC,UACf,OACK,IAAI,CAAC,CAAC,KAAK,KAAK,MAAMG,gBAAe,MAAM,GAAG,GAAG,KAAK,CAAC,EACvD,OAAO,CAAC,KAAK,QAAQ,MAAM,KAAK,CAAC;AAAA,MAC1C;AAAA,IACJ,IACA,EAAE,UAAU;AAAA,IAClB,OAAO,CAAC,QAAe,OAAO,WAAW;AACrC,aAAO,QAAQ,CAAC,CAAC,KAAK,KAAK,MAAM;AAC7B,iBAAS,MAAM,MAAM,OAAO,GAAG,GAAG,OAAO,MAAM;AAAA,MACnD,CAAC;AACD,aAAO;AAAA,IACX;AAAA,EACJ,CAAC;AACL;AASO,SAAS,iBAAqC,QAAiD;AAClG,QAAM,cAAc,OAAO,IAAI,CAAC,CAAC,EAAE,KAAK,MAAM,KAAK;AACnD,QAAM,YAAY,cAAc,YAAY,IAAI,YAAY,CAAC;AAC7D,QAAM,UAAU,cAAc,YAAY,IAAI,UAAU,CAAC,KAAK;AAE9D,SAAOJ,eAAc;AAAA,IACjB,GAAI,cAAc,OAAO,EAAE,QAAQ,IAAI,EAAE,UAAU;AAAA,IACnD,MAAM,CAAC,OAAmB,WAAW;AACjC,YAAM,SAAuB,CAAC;AAC9B,aAAO,QAAQ,CAAC,CAAC,KAAK,KAAK,MAAM;AAC7B,cAAM,CAAC,OAAO,SAAS,IAAI,MAAM,KAAK,OAAO,MAAM;AACnD,iBAAS;AACT,eAAO,GAAG,IAAI;AAAA,MAClB,CAAC;AACD,aAAO,CAAC,QAAe,MAAM;AAAA,IACjC;AAAA,EACJ,CAAC;AACL;AAaO,SAAS,eACZ,QACiB;AACjB,SAAOD,eAAa,iBAAiB,MAAM,GAAG,iBAAiB,MAAM,CAAC;AAC1E;;;ACtIA;AAAA,EACI,gBAAAA;AAAA,EACA,iBAAAC;AAAA,EACA,iBAAAC;AAAA,OAIG;AAKA,SAAS,iBAA4C;AACxD,SAAOA,eAAc;AAAA,IACjB,WAAW;AAAA,IACX,OAAO,CAAC,QAAQ,QAAQ,WAAW;AAAA,EACvC,CAAC;AACL;AAKO,SAAS,iBAA4C;AACxD,SAAOD,eAAc;AAAA,IACjB,WAAW;AAAA,IACX,MAAM,CAAC,QAAoB,WAAW,CAAC,QAAW,MAAM;AAAA,EAC5D,CAAC;AACL;AAKO,SAAS,eAA8C;AAC1D,SAAOD,eAAa,eAAe,GAAG,eAAe,CAAC;AAC1D", "sourcesContent": ["import {\n    assertIsFixedSize,\n    Codec,\n    combineCodec,\n    createDecoder,\n    create<PERSON>nco<PERSON>,\n    Decoder,\n    Encoder,\n    FixedSizeCodec,\n    FixedSizeDecoder,\n    FixedSizeEncoder,\n    getEncodedSize,\n    Offset,\n    VariableSizeCodec,\n    VariableSizeDecoder,\n    VariableSizeEncoder,\n} from '@solana/codecs-core';\nimport { getU32Decoder, getU32Encoder, NumberCodec, NumberDecoder, NumberEncoder } from '@solana/codecs-numbers';\n\nimport { assertValidNumberOfItemsForCodec } from './assertions';\nimport { getFixedSize, getMaxSize } from './utils';\n\n/**\n * Represents all the size options for array-like codecs\n * — i.e. `array`, `map` and `set`.\n *\n * It can be one of the following:\n * - a {@link NumberCodec} that prefixes its content with its size.\n * - a fixed number of items.\n * - or `'remainder'` to infer the number of items by dividing\n *   the rest of the byte array by the fixed size of its item.\n *   Note that this option is only available for fixed-size items.\n */\nexport type ArrayLikeCodecSize<TPrefix extends NumberCodec | NumberEncoder | NumberDecoder> =\n    | TPrefix\n    | number\n    | 'remainder';\n\n/** Defines the configs for array codecs. */\nexport type ArrayCodecConfig<TPrefix extends NumberCodec | NumberEncoder | NumberDecoder> = {\n    /**\n     * The size of the array.\n     * @defaultValue u32 prefix.\n     */\n    size?: ArrayLikeCodecSize<TPrefix>;\n};\n\n/**\n * Encodes an array of items.\n *\n * @param item - The encoder to use for the array's items.\n * @param config - A set of config for the encoder.\n */\nexport function getArrayEncoder<TFrom>(\n    item: Encoder<TFrom>,\n    config: ArrayCodecConfig<NumberEncoder> & { size: 0 },\n): FixedSizeEncoder<TFrom[], 0>;\nexport function getArrayEncoder<TFrom>(\n    item: FixedSizeEncoder<TFrom>,\n    config: ArrayCodecConfig<NumberEncoder> & { size: number },\n): FixedSizeEncoder<TFrom[]>;\nexport function getArrayEncoder<TFrom>(\n    item: FixedSizeEncoder<TFrom>,\n    config: ArrayCodecConfig<NumberEncoder> & { size: 'remainder' },\n): VariableSizeEncoder<TFrom[]>;\nexport function getArrayEncoder<TFrom>(\n    item: Encoder<TFrom>,\n    config?: ArrayCodecConfig<NumberEncoder> & { size?: number | NumberEncoder },\n): VariableSizeEncoder<TFrom[]>;\nexport function getArrayEncoder<TFrom>(\n    item: Encoder<TFrom>,\n    config: ArrayCodecConfig<NumberEncoder> = {},\n): Encoder<TFrom[]> {\n    const size = config.size ?? getU32Encoder();\n    if (size === 'remainder') {\n        assertIsFixedSize(item, 'Codecs of \"remainder\" size must have fixed-size items.');\n    }\n\n    const fixedSize = computeArrayLikeCodecSize(size, getFixedSize(item));\n    const maxSize = computeArrayLikeCodecSize(size, getMaxSize(item)) ?? undefined;\n\n    return createEncoder({\n        ...(fixedSize !== null\n            ? { fixedSize }\n            : {\n                  getSizeFromValue: (array: TFrom[]) => {\n                      const prefixSize = typeof size === 'object' ? getEncodedSize(array.length, size) : 0;\n                      return prefixSize + [...array].reduce((all, value) => all + getEncodedSize(value, item), 0);\n                  },\n                  maxSize,\n              }),\n        write: (array: TFrom[], bytes, offset) => {\n            if (typeof size === 'number') {\n                assertValidNumberOfItemsForCodec('array', size, array.length);\n            }\n            if (typeof size === 'object') {\n                offset = size.write(array.length, bytes, offset);\n            }\n            array.forEach(value => {\n                offset = item.write(value, bytes, offset);\n            });\n            return offset;\n        },\n    });\n}\n\n/**\n * Decodes an array of items.\n *\n * @param item - The encoder to use for the array's items.\n * @param config - A set of config for the encoder.\n */\nexport function getArrayDecoder<TTo>(\n    item: Decoder<TTo>,\n    config: ArrayCodecConfig<NumberDecoder> & { size: 0 },\n): FixedSizeDecoder<TTo[], 0>;\nexport function getArrayDecoder<TTo>(\n    item: FixedSizeDecoder<TTo>,\n    config: ArrayCodecConfig<NumberDecoder> & { size: number },\n): FixedSizeDecoder<TTo[]>;\nexport function getArrayDecoder<TTo>(\n    item: FixedSizeDecoder<TTo>,\n    config: ArrayCodecConfig<NumberDecoder> & { size: 'remainder' },\n): VariableSizeDecoder<TTo[]>;\nexport function getArrayDecoder<TTo>(\n    item: Decoder<TTo>,\n    config?: ArrayCodecConfig<NumberDecoder> & { size?: number | NumberDecoder },\n): VariableSizeDecoder<TTo[]>;\nexport function getArrayDecoder<TTo>(item: Decoder<TTo>, config: ArrayCodecConfig<NumberDecoder> = {}): Decoder<TTo[]> {\n    const size = config.size ?? getU32Decoder();\n    if (size === 'remainder') {\n        assertIsFixedSize(item, 'Codecs of \"remainder\" size must have fixed-size items.');\n    }\n\n    const itemSize = getFixedSize(item);\n    const fixedSize = computeArrayLikeCodecSize(size, itemSize);\n    const maxSize = computeArrayLikeCodecSize(size, getMaxSize(item)) ?? undefined;\n\n    return createDecoder({\n        ...(fixedSize !== null ? { fixedSize } : { maxSize }),\n        read: (bytes: Uint8Array, offset) => {\n            const array: TTo[] = [];\n            if (typeof size === 'object' && bytes.slice(offset).length === 0) {\n                return [array, offset];\n            }\n            const [resolvedSize, newOffset] = readArrayLikeCodecSize(size, itemSize, bytes, offset);\n            offset = newOffset;\n            for (let i = 0; i < resolvedSize; i += 1) {\n                const [value, newOffset] = item.read(bytes, offset);\n                offset = newOffset;\n                array.push(value);\n            }\n            return [array, offset];\n        },\n    });\n}\n\n/**\n * Creates a codec for an array of items.\n *\n * @param item - The codec to use for the array's items.\n * @param config - A set of config for the codec.\n */\nexport function getArrayCodec<TFrom, TTo extends TFrom = TFrom>(\n    item: Codec<TFrom, TTo>,\n    config: ArrayCodecConfig<NumberCodec> & { size: 0 },\n): FixedSizeCodec<TFrom[], TTo[], 0>;\nexport function getArrayCodec<TFrom, TTo extends TFrom = TFrom>(\n    item: FixedSizeCodec<TFrom, TTo>,\n    config: ArrayCodecConfig<NumberCodec> & { size: number },\n): FixedSizeCodec<TFrom[], TTo[]>;\nexport function getArrayCodec<TFrom, TTo extends TFrom = TFrom>(\n    item: FixedSizeCodec<TFrom, TTo>,\n    config: ArrayCodecConfig<NumberCodec> & { size: 'remainder' },\n): VariableSizeCodec<TFrom[], TTo[]>;\nexport function getArrayCodec<TFrom, TTo extends TFrom = TFrom>(\n    item: Codec<TFrom, TTo>,\n    config?: ArrayCodecConfig<NumberCodec> & { size?: number | NumberCodec },\n): VariableSizeCodec<TFrom[], TTo[]>;\nexport function getArrayCodec<TFrom, TTo extends TFrom = TFrom>(\n    item: Codec<TFrom, TTo>,\n    config: ArrayCodecConfig<NumberCodec> = {},\n): Codec<TFrom[], TTo[]> {\n    return combineCodec(getArrayEncoder(item, config as object), getArrayDecoder(item, config as object));\n}\n\nfunction readArrayLikeCodecSize(\n    size: ArrayLikeCodecSize<NumberDecoder>,\n    itemSize: number | null,\n    bytes: Uint8Array,\n    offset: Offset,\n): [number | bigint, Offset] {\n    if (typeof size === 'number') {\n        return [size, offset];\n    }\n\n    if (typeof size === 'object') {\n        return size.read(bytes, offset);\n    }\n\n    if (size === 'remainder') {\n        if (itemSize === null) {\n            // TODO: Coded error.\n            throw new Error('Codecs of \"remainder\" size must have fixed-size items.');\n        }\n        const remainder = Math.max(0, bytes.length - offset);\n        if (remainder % itemSize !== 0) {\n            // TODO: Coded error.\n            throw new Error(\n                `The remainder of the byte array (${remainder} bytes) cannot be split into chunks of ${itemSize} bytes. ` +\n                    `Codecs of \"remainder\" size must have a remainder that is a multiple of its item size. ` +\n                    `In other words, ${remainder} modulo ${itemSize} should be equal to zero.`,\n            );\n        }\n        return [remainder / itemSize, offset];\n    }\n\n    // TODO: Coded error.\n    throw new Error(`Unrecognized array-like codec size: ${JSON.stringify(size)}`);\n}\n\nfunction computeArrayLikeCodecSize(size: object | number | 'remainder', itemSize: number | null): number | null {\n    if (typeof size !== 'number') return null;\n    if (size === 0) return 0;\n    return itemSize === null ? null : itemSize * size;\n}\n", "/** Checks the number of items in an array-like structure is expected. */\nexport function assertValidNumberOfItemsForCodec(\n    codecDescription: string,\n    expected: number | bigint,\n    actual: number | bigint,\n) {\n    if (expected !== actual) {\n        // TODO: Coded error.\n        throw new Error(`Expected [${codecDescription}] to have ${expected} items, got ${actual}.`);\n    }\n}\n", "import { isFixedSize } from '@solana/codecs-core';\n\nexport function maxCodecSizes(sizes: (number | null)[]): number | null {\n    return sizes.reduce(\n        (all, size) => (all === null || size === null ? null : Math.max(all, size)),\n        0 as number | null,\n    );\n}\n\nexport function sumCodecSizes(sizes: (number | null)[]): number | null {\n    return sizes.reduce((all, size) => (all === null || size === null ? null : all + size), 0 as number | null);\n}\n\nexport function getFixedSize(codec: { fixedSize: number } | { maxSize?: number }): number | null {\n    return isFixedSize(codec) ? codec.fixedSize : null;\n}\n\nexport function getMaxSize(codec: { fixedSize: number } | { maxSize?: number }): number | null {\n    return isFixedSize(codec) ? codec.fixedSize : codec.maxSize ?? null;\n}\n", "import {\n    assertByteArrayHasEnoughBytesForCodec,\n    combineCodec,\n    createDecoder,\n    createEncoder,\n    FixedSizeCodec,\n    FixedSizeDecoder,\n    FixedSizeEncoder,\n} from '@solana/codecs-core';\n\n/** Defines the config for bitArray codecs. */\nexport type BitArrayCodecConfig = {\n    /**\n     * Whether to read the bits in reverse order.\n     * @defaultValue `false`\n     */\n    backward?: boolean;\n};\n\n/**\n * Encodes an array of booleans into bits.\n *\n * @param size - The amount of bytes to use for the bit array.\n * @param config - A set of config for the encoder.\n */\nexport function getBitArrayEncoder<TSize extends number>(\n    size: TSize,\n    config: BitArrayCodecConfig | boolean = {},\n): FixedSizeEncoder<boolean[], TSize> {\n    const parsedConfig: BitArrayCodecConfig = typeof config === 'boolean' ? { backward: config } : config;\n    const backward = parsedConfig.backward ?? false;\n    return createEncoder({\n        fixedSize: size,\n        write(value: boolean[], bytes, offset) {\n            const bytesToAdd: number[] = [];\n\n            for (let i = 0; i < size; i += 1) {\n                let byte = 0;\n                for (let j = 0; j < 8; j += 1) {\n                    const feature = Number(value[i * 8 + j] ?? 0);\n                    byte |= feature << (backward ? j : 7 - j);\n                }\n                if (backward) {\n                    bytesToAdd.unshift(byte);\n                } else {\n                    bytesToAdd.push(byte);\n                }\n            }\n\n            bytes.set(bytesToAdd, offset);\n            return size;\n        },\n    });\n}\n\n/**\n * Decodes bits into an array of booleans.\n *\n * @param size - The amount of bytes to use for the bit array.\n * @param config - A set of config for the decoder.\n */\nexport function getBitArrayDecoder<TSize extends number>(\n    size: TSize,\n    config: BitArrayCodecConfig | boolean = {},\n): FixedSizeDecoder<boolean[], TSize> {\n    const parsedConfig: BitArrayCodecConfig = typeof config === 'boolean' ? { backward: config } : config;\n    const backward = parsedConfig.backward ?? false;\n    return createDecoder({\n        fixedSize: size,\n        read(bytes, offset) {\n            assertByteArrayHasEnoughBytesForCodec('bitArray', size, bytes, offset);\n            const booleans: boolean[] = [];\n            let slice = bytes.slice(offset, offset + size);\n            slice = backward ? slice.reverse() : slice;\n\n            slice.forEach(byte => {\n                for (let i = 0; i < 8; i += 1) {\n                    if (backward) {\n                        booleans.push(Boolean(byte & 1));\n                        byte >>= 1;\n                    } else {\n                        booleans.push(Boolean(byte & 0b1000_0000));\n                        byte <<= 1;\n                    }\n                }\n            });\n\n            return [booleans, offset + size];\n        },\n    });\n}\n\n/**\n * An array of boolean codec that converts booleans to bits and vice versa.\n *\n * @param size - The amount of bytes to use for the bit array.\n * @param config - A set of config for the codec.\n */\nexport function getBitArrayCodec<TSize extends number>(\n    size: TSize,\n    config: BitArrayCodecConfig | boolean = {},\n): FixedSizeCodec<boolean[], boolean[], TSize> {\n    return combineCodec(getBitArrayEncoder(size, config), getBitArrayDecoder(size, config));\n}\n", "import {\n    assertIsFixedSize,\n    Codec,\n    combineCodec,\n    Decoder,\n    Encoder,\n    FixedSizeCodec,\n    FixedSizeDecoder,\n    FixedSizeEncoder,\n    mapDecoder,\n    mapEncoder,\n} from '@solana/codecs-core';\nimport {\n    FixedSizeNumberCodec,\n    FixedSizeNumberDecoder,\n    FixedSizeNumberEncoder,\n    getU8Decoder,\n    getU8Encoder,\n    NumberCodec,\n    NumberDecoder,\n    NumberEncoder,\n} from '@solana/codecs-numbers';\n\n/** Defines the config for boolean codecs. */\nexport type BooleanCodecConfig<TSize extends NumberCodec | NumberEncoder | NumberDecoder> = {\n    /**\n     * The number codec to delegate to.\n     * @defaultValue u8 size.\n     */\n    size?: TSize;\n};\n\n/**\n * Encodes booleans.\n *\n * @param config - A set of config for the encoder.\n */\nexport function getBooleanEncoder(): FixedSizeEncoder<boolean, 1>;\nexport function getBooleanEncoder<TSize extends number>(\n    config: BooleanCodecConfig<NumberEncoder> & { size: FixedSizeNumberEncoder<TSize> },\n): FixedSizeEncoder<boolean, TSize>;\nexport function getBooleanEncoder(config: BooleanCodecConfig<NumberEncoder>): Encoder<boolean>;\nexport function getBooleanEncoder(config: BooleanCodecConfig<NumberEncoder> = {}): Encoder<boolean> {\n    const size = config.size ?? getU8Encoder();\n    assertIsFixedSize(size, 'Codec [bool] requires a fixed size.');\n    return mapEncoder(size, (value: boolean) => (value ? 1 : 0));\n}\n\n/**\n * Decodes booleans.\n *\n * @param config - A set of config for the decoder.\n */\nexport function getBooleanDecoder(): FixedSizeDecoder<boolean, 1>;\nexport function getBooleanDecoder<TSize extends number>(\n    config: BooleanCodecConfig<NumberDecoder> & { size: FixedSizeNumberDecoder<TSize> },\n): FixedSizeDecoder<boolean, TSize>;\nexport function getBooleanDecoder(config: BooleanCodecConfig<NumberDecoder>): Decoder<boolean>;\nexport function getBooleanDecoder(config: BooleanCodecConfig<NumberDecoder> = {}): Decoder<boolean> {\n    const size = config.size ?? getU8Decoder();\n    assertIsFixedSize(size, 'Codec [bool] requires a fixed size.');\n    return mapDecoder(size, (value: number | bigint): boolean => Number(value) === 1);\n}\n\n/**\n * Creates a boolean codec.\n *\n * @param config - A set of config for the codec.\n */\nexport function getBooleanCodec(): FixedSizeCodec<boolean, boolean, 1>;\nexport function getBooleanCodec<TSize extends number>(\n    config: BooleanCodecConfig<NumberCodec> & { size: FixedSizeNumberCodec<TSize> },\n): FixedSizeCodec<boolean, boolean, TSize>;\nexport function getBooleanCodec(config: BooleanCodecConfig<NumberCodec>): Codec<boolean>;\nexport function getBooleanCodec(config: BooleanCodecConfig<NumberCodec> = {}): Codec<boolean> {\n    return combineCodec(getBooleanEncoder(config), getBooleanDecoder(config));\n}\n", "import {\n    assertByteArrayHasEnoughBytesForCodec,\n    assertByteArrayIsNotEmptyForCodec,\n    Codec,\n    combineCodec,\n    createDecoder,\n    createEncoder,\n    Decoder,\n    Encoder,\n    fixDecoder,\n    FixedSizeCodec,\n    FixedSizeDecoder,\n    FixedSizeEncoder,\n    fixEncoder,\n    getEncodedSize,\n    VariableSizeCodec,\n    VariableSizeDecoder,\n    VariableSizeEncoder,\n} from '@solana/codecs-core';\nimport { NumberCodec, NumberDecoder, NumberEncoder } from '@solana/codecs-numbers';\n\n/** Defines the config for bytes codecs. */\nexport type BytesCodecConfig<TSize extends NumberCodec | NumberEncoder | NumberDecoder> = {\n    /**\n     * The size of the byte array. It can be one of the following:\n     * - a {@link NumberSerializer} that prefixes the byte array with its size.\n     * - a fixed number of bytes.\n     * - or `'variable'` to use the rest of the byte array.\n     * @defaultValue `'variable'`\n     */\n    size?: TSize | number | 'variable';\n};\n\n/**\n * Encodes sized bytes.\n *\n * @param config - A set of config for the encoder.\n */\nexport function getBytesEncoder<TSize extends number>(\n    config: BytesCodecConfig<NumberEncoder> & { size: TSize },\n): FixedSizeEncoder<Uint8Array, TSize>;\nexport function getBytesEncoder(config?: BytesCodecConfig<NumberEncoder>): VariableSizeEncoder<Uint8Array>;\nexport function getBytesEncoder(config: BytesCodecConfig<NumberEncoder> = {}): Encoder<Uint8Array> {\n    const size = config.size ?? 'variable';\n\n    const byteEncoder: Encoder<Uint8Array> = createEncoder({\n        getSizeFromValue: (value: Uint8Array) => value.length,\n        write: (value: Uint8Array, bytes, offset) => {\n            bytes.set(value, offset);\n            return offset + value.length;\n        },\n    });\n\n    if (size === 'variable') {\n        return byteEncoder;\n    }\n\n    if (typeof size === 'number') {\n        return fixEncoder(byteEncoder, size);\n    }\n\n    return createEncoder({\n        getSizeFromValue: (value: Uint8Array) => getEncodedSize(value.length, size) + value.length,\n        write: (value: Uint8Array, bytes, offset) => {\n            offset = size.write(value.length, bytes, offset);\n            return byteEncoder.write(value, bytes, offset);\n        },\n    });\n}\n\n/**\n * Decodes sized bytes.\n *\n * @param config - A set of config for the decoder.\n */\nexport function getBytesDecoder<TSize extends number>(\n    config: BytesCodecConfig<NumberDecoder> & { size: TSize },\n): FixedSizeDecoder<Uint8Array, TSize>;\nexport function getBytesDecoder(config?: BytesCodecConfig<NumberDecoder>): VariableSizeDecoder<Uint8Array>;\nexport function getBytesDecoder(config: BytesCodecConfig<NumberDecoder> = {}): Decoder<Uint8Array> {\n    const size = config.size ?? 'variable';\n\n    const byteDecoder: Decoder<Uint8Array> = createDecoder({\n        read: (bytes: Uint8Array, offset) => {\n            const slice = bytes.slice(offset);\n            return [slice, offset + slice.length];\n        },\n    });\n\n    if (size === 'variable') {\n        return byteDecoder;\n    }\n\n    if (typeof size === 'number') {\n        return fixDecoder(byteDecoder, size);\n    }\n\n    return createDecoder({\n        read: (bytes: Uint8Array, offset) => {\n            assertByteArrayIsNotEmptyForCodec('bytes', bytes, offset);\n            const [lengthBigInt, lengthOffset] = size.read(bytes, offset);\n            const length = Number(lengthBigInt);\n            offset = lengthOffset;\n            const contentBytes = bytes.slice(offset, offset + length);\n            assertByteArrayHasEnoughBytesForCodec('bytes', length, contentBytes);\n            const [value, contentOffset] = byteDecoder.read(contentBytes, 0);\n            offset += contentOffset;\n            return [value, offset];\n        },\n    });\n}\n\n/**\n * Creates a sized bytes codec.\n *\n * @param config - A set of config for the codec.\n */\nexport function getBytesCodec<TSize extends number>(\n    config: BytesCodecConfig<NumberCodec> & { size: TSize },\n): FixedSizeCodec<Uint8Array, Uint8Array, TSize>;\nexport function getBytesCodec(config?: BytesCodecConfig<NumberCodec>): VariableSizeCodec<Uint8Array>;\nexport function getBytesCodec(config: BytesCodecConfig<NumberCodec> = {}): Codec<Uint8Array> {\n    return combineCodec(getBytesEncoder(config), getBytesDecoder(config));\n}\n", "import {\n    assertByteArrayIsNotEmptyForCodec,\n    Codec,\n    combineCodec,\n    createDecoder,\n    createEncoder,\n    Decoder,\n    Encoder,\n    getEncodedSize,\n    isFixedSize,\n} from '@solana/codecs-core';\nimport { getU8Decoder, getU8Encoder, NumberCodec, NumberDecoder, NumberEncoder } from '@solana/codecs-numbers';\n\nimport { getMaxSize, maxCodecSizes, sumCodecSizes } from './utils';\n\n/**\n * Defines a data enum using discriminated union types.\n *\n * @example\n * ```ts\n * type WebPageEvent =\n *   | { __kind: 'pageview', url: string }\n *   | { __kind: 'click', x: number, y: number };\n * ```\n */\nexport type DataEnum = { __kind: string };\n\n/**\n * Extracts a variant from a data enum.\n *\n * @example\n * ```ts\n * type WebPageEvent =\n *   | { __kind: 'pageview', url: string }\n *   | { __kind: 'click', x: number, y: number };\n * type ClickEvent = GetDataEnumKind<WebPageEvent, 'click'>;\n * // -> { __kind: 'click', x: number, y: number }\n * ```\n */\nexport type GetDataEnumKind<T extends DataEnum, K extends T['__kind']> = Extract<T, { __kind: K }>;\n\n/**\n * Extracts a variant from a data enum without its discriminator.\n *\n * @example\n * ```ts\n * type WebPageEvent =\n *   | { __kind: 'pageview', url: string }\n *   | { __kind: 'click', x: number, y: number };\n * type ClickEvent = GetDataEnumKindContent<WebPageEvent, 'click'>;\n * // -> { x: number, y: number }\n * ```\n */\nexport type GetDataEnumKindContent<T extends DataEnum, K extends T['__kind']> = Omit<\n    Extract<T, { __kind: K }>,\n    '__kind'\n>;\n\n/** Get the name and encoder of each variant in a data enum. */\nexport type DataEnumToEncoderTuple<TFrom extends DataEnum> = Array<\n    TFrom extends never\n        ? never\n        : [\n              TFrom['__kind'],\n              keyof Omit<TFrom, '__kind'> extends never\n                  ? Encoder<Omit<TFrom, '__kind'>> | Encoder<void>\n                  : Encoder<Omit<TFrom, '__kind'>>,\n          ]\n>;\n\n/** Get the name and decoder of each variant in a data enum. */\nexport type DataEnumToDecoderTuple<TTo extends DataEnum> = Array<\n    TTo extends never\n        ? never\n        : [\n              TTo['__kind'],\n              keyof Omit<TTo, '__kind'> extends never\n                  ? Decoder<Omit<TTo, '__kind'>> | Decoder<void>\n                  : Decoder<Omit<TTo, '__kind'>>,\n          ]\n>;\n\n/** Get the name and codec of each variant in a data enum. */\nexport type DataEnumToCodecTuple<TFrom extends DataEnum, TTo extends TFrom = TFrom> = Array<\n    TFrom extends never\n        ? never\n        : [\n              TFrom['__kind'],\n              keyof Omit<TFrom, '__kind'> extends never\n                  ? Codec<Omit<TFrom, '__kind'>, Omit<TTo, '__kind'>> | Codec<void>\n                  : Codec<Omit<TFrom, '__kind'>, Omit<TTo, '__kind'>>,\n          ]\n>;\n\n/** Defines the config for data enum codecs. */\nexport type DataEnumCodecConfig<TDiscriminator = NumberCodec | NumberEncoder | NumberDecoder> = {\n    /**\n     * The codec to use for the enum discriminator prefixing the variant.\n     * @defaultValue u8 prefix.\n     */\n    size?: TDiscriminator;\n};\n\n/**\n * Creates a data enum encoder.\n *\n * @param variants - The variant encoders of the data enum.\n * @param config - A set of config for the encoder.\n */\nexport function getDataEnumEncoder<TFrom extends DataEnum>(\n    variants: DataEnumToEncoderTuple<TFrom>,\n    config: DataEnumCodecConfig<NumberEncoder> = {},\n): Encoder<TFrom> {\n    const prefix = config.size ?? getU8Encoder();\n    const fixedSize = getDataEnumFixedSize(variants, prefix);\n    return createEncoder({\n        ...(fixedSize !== null\n            ? { fixedSize }\n            : {\n                  getSizeFromValue: (variant: TFrom) => {\n                      const discriminator = getVariantDiscriminator(variants, variant);\n                      const variantEncoder = variants[discriminator][1];\n                      return (\n                          getEncodedSize(discriminator, prefix) +\n                          getEncodedSize(variant as void & TFrom, variantEncoder)\n                      );\n                  },\n                  maxSize: getDataEnumMaxSize(variants, prefix),\n              }),\n        write: (variant: TFrom, bytes, offset) => {\n            const discriminator = getVariantDiscriminator(variants, variant);\n            offset = prefix.write(discriminator, bytes, offset);\n            const variantEncoder = variants[discriminator][1];\n            return variantEncoder.write(variant as void & TFrom, bytes, offset);\n        },\n    });\n}\n\n/**\n * Creates a data enum decoder.\n *\n * @param variants - The variant decoders of the data enum.\n * @param config - A set of config for the decoder.\n */\nexport function getDataEnumDecoder<T extends DataEnum>(\n    variants: DataEnumToDecoderTuple<T>,\n    config: DataEnumCodecConfig<NumberDecoder> = {},\n): Decoder<T> {\n    const prefix = config.size ?? getU8Decoder();\n    const fixedSize = getDataEnumFixedSize(variants, prefix);\n    return createDecoder({\n        ...(fixedSize !== null ? { fixedSize } : { maxSize: getDataEnumMaxSize(variants, prefix) }),\n        read: (bytes: Uint8Array, offset) => {\n            assertByteArrayIsNotEmptyForCodec('dataEnum', bytes, offset);\n            const [discriminator, dOffset] = prefix.read(bytes, offset);\n            offset = dOffset;\n            const variantField = variants[Number(discriminator)] ?? null;\n            if (!variantField) {\n                // TODO: Coded error.\n                throw new Error(\n                    `Enum discriminator out of range. ` +\n                        `Expected a number between 0 and ${variants.length - 1}, got ${discriminator}.`,\n                );\n            }\n            const [variant, vOffset] = variantField[1].read(bytes, offset);\n            offset = vOffset;\n            return [{ __kind: variantField[0], ...(variant ?? {}) } as T, offset];\n        },\n    });\n}\n\n/**\n * Creates a data enum codec.\n *\n * @param variants - The variant codecs of the data enum.\n * @param config - A set of config for the codec.\n */\nexport function getDataEnumCodec<T extends DataEnum, U extends T = T>(\n    variants: DataEnumToCodecTuple<T, U>,\n    config: DataEnumCodecConfig<NumberCodec> = {},\n): Codec<T, U> {\n    return combineCodec(getDataEnumEncoder<T>(variants, config), getDataEnumDecoder<U>(variants, config));\n}\n\nfunction getDataEnumFixedSize<T extends DataEnum>(\n    variants: DataEnumToEncoderTuple<T> | DataEnumToDecoderTuple<T>,\n    prefix: { fixedSize: number } | object,\n): number | null {\n    if (variants.length === 0) return isFixedSize(prefix) ? prefix.fixedSize : null;\n    if (!isFixedSize(variants[0][1])) return null;\n    const variantSize = variants[0][1].fixedSize;\n    const sameSizedVariants = variants.every(\n        variant => isFixedSize(variant[1]) && variant[1].fixedSize === variantSize,\n    );\n    if (!sameSizedVariants) return null;\n    return isFixedSize(prefix) ? prefix.fixedSize + variantSize : null;\n}\n\nfunction getDataEnumMaxSize<T extends DataEnum>(\n    variants: DataEnumToEncoderTuple<T> | DataEnumToDecoderTuple<T>,\n    prefix: { fixedSize: number } | object,\n) {\n    const maxVariantSize = maxCodecSizes(variants.map(([, codec]) => getMaxSize(codec)));\n    return sumCodecSizes([getMaxSize(prefix), maxVariantSize]) ?? undefined;\n}\n\nfunction getVariantDiscriminator<TFrom extends DataEnum>(variants: DataEnumToEncoderTuple<TFrom>, variant: TFrom) {\n    const discriminator = variants.findIndex(([key]) => variant.__kind === key);\n    if (discriminator < 0) {\n        // TODO: Coded error.\n        throw new Error(\n            `Invalid data enum variant. ` +\n                `Expected one of [${variants.map(([key]) => key).join(', ')}], ` +\n                `got \"${variant.__kind}\".`,\n        );\n    }\n    return discriminator;\n}\n", "import {\n    Codec,\n    combineCodec,\n    Decoder,\n    Encoder,\n    FixedSizeCodec,\n    FixedSizeDecoder,\n    FixedSizeEncoder,\n    mapDecoder,\n    mapEncoder,\n    VariableSizeCodec,\n    VariableSizeDecoder,\n    VariableSizeEncoder,\n} from '@solana/codecs-core';\nimport { NumberCodec, NumberDecoder, NumberEncoder } from '@solana/codecs-numbers';\n\nimport { ArrayLikeCodecSize, getArrayDecoder, getArrayEncoder } from './array';\nimport { getTupleDecoder, getTupleEncoder } from './tuple';\n\n/** Defines the config for Map codecs. */\nexport type MapCodecConfig<TPrefix extends NumberCodec | NumberEncoder | NumberDecoder> = {\n    /**\n     * The size of the array.\n     * @defaultValue u32 prefix.\n     */\n    size?: ArrayLikeCodecSize<TPrefix>;\n};\n\n/**\n * Creates a encoder for a map.\n *\n * @param key - The encoder to use for the map's keys.\n * @param value - The encoder to use for the map's values.\n * @param config - A set of config for the encoder.\n */\nexport function getMapEncoder<TFromKey, TFromValue>(\n    key: Encoder<TFromKey>,\n    value: Encoder<TFromValue>,\n    config: MapCodecConfig<NumberEncoder> & { size: 0 },\n): FixedSizeEncoder<Map<TFromKey, TFromValue>, 0>;\nexport function getMapEncoder<TFromKey, TFromValue>(\n    key: FixedSizeEncoder<TFromKey>,\n    value: FixedSizeEncoder<TFromValue>,\n    config: MapCodecConfig<NumberEncoder> & { size: number },\n): FixedSizeEncoder<Map<TFromKey, TFromValue>>;\nexport function getMapEncoder<TFromKey, TFromValue>(\n    key: FixedSizeEncoder<TFromKey>,\n    value: FixedSizeEncoder<TFromValue>,\n    config: MapCodecConfig<NumberEncoder> & { size: 'remainder' },\n): VariableSizeEncoder<Map<TFromKey, TFromValue>>;\nexport function getMapEncoder<TFromKey, TFromValue>(\n    key: Encoder<TFromKey>,\n    value: Encoder<TFromValue>,\n    config?: MapCodecConfig<NumberEncoder> & { size?: number | NumberEncoder },\n): VariableSizeEncoder<Map<TFromKey, TFromValue>>;\nexport function getMapEncoder<TFromKey, TFromValue>(\n    key: Encoder<TFromKey>,\n    value: Encoder<TFromValue>,\n    config: MapCodecConfig<NumberEncoder> = {},\n): Encoder<Map<TFromKey, TFromValue>> {\n    return mapEncoder(\n        getArrayEncoder(getTupleEncoder([key, value]), config as object),\n        (map: Map<TFromKey, TFromValue>): [TFromKey, TFromValue][] => [...map.entries()],\n    );\n}\n\n/**\n * Creates a decoder for a map.\n *\n * @param key - The decoder to use for the map's keys.\n * @param value - The decoder to use for the map's values.\n * @param config - A set of config for the decoder.\n */\nexport function getMapDecoder<TToKey, TToValue>(\n    key: Decoder<TToKey>,\n    value: Decoder<TToValue>,\n    config: MapCodecConfig<NumberDecoder> & { size: 0 },\n): FixedSizeDecoder<Map<TToKey, TToValue>, 0>;\nexport function getMapDecoder<TToKey, TToValue>(\n    key: FixedSizeDecoder<TToKey>,\n    value: FixedSizeDecoder<TToValue>,\n    config: MapCodecConfig<NumberDecoder> & { size: number },\n): FixedSizeDecoder<Map<TToKey, TToValue>>;\nexport function getMapDecoder<TToKey, TToValue>(\n    key: FixedSizeDecoder<TToKey>,\n    value: FixedSizeDecoder<TToValue>,\n    config: MapCodecConfig<NumberDecoder> & { size: 'remainder' },\n): VariableSizeDecoder<Map<TToKey, TToValue>>;\nexport function getMapDecoder<TToKey, TToValue>(\n    key: Decoder<TToKey>,\n    value: Decoder<TToValue>,\n    config?: MapCodecConfig<NumberDecoder> & { size?: number | NumberDecoder },\n): VariableSizeDecoder<Map<TToKey, TToValue>>;\nexport function getMapDecoder<TToKey, TToValue>(\n    key: Decoder<TToKey>,\n    value: Decoder<TToValue>,\n    config: MapCodecConfig<NumberDecoder> = {},\n): Decoder<Map<TToKey, TToValue>> {\n    return mapDecoder(\n        getArrayDecoder(getTupleDecoder([key, value]), config as object),\n        (entries: [TToKey, TToValue][]): Map<TToKey, TToValue> => new Map(entries),\n    );\n}\n\n/**\n * Creates a codec for a map.\n *\n * @param key - The codec to use for the map's keys.\n * @param value - The codec to use for the map's values.\n * @param config - A set of config for the codec.\n */\nexport function getMapCodec<\n    TFromKey,\n    TFromValue,\n    TToKey extends TFromKey = TFromKey,\n    TToValue extends TFromValue = TFromValue,\n>(\n    key: Codec<TFromKey, TToKey>,\n    value: Codec<TFromValue, TToValue>,\n    config: MapCodecConfig<NumberCodec> & { size: 0 },\n): FixedSizeCodec<Map<TFromKey, TFromValue>, Map<TToKey, TToValue>, 0>;\nexport function getMapCodec<\n    TFromKey,\n    TFromValue,\n    TToKey extends TFromKey = TFromKey,\n    TToValue extends TFromValue = TFromValue,\n>(\n    key: FixedSizeCodec<TFromKey, TToKey>,\n    value: FixedSizeCodec<TFromValue, TToValue>,\n    config: MapCodecConfig<NumberCodec> & { size: number },\n): FixedSizeCodec<Map<TFromKey, TFromValue>, Map<TToKey, TToValue>>;\nexport function getMapCodec<\n    TFromKey,\n    TFromValue,\n    TToKey extends TFromKey = TFromKey,\n    TToValue extends TFromValue = TFromValue,\n>(\n    key: FixedSizeCodec<TFromKey, TToKey>,\n    value: FixedSizeCodec<TFromValue, TToValue>,\n    config: MapCodecConfig<NumberCodec> & { size: 'remainder' },\n): VariableSizeCodec<Map<TFromKey, TFromValue>, Map<TToKey, TToValue>>;\nexport function getMapCodec<\n    TFromKey,\n    TFromValue,\n    TToKey extends TFromKey = TFromKey,\n    TToValue extends TFromValue = TFromValue,\n>(\n    key: Codec<TFromKey, TToKey>,\n    value: Codec<TFromValue, TToValue>,\n    config?: MapCodecConfig<NumberCodec> & { size?: number | NumberCodec },\n): VariableSizeCodec<Map<TFromKey, TFromValue>, Map<TToKey, TToValue>>;\nexport function getMapCodec<\n    TFromKey,\n    TFromValue,\n    TToKey extends TFromKey = TFromKey,\n    TToValue extends TFromValue = TFromValue,\n>(\n    key: Codec<TFromKey, TToKey>,\n    value: Codec<TFromValue, TToValue>,\n    config: MapCodecConfig<NumberCodec> = {},\n): Codec<Map<TFromKey, TFromValue>, Map<TToKey, TToValue>> {\n    return combineCodec(getMapEncoder(key, value, config as object), getMapDecoder(key, value, config as object));\n}\n", "import {\n    Codec,\n    combineCodec,\n    createDecoder,\n    createEncoder,\n    Decoder,\n    Encoder,\n    FixedSizeCodec,\n    FixedSizeDecoder,\n    FixedSizeEncoder,\n    getEncodedSize,\n    VariableSizeCodec,\n    VariableSizeDecoder,\n    VariableSizeEncoder,\n} from '@solana/codecs-core';\n\nimport { assertValidNumberOfItemsForCodec } from './assertions';\nimport { getFixedSize, getMaxSize, sumCodecSizes } from './utils';\n\ntype WrapInFixedSizeEncoder<TFrom> = {\n    [P in keyof TFrom]: FixedSizeEncoder<TFrom[P]>;\n};\ntype WrapInEncoder<TFrom> = {\n    [P in keyof TFrom]: Encoder<TFrom[P]>;\n};\ntype WrapInFixedSizeDecoder<TTo> = {\n    [P in keyof TTo]: FixedSizeDecoder<TTo[P]>;\n};\ntype WrapInDecoder<TTo> = {\n    [P in keyof TTo]: Decoder<TTo[P]>;\n};\ntype WrapInCodec<TFrom, TTo extends TFrom> = {\n    [P in keyof TFrom]: Codec<TFrom[P], TTo[P]>;\n};\ntype WrapInFixedSizeCodec<TFrom, TTo extends TFrom> = {\n    [P in keyof TFrom]: FixedSizeCodec<TFrom[P], TTo[P]>;\n};\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\ntype AnyArray = any[];\n\n/**\n * Creates a encoder for a tuple-like array.\n *\n * @param items - The encoders to use for each item in the tuple.\n */\nexport function getTupleEncoder<TFrom extends AnyArray>(\n    items: WrapInFixedSizeEncoder<[...TFrom]>,\n): FixedSizeEncoder<TFrom>;\nexport function getTupleEncoder<TFrom extends AnyArray>(items: WrapInEncoder<[...TFrom]>): VariableSizeEncoder<TFrom>;\nexport function getTupleEncoder<TFrom extends AnyArray>(items: WrapInEncoder<[...TFrom]>): Encoder<TFrom> {\n    const fixedSize = sumCodecSizes(items.map(getFixedSize));\n    const maxSize = sumCodecSizes(items.map(getMaxSize)) ?? undefined;\n\n    return createEncoder({\n        ...(fixedSize === null\n            ? {\n                  getSizeFromValue: (value: TFrom) =>\n                      items.map((item, index) => getEncodedSize(value[index], item)).reduce((all, one) => all + one, 0),\n                  maxSize,\n              }\n            : { fixedSize }),\n        write: (value: TFrom, bytes, offset) => {\n            assertValidNumberOfItemsForCodec('tuple', items.length, value.length);\n            items.forEach((item, index) => {\n                offset = item.write(value[index], bytes, offset);\n            });\n            return offset;\n        },\n    });\n}\n\n/**\n * Creates a decoder for a tuple-like array.\n *\n * @param items - The decoders to use for each item in the tuple.\n */\nexport function getTupleDecoder<TTo extends AnyArray>(items: WrapInFixedSizeDecoder<[...TTo]>): FixedSizeDecoder<TTo>;\nexport function getTupleDecoder<TTo extends AnyArray>(items: WrapInDecoder<[...TTo]>): VariableSizeDecoder<TTo>;\nexport function getTupleDecoder<TTo extends AnyArray>(items: WrapInDecoder<[...TTo]>): Decoder<TTo> {\n    const fixedSize = sumCodecSizes(items.map(getFixedSize));\n    const maxSize = sumCodecSizes(items.map(getMaxSize)) ?? undefined;\n\n    return createDecoder({\n        ...(fixedSize === null ? { maxSize } : { fixedSize }),\n        read: (bytes: Uint8Array, offset) => {\n            const values = [] as AnyArray as TTo;\n            items.forEach(item => {\n                const [newValue, newOffset] = item.read(bytes, offset);\n                values.push(newValue);\n                offset = newOffset;\n            });\n            return [values, offset];\n        },\n    });\n}\n\n/**\n * Creates a codec for a tuple-like array.\n *\n * @param items - The codecs to use for each item in the tuple.\n */\nexport function getTupleCodec<TFrom extends AnyArray, TTo extends TFrom = TFrom>(\n    items: WrapInFixedSizeCodec<[...TFrom], [...TTo]>,\n): FixedSizeCodec<TFrom, TTo>;\nexport function getTupleCodec<TFrom extends AnyArray, TTo extends TFrom = TFrom>(\n    items: WrapInCodec<[...TFrom], [...TTo]>,\n): VariableSizeCodec<TFrom, TTo>;\nexport function getTupleCodec<TFrom extends AnyArray, TTo extends TFrom = TFrom>(\n    items: WrapInCodec<[...TFrom], [...TTo]>,\n): Codec<TFrom, TTo> {\n    return combineCodec(\n        getTupleEncoder(items as WrapInEncoder<[...TFrom]>),\n        getTupleDecoder(items as WrapInDecoder<[...TTo]>),\n    );\n}\n", "import {\n    assertIsFixedSize,\n    Codec,\n    combineCodec,\n    createDecoder,\n    createEnco<PERSON>,\n    Decoder,\n    Encoder,\n    FixedSizeCodec,\n    FixedSizeDecoder,\n    FixedSizeEncoder,\n    getEncodedSize,\n    isFixedSize,\n    VariableSizeCodec,\n    VariableSizeDecoder,\n    VariableSizeEncoder,\n} from '@solana/codecs-core';\nimport {\n    FixedSizeNumberCodec,\n    FixedSizeNumberDecoder,\n    FixedSizeNumberEncoder,\n    getU8Decoder,\n    getU8Encoder,\n    NumberCodec,\n    NumberDecoder,\n    NumberEncoder,\n} from '@solana/codecs-numbers';\n\nimport { getMaxSize, sumCodecSizes } from './utils';\n\n/** Defines the config for nullable codecs. */\nexport type NullableCodecConfig<TPrefix extends NumberCodec | NumberEncoder | NumberDecoder> = {\n    /**\n     * The codec to use for the boolean prefix.\n     * @defaultValue u8 prefix.\n     */\n    prefix?: TPrefix;\n\n    /**\n     * Whether the item codec should be of fixed size.\n     *\n     * When this is true, a `null` value will skip the bytes that would\n     * have been used for the item. Note that this will only work if the\n     * item codec is of fixed size.\n     * @defaultValue `false`\n     */\n    fixed?: boolean;\n};\n\n/**\n * Creates a encoder for an optional value using `null` as the `None` value.\n *\n * @param item - The encoder to use for the value that may be present.\n * @param config - A set of config for the encoder.\n */\nexport function getNullableEncoder<TFrom>(\n    item: FixedSizeEncoder<TFrom>,\n    config: NullableCodecConfig<FixedSizeNumberEncoder> & { fixed: true },\n): FixedSizeEncoder<TFrom | null>;\nexport function getNullableEncoder<TFrom>(\n    item: FixedSizeEncoder<TFrom, 0>,\n    config?: NullableCodecConfig<FixedSizeNumberEncoder>,\n): FixedSizeEncoder<TFrom | null>;\nexport function getNullableEncoder<TFrom>(\n    item: Encoder<TFrom>,\n    config?: NullableCodecConfig<NumberEncoder> & { fixed?: false },\n): VariableSizeEncoder<TFrom | null>;\nexport function getNullableEncoder<TFrom>(\n    item: Encoder<TFrom>,\n    config: NullableCodecConfig<NumberEncoder> = {},\n): Encoder<TFrom | null> {\n    const prefix = config.prefix ?? getU8Encoder();\n    const fixed = config.fixed ?? false;\n\n    const isZeroSizeItem = isFixedSize(item) && isFixedSize(prefix) && item.fixedSize === 0;\n    if (fixed || isZeroSizeItem) {\n        assertIsFixedSize(item, 'Fixed nullables can only be used with fixed-size codecs.');\n        assertIsFixedSize(prefix, 'Fixed nullables can only be used with fixed-size prefix.');\n        const fixedSize = prefix.fixedSize + item.fixedSize;\n        return createEncoder({\n            fixedSize,\n            write: (option: TFrom | null, bytes, offset) => {\n                const prefixOffset = prefix.write(Number(option !== null), bytes, offset);\n                if (option !== null) {\n                    item.write(option, bytes, prefixOffset);\n                }\n                return offset + fixedSize;\n            },\n        });\n    }\n\n    return createEncoder({\n        getSizeFromValue: (option: TFrom | null) =>\n            getEncodedSize(Number(option !== null), prefix) + (option !== null ? getEncodedSize(option, item) : 0),\n        maxSize: sumCodecSizes([prefix, item].map(getMaxSize)) ?? undefined,\n        write: (option: TFrom | null, bytes, offset) => {\n            offset = prefix.write(Number(option !== null), bytes, offset);\n            if (option !== null) {\n                offset = item.write(option, bytes, offset);\n            }\n            return offset;\n        },\n    });\n}\n\n/**\n * Creates a decoder for an optional value using `null` as the `None` value.\n *\n * @param item - The decoder to use for the value that may be present.\n * @param config - A set of config for the decoder.\n */\nexport function getNullableDecoder<TTo>(\n    item: FixedSizeDecoder<TTo>,\n    config: NullableCodecConfig<FixedSizeNumberDecoder> & { fixed: true },\n): FixedSizeDecoder<TTo | null>;\nexport function getNullableDecoder<TTo>(\n    item: FixedSizeDecoder<TTo, 0>,\n    config?: NullableCodecConfig<FixedSizeNumberDecoder>,\n): FixedSizeDecoder<TTo | null>;\nexport function getNullableDecoder<TTo>(\n    item: Decoder<TTo>,\n    config?: NullableCodecConfig<NumberDecoder> & { fixed?: false },\n): VariableSizeDecoder<TTo | null>;\nexport function getNullableDecoder<TTo>(\n    item: Decoder<TTo>,\n    config: NullableCodecConfig<NumberDecoder> = {},\n): Decoder<TTo | null> {\n    const prefix = config.prefix ?? getU8Decoder();\n    const fixed = config.fixed ?? false;\n\n    let fixedSize: number | null = null;\n    const isZeroSizeItem = isFixedSize(item) && isFixedSize(prefix) && item.fixedSize === 0;\n    if (fixed || isZeroSizeItem) {\n        assertIsFixedSize(item, 'Fixed nullables can only be used with fixed-size codecs.');\n        assertIsFixedSize(prefix, 'Fixed nullables can only be used with fixed-size prefix.');\n        fixedSize = prefix.fixedSize + item.fixedSize;\n    }\n\n    return createDecoder({\n        ...(fixedSize === null\n            ? { maxSize: sumCodecSizes([prefix, item].map(getMaxSize)) ?? undefined }\n            : { fixedSize }),\n        read: (bytes: Uint8Array, offset) => {\n            if (bytes.length - offset <= 0) {\n                return [null, offset];\n            }\n            const [isSome, prefixOffset] = prefix.read(bytes, offset);\n            if (isSome === 0) {\n                return [null, fixedSize !== null ? offset + fixedSize : prefixOffset];\n            }\n            const [value, newOffset] = item.read(bytes, prefixOffset);\n            return [value, fixedSize !== null ? offset + fixedSize : newOffset];\n        },\n    });\n}\n\n/**\n * Creates a codec for an optional value using `null` as the `None` value.\n *\n * @param item - The codec to use for the value that may be present.\n * @param config - A set of config for the codec.\n */\nexport function getNullableCodec<TFrom, TTo extends TFrom = TFrom>(\n    item: FixedSizeCodec<TFrom, TTo>,\n    config: NullableCodecConfig<FixedSizeNumberCodec> & { fixed: true },\n): FixedSizeCodec<TFrom | null, TTo | null>;\nexport function getNullableCodec<TFrom, TTo extends TFrom = TFrom>(\n    item: FixedSizeCodec<TFrom, TTo, 0>,\n    config?: NullableCodecConfig<FixedSizeNumberCodec>,\n): FixedSizeCodec<TFrom | null, TTo | null>;\nexport function getNullableCodec<TFrom, TTo extends TFrom = TFrom>(\n    item: Codec<TFrom, TTo>,\n    config?: NullableCodecConfig<NumberCodec> & { fixed?: false },\n): VariableSizeCodec<TFrom | null, TTo | null>;\nexport function getNullableCodec<TFrom, TTo extends TFrom = TFrom>(\n    item: Codec<TFrom, TTo>,\n    config: NullableCodecConfig<NumberCodec> = {},\n): Codec<TFrom | null, TTo | null> {\n    const configCast = config as NullableCodecConfig<NumberCodec> & { fixed?: false };\n    return combineCodec(getNullableEncoder<TFrom>(item, configCast), getNullableDecoder<TTo>(item, configCast));\n}\n", "import {\n    Codec,\n    combineCodec,\n    Decoder,\n    Encoder,\n    FixedSizeCodec,\n    FixedSizeDecoder,\n    FixedSizeEncoder,\n    mapDecoder,\n    mapEncoder,\n    VariableSizeCodec,\n    VariableSizeDecoder,\n    VariableSizeEncoder,\n} from '@solana/codecs-core';\nimport {\n    FixedSizeNumberCodec,\n    FixedSizeNumberDecoder,\n    FixedSizeNumberEncoder,\n    getU8Decoder,\n    getU8Encoder,\n    NumberCodec,\n    NumberDecoder,\n    NumberEncoder,\n} from '@solana/codecs-numbers';\n\n/**\n * Defines a scalar enum as a type from its constructor.\n *\n * @example\n * ```ts\n * enum Direction { Left, Right };\n * type DirectionType = ScalarEnum<Direction>;\n * ```\n */\nexport type ScalarEnum<T> = ({ [key: number | string]: string | number | T } | number | T) & NonNullable<unknown>;\n\n/** Defines the config for scalar enum codecs. */\nexport type ScalarEnumCodecConfig<TDiscriminator extends NumberCodec | NumberEncoder | NumberDecoder> = {\n    /**\n     * The codec to use for the enum discriminator.\n     * @defaultValue u8 discriminator.\n     */\n    size?: TDiscriminator;\n};\n\n/**\n * Creates a scalar enum encoder.\n *\n * @param constructor - The constructor of the scalar enum.\n * @param config - A set of config for the encoder.\n */\nexport function getScalarEnumEncoder<TFrom, TFromConstructor extends ScalarEnum<TFrom>>(\n    constructor: TFromConstructor,\n): FixedSizeEncoder<TFrom, 1>;\nexport function getScalarEnumEncoder<TFrom, TFromConstructor extends ScalarEnum<TFrom>, TSize extends number>(\n    constructor: TFromConstructor,\n    config: ScalarEnumCodecConfig<NumberEncoder> & { size: FixedSizeNumberEncoder<TSize> },\n): FixedSizeEncoder<TFrom, TSize>;\nexport function getScalarEnumEncoder<TFrom, TFromConstructor extends ScalarEnum<TFrom>>(\n    constructor: TFromConstructor,\n    config?: ScalarEnumCodecConfig<NumberEncoder>,\n): VariableSizeEncoder<TFrom>;\nexport function getScalarEnumEncoder<TFrom, TFromConstructor extends ScalarEnum<TFrom>>(\n    constructor: TFromConstructor,\n    config: ScalarEnumCodecConfig<NumberEncoder> = {},\n): Encoder<TFrom> {\n    const prefix = config.size ?? getU8Encoder();\n    const { minRange, maxRange, stringValues, enumKeys, enumValues } = getScalarEnumStats(constructor);\n    return mapEncoder(prefix, (value: TFrom): number => {\n        const isInvalidNumber = typeof value === 'number' && (value < minRange || value > maxRange);\n        const isInvalidString = typeof value === 'string' && !stringValues.includes(value);\n        if (isInvalidNumber || isInvalidString) {\n            // TODO: Coded error.\n            throw new Error(\n                `Invalid scalar enum variant. ` +\n                    `Expected one of [${stringValues.join(', ')}] ` +\n                    `or a number between ${minRange} and ${maxRange}, ` +\n                    `got \"${value}\".`,\n            );\n        }\n        if (typeof value === 'number') return value;\n        const valueIndex = enumValues.indexOf(value);\n        if (valueIndex >= 0) return valueIndex;\n        return enumKeys.indexOf(value as string);\n    });\n}\n\n/**\n * Creates a scalar enum decoder.\n *\n * @param constructor - The constructor of the scalar enum.\n * @param config - A set of config for the decoder.\n */\nexport function getScalarEnumDecoder<TTo, TToConstructor extends ScalarEnum<TTo>>(\n    constructor: TToConstructor,\n): FixedSizeDecoder<TTo, 1>;\nexport function getScalarEnumDecoder<TTo, TToConstructor extends ScalarEnum<TTo>, TSize extends number>(\n    constructor: TToConstructor,\n    config: ScalarEnumCodecConfig<NumberDecoder> & { size: FixedSizeNumberDecoder<TSize> },\n): FixedSizeDecoder<TTo, TSize>;\nexport function getScalarEnumDecoder<TTo, TToConstructor extends ScalarEnum<TTo>>(\n    constructor: TToConstructor,\n    config?: ScalarEnumCodecConfig<NumberDecoder>,\n): VariableSizeDecoder<TTo>;\nexport function getScalarEnumDecoder<TTo, TToConstructor extends ScalarEnum<TTo>>(\n    constructor: TToConstructor,\n    config: ScalarEnumCodecConfig<NumberDecoder> = {},\n): Decoder<TTo> {\n    const prefix = config.size ?? getU8Decoder();\n    const { minRange, maxRange, isNumericEnum, enumValues } = getScalarEnumStats(constructor);\n    return mapDecoder(prefix, (value: number | bigint): TTo => {\n        const valueAsNumber = Number(value);\n        if (valueAsNumber < minRange || valueAsNumber > maxRange) {\n            // TODO: Coded error.\n            throw new Error(\n                `Enum discriminator out of range. ` +\n                    `Expected a number between ${minRange} and ${maxRange}, got ${valueAsNumber}.`,\n            );\n        }\n        return (isNumericEnum ? valueAsNumber : enumValues[valueAsNumber]) as TTo;\n    });\n}\n\n/**\n * Creates a scalar enum codec.\n *\n * @param constructor - The constructor of the scalar enum.\n * @param config - A set of config for the codec.\n */\nexport function getScalarEnumCodec<TFrom, TFromConstructor extends ScalarEnum<TFrom>>(\n    constructor: TFromConstructor,\n): FixedSizeCodec<TFrom, TFrom, 1>;\nexport function getScalarEnumCodec<TFrom, TFromConstructor extends ScalarEnum<TFrom>, TSize extends number>(\n    constructor: TFromConstructor,\n    config: ScalarEnumCodecConfig<NumberCodec> & { size: FixedSizeNumberCodec<TSize> },\n): FixedSizeCodec<TFrom, TFrom, TSize>;\nexport function getScalarEnumCodec<TFrom, TFromConstructor extends ScalarEnum<TFrom>>(\n    constructor: TFromConstructor,\n    config?: ScalarEnumCodecConfig<NumberCodec>,\n): VariableSizeCodec<TFrom>;\nexport function getScalarEnumCodec<TFrom, TFromConstructor extends ScalarEnum<TFrom>>(\n    constructor: TFromConstructor,\n    config: ScalarEnumCodecConfig<NumberCodec> = {},\n): Codec<TFrom> {\n    return combineCodec(getScalarEnumEncoder(constructor, config), getScalarEnumDecoder(constructor, config));\n}\n\nfunction getScalarEnumStats<TFrom>(constructor: ScalarEnum<TFrom>): {\n    enumKeys: string[];\n    enumValues: TFrom[];\n    isNumericEnum: boolean;\n    minRange: number;\n    maxRange: number;\n    stringValues: string[];\n} {\n    const enumKeys = Object.keys(constructor);\n    const enumValues = Object.values(constructor);\n    const isNumericEnum = enumValues.some(v => typeof v === 'number');\n    const minRange = 0;\n    const maxRange = isNumericEnum ? enumValues.length / 2 - 1 : enumValues.length - 1;\n    const stringValues: string[] = isNumericEnum ? [...enumKeys] : [...new Set([...enumKeys, ...enumValues])];\n\n    return {\n        enumKeys,\n        enumValues,\n        isNumericEnum,\n        maxRange,\n        minRange,\n        stringValues,\n    };\n}\n", "import {\n    Codec,\n    combineCodec,\n    Decoder,\n    Encoder,\n    FixedSizeCodec,\n    FixedSizeDecoder,\n    FixedSizeEncoder,\n    mapDecoder,\n    mapEncoder,\n    VariableSizeCodec,\n    VariableSizeDecoder,\n    VariableSizeEncoder,\n} from '@solana/codecs-core';\nimport { NumberCodec, NumberDecoder, NumberEncoder } from '@solana/codecs-numbers';\n\nimport { ArrayLikeCodecSize, getArrayDecoder, getArrayEncoder } from './array';\n\n/** Defines the config for set codecs. */\nexport type SetCodecConfig<TPrefix extends NumberCodec | NumberEncoder | NumberDecoder> = {\n    /**\n     * The size of the set.\n     * @defaultValue u32 prefix.\n     */\n    size?: ArrayLikeCodecSize<TPrefix>;\n};\n\n/**\n * Encodes an set of items.\n *\n * @param item - The encoder to use for the set's items.\n * @param config - A set of config for the encoder.\n */\nexport function getSetEncoder<TFrom>(\n    item: Encoder<TFrom>,\n    config: SetCodecConfig<NumberEncoder> & { size: 0 },\n): FixedSizeEncoder<Set<TFrom>, 0>;\nexport function getSetEncoder<TFrom>(\n    item: FixedSizeEncoder<TFrom>,\n    config: SetCodecConfig<NumberEncoder> & { size: number },\n): FixedSizeEncoder<Set<TFrom>>;\nexport function getSetEncoder<TFrom>(\n    item: FixedSizeEncoder<TFrom>,\n    config: SetCodecConfig<NumberEncoder> & { size: 'remainder' },\n): VariableSizeEncoder<Set<TFrom>>;\nexport function getSetEncoder<TFrom>(\n    item: Encoder<TFrom>,\n    config?: SetCodecConfig<NumberEncoder> & { size?: number | NumberEncoder },\n): VariableSizeEncoder<Set<TFrom>>;\nexport function getSetEncoder<TFrom>(\n    item: Encoder<TFrom>,\n    config: SetCodecConfig<NumberEncoder> = {},\n): Encoder<Set<TFrom>> {\n    return mapEncoder(getArrayEncoder(item, config as object), (set: Set<TFrom>): TFrom[] => [...set]);\n}\n\n/**\n * Decodes an set of items.\n *\n * @param item - The encoder to use for the set's items.\n * @param config - A set of config for the encoder.\n */\nexport function getSetDecoder<TTo>(\n    item: Decoder<TTo>,\n    config: SetCodecConfig<NumberDecoder> & { size: 0 },\n): FixedSizeDecoder<Set<TTo>, 0>;\nexport function getSetDecoder<TTo>(\n    item: FixedSizeDecoder<TTo>,\n    config: SetCodecConfig<NumberDecoder> & { size: number },\n): FixedSizeDecoder<Set<TTo>>;\nexport function getSetDecoder<TTo>(\n    item: FixedSizeDecoder<TTo>,\n    config: SetCodecConfig<NumberDecoder> & { size: 'remainder' },\n): VariableSizeDecoder<Set<TTo>>;\nexport function getSetDecoder<TTo>(\n    item: Decoder<TTo>,\n    config?: SetCodecConfig<NumberDecoder> & { size?: number | NumberDecoder },\n): VariableSizeDecoder<Set<TTo>>;\nexport function getSetDecoder<TTo>(item: Decoder<TTo>, config: SetCodecConfig<NumberDecoder> = {}): Decoder<Set<TTo>> {\n    return mapDecoder(getArrayDecoder(item, config as object), (entries: TTo[]): Set<TTo> => new Set(entries));\n}\n\n/**\n * Creates a codec for an set of items.\n *\n * @param item - The codec to use for the set's items.\n * @param config - A set of config for the codec.\n */\nexport function getSetCodec<TFrom, TTo extends TFrom = TFrom>(\n    item: Codec<TFrom, TTo>,\n    config: SetCodecConfig<NumberCodec> & { size: 0 },\n): FixedSizeCodec<Set<TFrom>, Set<TTo>, 0>;\nexport function getSetCodec<TFrom, TTo extends TFrom = TFrom>(\n    item: FixedSizeCodec<TFrom, TTo>,\n    config: SetCodecConfig<NumberCodec> & { size: number },\n): FixedSizeCodec<Set<TFrom>, Set<TTo>>;\nexport function getSetCodec<TFrom, TTo extends TFrom = TFrom>(\n    item: FixedSizeCodec<TFrom, TTo>,\n    config: SetCodecConfig<NumberCodec> & { size: 'remainder' },\n): VariableSizeCodec<Set<TFrom>, Set<TTo>>;\nexport function getSetCodec<TFrom, TTo extends TFrom = TFrom>(\n    item: Codec<TFrom, TTo>,\n    config?: SetCodecConfig<NumberCodec> & { size?: number | NumberCodec },\n): VariableSizeCodec<Set<TFrom>, Set<TTo>>;\nexport function getSetCodec<TFrom, TTo extends TFrom = TFrom>(\n    item: Codec<TFrom, TTo>,\n    config: SetCodecConfig<NumberCodec> = {},\n): Codec<Set<TFrom>, Set<TTo>> {\n    return combineCodec(getSetEncoder(item, config as object), getSetDecoder(item, config as object));\n}\n", "import {\n    Codec,\n    combineCodec,\n    createDecoder,\n    createEncoder,\n    Decoder,\n    Encoder,\n    FixedSizeCodec,\n    FixedSizeDecoder,\n    FixedSizeEncoder,\n    getEncodedSize,\n    VariableSizeCodec,\n    VariableSizeDecoder,\n    VariableSizeEncoder,\n} from '@solana/codecs-core';\n\nimport { getFixedSize, getMaxSize, sumCodecSizes } from './utils';\n\n/** Get the name and encoder of each field in a struct. */\nexport type StructToEncoderTuple<TFrom extends object> = Array<\n    {\n        [K in keyof TFrom]: [K, Encoder<TFrom[K]>];\n    }[keyof TFrom]\n>;\n\n/** Get the name and fixed-size encoder of each field in a struct. */\nexport type StructToFixedSizeEncoderTuple<TFrom extends object> = Array<\n    {\n        [K in keyof TFrom]: [K, FixedSizeEncoder<TFrom[K]>];\n    }[keyof TFrom]\n>;\n\n/** Get the name and decoder of each field in a struct. */\nexport type StructToDecoderTuple<TTo extends object> = Array<\n    {\n        [K in keyof TTo]: [K, Decoder<TTo[K]>];\n    }[keyof TTo]\n>;\n\n/** Get the name and fixed-size decoder of each field in a struct. */\nexport type StructToFixedSizeDecoderTuple<TTo extends object> = Array<\n    {\n        [K in keyof TTo]: [K, FixedSizeDecoder<TTo[K]>];\n    }[keyof TTo]\n>;\n\n/** Get the name and codec of each field in a struct. */\nexport type StructToCodecTuple<TFrom extends object, TTo extends TFrom> = Array<\n    {\n        [K in keyof TFrom]: [K, Codec<TFrom[K], TTo[K]>];\n    }[keyof TFrom]\n>;\n\n/** Get the name and fixed-size codec of each field in a struct. */\nexport type StructToFixedSizeCodecTuple<TFrom extends object, TTo extends TFrom> = Array<\n    {\n        [K in keyof TFrom]: [K, FixedSizeCodec<TFrom[K], TTo[K]>];\n    }[keyof TFrom]\n>;\n\n/**\n * Creates a encoder for a custom object.\n *\n * @param fields - The name and encoder of each field.\n */\nexport function getStructEncoder<TFrom extends object>(\n    fields: StructToFixedSizeEncoderTuple<TFrom>,\n): FixedSizeEncoder<TFrom>;\nexport function getStructEncoder<TFrom extends object>(fields: StructToEncoderTuple<TFrom>): VariableSizeEncoder<TFrom>;\nexport function getStructEncoder<TFrom extends object>(fields: StructToEncoderTuple<TFrom>): Encoder<TFrom> {\n    const fieldCodecs = fields.map(([, codec]) => codec);\n    const fixedSize = sumCodecSizes(fieldCodecs.map(getFixedSize));\n    const maxSize = sumCodecSizes(fieldCodecs.map(getMaxSize)) ?? undefined;\n\n    return createEncoder({\n        ...(fixedSize === null\n            ? {\n                  getSizeFromValue: (value: TFrom) =>\n                      fields\n                          .map(([key, codec]) => getEncodedSize(value[key], codec))\n                          .reduce((all, one) => all + one, 0),\n                  maxSize,\n              }\n            : { fixedSize }),\n        write: (struct: TFrom, bytes, offset) => {\n            fields.forEach(([key, codec]) => {\n                offset = codec.write(struct[key], bytes, offset);\n            });\n            return offset;\n        },\n    });\n}\n\n/**\n * Creates a decoder for a custom object.\n *\n * @param fields - The name and decoder of each field.\n */\nexport function getStructDecoder<TTo extends object>(fields: StructToFixedSizeDecoderTuple<TTo>): FixedSizeDecoder<TTo>;\nexport function getStructDecoder<TTo extends object>(fields: StructToDecoderTuple<TTo>): VariableSizeDecoder<TTo>;\nexport function getStructDecoder<TTo extends object>(fields: StructToDecoderTuple<TTo>): Decoder<TTo> {\n    const fieldCodecs = fields.map(([, codec]) => codec);\n    const fixedSize = sumCodecSizes(fieldCodecs.map(getFixedSize));\n    const maxSize = sumCodecSizes(fieldCodecs.map(getMaxSize)) ?? undefined;\n\n    return createDecoder({\n        ...(fixedSize === null ? { maxSize } : { fixedSize }),\n        read: (bytes: Uint8Array, offset) => {\n            const struct: Partial<TTo> = {};\n            fields.forEach(([key, codec]) => {\n                const [value, newOffset] = codec.read(bytes, offset);\n                offset = newOffset;\n                struct[key] = value;\n            });\n            return [struct as TTo, offset];\n        },\n    });\n}\n\n/**\n * Creates a codec for a custom object.\n *\n * @param fields - The name and codec of each field.\n */\nexport function getStructCodec<TFrom extends object, TTo extends TFrom = TFrom>(\n    fields: StructToFixedSizeCodecTuple<TFrom, TTo>,\n): FixedSizeCodec<TFrom, TTo>;\nexport function getStructCodec<TFrom extends object, TTo extends TFrom = TFrom>(\n    fields: StructToCodecTuple<TFrom, TTo>,\n): VariableSizeCodec<TFrom, TTo>;\nexport function getStructCodec<TFrom extends object, TTo extends TFrom = TFrom>(\n    fields: StructToCodecTuple<TFrom, TTo>,\n): Codec<TFrom, TTo> {\n    return combineCodec(getStructEncoder(fields), getStructDecoder(fields));\n}\n", "import {\n    combineCodec,\n    createDecoder,\n    createEncoder,\n    FixedSizeCodec,\n    FixedSizeDecoder,\n    FixedSizeEncoder,\n} from '@solana/codecs-core';\n\n/**\n * Creates a void encoder.\n */\nexport function getUnitEncoder(): FixedSizeEncoder<void, 0> {\n    return createEncoder({\n        fixedSize: 0,\n        write: (_value, _bytes, offset) => offset,\n    });\n}\n\n/**\n * Creates a void decoder.\n */\nexport function getUnitDecoder(): FixedSizeDecoder<void, 0> {\n    return createDecoder({\n        fixedSize: 0,\n        read: (_bytes: Uint8Array, offset) => [undefined, offset],\n    });\n}\n\n/**\n * Creates a void codec.\n */\nexport function getUnitCodec(): FixedSizeCodec<void, void, 0> {\n    return combineCodec(getUnitEncoder(), getUnitDecoder());\n}\n"]}