{"version": 3, "sources": ["../src/assertions.ts", "../src/baseX.ts", "../src/base10.ts", "../src/base16.ts", "../src/base58.ts", "../src/base64.ts", "../src/baseX-reslice.ts", "../src/null-characters.ts", "../src/string.ts", "../src/utf8.ts", "../../text-encoding-impl/src/index.node.ts"], "names": ["alphabet", "combineCodec", "createDecoder", "createEncoder", "e", "TextDecoder", "TextEncoder"], "mappings": ";AAGO,SAAS,sBAAsBA,WAAkB,WAAmB,aAAa,WAAW;AAC/F,MAAI,CAAC,UAAU,MAAM,IAAI,OAAO,KAAKA,SAAQ,KAAK,CAAC,GAAG;AAElD,UAAM,IAAI,MAAM,6BAA6BA,UAAS,MAAM,UAAU,UAAU,IAAI;AAAA,EACxF;AACJ;;;ACRA;AAAA,EACI;AAAA,EACA;AAAA,EACA;AAAA,OAIG;AASA,IAAM,kBAAkB,CAACA,cAAkD;AAC9E,SAAO,cAAc;AAAA,IACjB,kBAAkB,CAAC,UAA0B;AACzC,YAAM,CAAC,eAAe,SAAS,IAAI,uBAAuB,OAAOA,UAAS,CAAC,CAAC;AAC5E,UAAI,cAAc;AAAI,eAAO,MAAM;AAEnC,YAAM,eAAe,mBAAmB,WAAWA,SAAQ;AAC3D,aAAO,cAAc,SAAS,KAAK,KAAK,aAAa,SAAS,EAAE,EAAE,SAAS,CAAC;AAAA,IAChF;AAAA,IACA,MAAM,OAAe,OAAO,QAAQ;AAEhC,4BAAsBA,WAAU,KAAK;AACrC,UAAI,UAAU;AAAI,eAAO;AAGzB,YAAM,CAAC,eAAe,SAAS,IAAI,uBAAuB,OAAOA,UAAS,CAAC,CAAC;AAC5E,UAAI,cAAc,IAAI;AAClB,cAAM,IAAI,IAAI,WAAW,cAAc,MAAM,EAAE,KAAK,CAAC,GAAG,MAAM;AAC9D,eAAO,SAAS,cAAc;AAAA,MAClC;AAGA,UAAI,eAAe,mBAAmB,WAAWA,SAAQ;AAGzD,YAAM,YAAsB,CAAC;AAC7B,aAAO,eAAe,IAAI;AACtB,kBAAU,QAAQ,OAAO,eAAe,IAAI,CAAC;AAC7C,wBAAgB;AAAA,MACpB;AAEA,YAAM,aAAa,CAAC,GAAG,MAAM,cAAc,MAAM,EAAE,KAAK,CAAC,GAAG,GAAG,SAAS;AACxE,YAAM,IAAI,YAAY,MAAM;AAC5B,aAAO,SAAS,WAAW;AAAA,IAC/B;AAAA,EACJ,CAAC;AACL;AAOO,IAAM,kBAAkB,CAACA,cAAkD;AAC9E,SAAO,cAAc;AAAA,IACjB,KAAK,UAAU,QAA0B;AACrC,YAAM,QAAQ,WAAW,IAAI,WAAW,SAAS,MAAM,MAAM;AAC7D,UAAI,MAAM,WAAW;AAAG,eAAO,CAAC,IAAI,CAAC;AAGrC,UAAI,aAAa,MAAM,UAAU,OAAK,MAAM,CAAC;AAC7C,mBAAa,eAAe,KAAK,MAAM,SAAS;AAChD,YAAM,gBAAgBA,UAAS,CAAC,EAAE,OAAO,UAAU;AACnD,UAAI,eAAe,MAAM;AAAQ,eAAO,CAAC,eAAe,SAAS,MAAM;AAGvE,YAAM,eAAe,MAAM,MAAM,UAAU,EAAE,OAAO,CAAC,KAAK,SAAS,MAAM,OAAO,OAAO,IAAI,GAAG,EAAE;AAGhG,YAAM,YAAY,mBAAmB,cAAcA,SAAQ;AAE3D,aAAO,CAAC,gBAAgB,WAAW,SAAS,MAAM;AAAA,IACtD;AAAA,EACJ,CAAC;AACL;AAWO,IAAM,gBAAgB,CAACA,cAC1B,aAAa,gBAAgBA,SAAQ,GAAG,gBAAgBA,SAAQ,CAAC;AAErE,SAAS,uBAAuB,OAAe,eAAyC;AACpF,QAAM,mBAAmB,CAAC,GAAG,KAAK,EAAE,UAAU,OAAK,MAAM,aAAa;AACtE,SAAO,qBAAqB,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,MAAM,GAAG,gBAAgB,GAAG,MAAM,MAAM,gBAAgB,CAAC;AACnH;AAEA,SAAS,mBAAmB,OAAeA,WAA0B;AACjE,QAAM,OAAO,OAAOA,UAAS,MAAM;AACnC,SAAO,CAAC,GAAG,KAAK,EAAE,OAAO,CAAC,KAAK,SAAS,MAAM,OAAO,OAAOA,UAAS,QAAQ,IAAI,CAAC,GAAG,EAAE;AAC3F;AAEA,SAAS,mBAAmB,OAAeA,WAA0B;AACjE,QAAM,OAAO,OAAOA,UAAS,MAAM;AACnC,QAAM,YAAY,CAAC;AACnB,SAAO,QAAQ,IAAI;AACf,cAAU,QAAQA,UAAS,OAAO,QAAQ,IAAI,CAAC,CAAC;AAChD,aAAS;AAAA,EACb;AACA,SAAO,UAAU,KAAK,EAAE;AAC5B;;;AC9GA,IAAM,WAAW;AAGV,IAAM,mBAAmB,MAAM,gBAAgB,QAAQ;AAGvD,IAAM,mBAAmB,MAAM,gBAAgB,QAAQ;AAGvD,IAAM,iBAAiB,MAAM,cAAc,QAAQ;;;ACX1D;AAAA,EACI,gBAAAC;AAAA,EACA,iBAAAC;AAAA,EACA,iBAAAC;AAAA,OAIG;AAKA,IAAM,mBAAmB,MAC5BA,eAAc;AAAA,EACV,kBAAkB,CAAC,UAAkB,KAAK,KAAK,MAAM,SAAS,CAAC;AAAA,EAC/D,MAAM,OAAe,OAAO,QAAQ;AAChC,UAAM,iBAAiB,MAAM,YAAY;AACzC,0BAAsB,oBAAoB,gBAAgB,KAAK;AAC/D,UAAM,UAAU,eAAe,MAAM,SAAS;AAC9C,UAAM,WAAW,UAAU,QAAQ,IAAI,CAAC,SAAiB,SAAS,MAAM,EAAE,CAAC,IAAI,CAAC;AAChF,UAAM,IAAI,UAAU,MAAM;AAC1B,WAAO,SAAS,SAAS;AAAA,EAC7B;AACJ,CAAC;AAGE,IAAM,mBAAmB,MAC5BD,eAAc;AAAA,EACV,KAAK,OAAO,QAAQ;AAChB,UAAM,QAAQ,MAAM,MAAM,MAAM,EAAE,OAAO,CAAC,KAAK,SAAS,MAAM,KAAK,SAAS,EAAE,EAAE,SAAS,GAAG,GAAG,GAAG,EAAE;AACpG,WAAO,CAAC,OAAO,MAAM,MAAM;AAAA,EAC/B;AACJ,CAAC;AAGE,IAAM,iBAAiB,MAAiCD,cAAa,iBAAiB,GAAG,iBAAiB,CAAC;;;ACjClH,IAAMD,YAAW;AAGV,IAAM,mBAAmB,MAAM,gBAAgBA,SAAQ;AAGvD,IAAM,mBAAmB,MAAM,gBAAgBA,SAAQ;AAGvD,IAAM,iBAAiB,MAAM,cAAcA,SAAQ;;;ACX1D;AAAA,EACI,gBAAAC;AAAA,EACA,iBAAAC;AAAA,EACA,iBAAAC;AAAA,EACA;AAAA,EACA;AAAA,OAIG;;;ACTP;AAAA,EACI,gBAAAF;AAAA,EACA,iBAAAC;AAAA,EACA,iBAAAC;AAAA,OAIG;AAQA,IAAM,yBAAyB,CAACH,WAAkB,SACrDG,eAAc;AAAA,EACV,kBAAkB,CAAC,UAAkB,KAAK,MAAO,MAAM,SAAS,OAAQ,CAAC;AAAA,EACzE,MAAM,OAAe,OAAO,QAAQ;AAChC,0BAAsBH,WAAU,KAAK;AACrC,QAAI,UAAU;AAAI,aAAO;AACzB,UAAM,cAAc,CAAC,GAAG,KAAK,EAAE,IAAI,OAAKA,UAAS,QAAQ,CAAC,CAAC;AAC3D,UAAM,gBAAgB,QAAQ,aAAa,MAAM,GAAG,KAAK;AACzD,UAAM,IAAI,eAAe,MAAM;AAC/B,WAAO,cAAc,SAAS;AAAA,EAClC;AACJ,CAAC;AAME,IAAM,yBAAyB,CAACA,WAAkB,SACrDE,eAAc;AAAA,EACV,KAAK,UAAU,SAAS,GAAqB;AACzC,UAAM,QAAQ,WAAW,IAAI,WAAW,SAAS,MAAM,MAAM;AAC7D,QAAI,MAAM,WAAW;AAAG,aAAO,CAAC,IAAI,SAAS,MAAM;AACnD,UAAM,cAAc,QAAQ,CAAC,GAAG,KAAK,GAAG,GAAG,MAAM,IAAI;AACrD,WAAO,CAAC,YAAY,IAAI,OAAKF,UAAS,CAAC,CAAC,EAAE,KAAK,EAAE,GAAG,SAAS,MAAM;AAAA,EACvE;AACJ,CAAC;AASE,IAAM,uBAAuB,CAACA,WAAkB,SACnDC,cAAa,uBAAuBD,WAAU,IAAI,GAAG,uBAAuBA,WAAU,IAAI,CAAC;AAG/F,SAAS,QAAQ,OAAiB,WAAmB,YAAoB,cAAiC;AACtG,QAAM,SAAS,CAAC;AAChB,MAAI,cAAc;AAClB,MAAI,oBAAoB;AACxB,QAAM,QAAQ,KAAK,cAAc;AACjC,aAAW,SAAS,OAAO;AACvB,kBAAe,eAAe,YAAa;AAC3C,yBAAqB;AACrB,WAAO,qBAAqB,YAAY;AACpC,2BAAqB;AACrB,aAAO,KAAM,eAAe,oBAAqB,IAAI;AAAA,IACzD;AAAA,EACJ;AACA,MAAI,gBAAgB,oBAAoB,GAAG;AACvC,WAAO,KAAM,eAAgB,aAAa,oBAAsB,IAAI;AAAA,EACxE;AACA,SAAO;AACX;;;ADxDA,IAAMA,YAAW;AAGV,IAAM,mBAAmB,MAAmC;AAC/D,MAAI,OAAa;AACb,WAAOG,eAAc;AAAA,MACjB,kBAAkB,CAAC,UAAkB;AACjC,YAAI;AACA,iBAAQ,KAAwB,KAAK,EAAE;AAAA,QAC3C,SAASC,IAAG;AAER,gBAAM,IAAI,MAAM,sCAAsC,KAAK,IAAI;AAAA,QACnE;AAAA,MACJ;AAAA,MACA,MAAM,OAAe,OAAO,QAAQ;AAChC,YAAI;AACA,gBAAM,aAAc,KAAwB,KAAK,EAC5C,MAAM,EAAE,EACR,IAAI,OAAK,EAAE,WAAW,CAAC,CAAC;AAC7B,gBAAM,IAAI,YAAY,MAAM;AAC5B,iBAAO,WAAW,SAAS;AAAA,QAC/B,SAASA,IAAG;AAER,gBAAM,IAAI,MAAM,sCAAsC,KAAK,IAAI;AAAA,QACnE;AAAA,MACJ;AAAA,IACJ,CAAC;AAAA,EACL;AAEA,MAAI,MAAY;AACZ,WAAOD,eAAc;AAAA,MACjB,kBAAkB,CAAC,UAAkB,OAAO,KAAK,OAAO,QAAQ,EAAE;AAAA,MAClE,MAAM,OAAe,OAAO,QAAQ;AAChC,8BAAsBH,WAAU,MAAM,QAAQ,MAAM,EAAE,CAAC;AACvD,cAAM,SAAS,OAAO,KAAK,OAAO,QAAQ;AAC1C,cAAM,IAAI,QAAQ,MAAM;AACxB,eAAO,OAAO,SAAS;AAAA,MAC3B;AAAA,IACJ,CAAC;AAAA,EACL;AAEA,SAAO,WAAW,uBAAuBA,WAAU,CAAC,GAAG,CAAC,UAA0B,MAAM,QAAQ,MAAM,EAAE,CAAC;AAC7G;AAGO,IAAM,mBAAmB,MAAmC;AAC/D,MAAI,OAAa;AACb,WAAOE,eAAc;AAAA,MACjB,KAAK,OAAO,SAAS,GAAG;AACpB,cAAM,QAAQ,MAAM,MAAM,MAAM;AAChC,cAAM,QAAS,KAAwB,OAAO,aAAa,GAAG,KAAK,CAAC;AACpE,eAAO,CAAC,OAAO,MAAM,MAAM;AAAA,MAC/B;AAAA,IACJ,CAAC;AAAA,EACL;AAEA,MAAI,MAAY;AACZ,WAAOA,eAAc;AAAA,MACjB,MAAM,CAAC,OAAO,SAAS,MAAM,CAAC,OAAO,KAAK,OAAO,MAAM,EAAE,SAAS,QAAQ,GAAG,MAAM,MAAM;AAAA,IAC7F,CAAC;AAAA,EACL;AAEA,SAAO;AAAA,IAAW,uBAAuBF,WAAU,CAAC;AAAA,IAAG,CAAC,UACpD,MAAM,OAAO,KAAK,KAAK,MAAM,SAAS,CAAC,IAAI,GAAG,GAAG;AAAA,EACrD;AACJ;AAGO,IAAM,iBAAiB,MAAiCC,cAAa,iBAAiB,GAAG,iBAAiB,CAAC;;;AEjF3G,IAAM,uBAAuB,CAAC;AAAA;AAAA,EAEjC,MAAM,QAAQ,WAAW,EAAE;AAAA;AAGxB,IAAM,oBAAoB,CAAC,OAAe,UAAkB,MAAM,OAAO,OAAO,IAAQ;;;ACN/F;AAAA,EACI;AAAA,EACA;AAAA,EAEA,gBAAAA;AAAA,EACA,iBAAAC;AAAA,EACA,iBAAAC;AAAA,EAGA;AAAA,EAIA;AAAA,EACA;AAAA,OAIG;AACP,SAAS,eAAe,qBAAgE;;;ACnBxF;AAAA,EAEI,gBAAAF;AAAA,EACA,iBAAAC;AAAA,EACA,iBAAAC;AAAA,OAGG;;;ACPA,IAAME,IAAc,WAAW;AAA/B,IACMC,IAAc,WAAW;;;ADY/B,IAAM,iBAAiB,MAAmC;AAC7D,MAAI;AACJ,SAAOH,eAAc;AAAA,IACjB,kBAAkB,YAAU,gBAAgB,IAAI,EAAY,GAAG,OAAO,KAAK,EAAE;AAAA,IAC7E,OAAO,CAAC,OAAe,OAAO,WAAW;AACrC,YAAM,cAAc,gBAAgB,IAAI,EAAY,GAAG,OAAO,KAAK;AACnE,YAAM,IAAI,YAAY,MAAM;AAC5B,aAAO,SAAS,WAAW;AAAA,IAC/B;AAAA,EACJ,CAAC;AACL;AAGO,IAAM,iBAAiB,MAAmC;AAC7D,MAAI;AACJ,SAAOD,eAAc;AAAA,IACjB,KAAK,OAAO,QAAQ;AAChB,YAAM,SAAS,gBAAgB,IAAI,EAAY,GAAG,OAAO,MAAM,MAAM,MAAM,CAAC;AAC5E,aAAO,CAAC,qBAAqB,KAAK,GAAG,MAAM,MAAM;AAAA,IACrD;AAAA,EACJ,CAAC;AACL;AAGO,IAAM,eAAe,MAAqBD,cAAa,eAAe,GAAG,eAAe,CAAC;;;ADoBzF,SAAS,iBAAiB,SAA4D,CAAC,GAAoB;AAC9G,QAAM,OAAO,OAAO,QAAQ,cAAc;AAC1C,QAAM,WAAW,OAAO,YAAY,eAAe;AAEnD,MAAI,SAAS,YAAY;AACrB,WAAO;AAAA,EACX;AAEA,MAAI,OAAO,SAAS,UAAU;AAC1B,WAAO,WAAW,UAAU,IAAI;AAAA,EACpC;AAEA,SAAOE,eAAc;AAAA,IACjB,kBAAkB,CAAC,UAAkB;AACjC,YAAM,cAAc,eAAe,OAAO,QAAQ;AAClD,aAAO,eAAe,aAAa,IAAI,IAAI;AAAA,IAC/C;AAAA,IACA,OAAO,CAAC,OAAe,OAAO,WAAW;AACrC,YAAM,cAAc,eAAe,OAAO,QAAQ;AAClD,eAAS,KAAK,MAAM,aAAa,OAAO,MAAM;AAC9C,aAAO,SAAS,MAAM,OAAO,OAAO,MAAM;AAAA,IAC9C;AAAA,EACJ,CAAC;AACL;AAeO,SAAS,iBAAiB,SAA4D,CAAC,GAAoB;AAC9G,QAAM,OAAO,OAAO,QAAQ,cAAc;AAC1C,QAAM,WAAW,OAAO,YAAY,eAAe;AAEnD,MAAI,SAAS,YAAY;AACrB,WAAO;AAAA,EACX;AAEA,MAAI,OAAO,SAAS,UAAU;AAC1B,WAAO,WAAW,UAAU,IAAI;AAAA,EACpC;AAEA,SAAOD,eAAc;AAAA,IACjB,MAAM,CAAC,OAAmB,SAAS,MAAM;AACrC,wCAAkC,UAAU,OAAO,MAAM;AACzD,YAAM,CAAC,cAAc,YAAY,IAAI,KAAK,KAAK,OAAO,MAAM;AAC5D,YAAM,SAAS,OAAO,YAAY;AAClC,eAAS;AACT,YAAM,eAAe,MAAM,MAAM,QAAQ,SAAS,MAAM;AACxD,4CAAsC,UAAU,QAAQ,YAAY;AACpE,YAAM,CAAC,OAAO,aAAa,IAAI,SAAS,KAAK,cAAc,CAAC;AAC5D,gBAAU;AACV,aAAO,CAAC,OAAO,MAAM;AAAA,IACzB;AAAA,EACJ,CAAC;AACL;AAaO,SAAS,eAAe,SAAwD,CAAC,GAAkB;AACtG,SAAOD,cAAa,iBAAiB,MAAM,GAAG,iBAAiB,MAAM,CAAC;AAC1E", "sourcesContent": ["/**\n * Asserts that a given string matches a given alphabet.\n */\nexport function assertValidBaseString(alphabet: string, testValue: string, givenValue = testValue) {\n    if (!testValue.match(new RegExp(`^[${alphabet}]*$`))) {\n        // TODO: Coded error.\n        throw new Error(`Expected a string of base ${alphabet.length}, got [${givenValue}].`);\n    }\n}\n", "import {\n    combineCodec,\n    createDecoder,\n    createEncoder,\n    VariableSizeCodec,\n    VariableSizeDecoder,\n    VariableSizeEncoder,\n} from '@solana/codecs-core';\n\nimport { assertValidBaseString } from './assertions';\n\n/**\n * Encodes a string using a custom alphabet by dividing\n * by the base and handling leading zeroes.\n * @see {@link getBaseXCodec} for a more detailed description.\n */\nexport const getBaseXEncoder = (alphabet: string): VariableSizeEncoder<string> => {\n    return createEncoder({\n        getSizeFromValue: (value: string): number => {\n            const [leadingZeroes, tailChars] = partitionLeadingZeroes(value, alphabet[0]);\n            if (tailChars === '') return value.length;\n\n            const base10Number = getBigIntFromBaseX(tailChars, alphabet);\n            return leadingZeroes.length + Math.ceil(base10Number.toString(16).length / 2);\n        },\n        write(value: string, bytes, offset) {\n            // Check if the value is valid.\n            assertValidBaseString(alphabet, value);\n            if (value === '') return offset;\n\n            // Handle leading zeroes.\n            const [leadingZeroes, tailChars] = partitionLeadingZeroes(value, alphabet[0]);\n            if (tailChars === '') {\n                bytes.set(new Uint8Array(leadingZeroes.length).fill(0), offset);\n                return offset + leadingZeroes.length;\n            }\n\n            // From baseX to base10.\n            let base10Number = getBigIntFromBaseX(tailChars, alphabet);\n\n            // From base10 to bytes.\n            const tailBytes: number[] = [];\n            while (base10Number > 0n) {\n                tailBytes.unshift(Number(base10Number % 256n));\n                base10Number /= 256n;\n            }\n\n            const bytesToAdd = [...Array(leadingZeroes.length).fill(0), ...tailBytes];\n            bytes.set(bytesToAdd, offset);\n            return offset + bytesToAdd.length;\n        },\n    });\n};\n\n/**\n * Decodes a string using a custom alphabet by dividing\n * by the base and handling leading zeroes.\n * @see {@link getBaseXCodec} for a more detailed description.\n */\nexport const getBaseXDecoder = (alphabet: string): VariableSizeDecoder<string> => {\n    return createDecoder({\n        read(rawBytes, offset): [string, number] {\n            const bytes = offset === 0 ? rawBytes : rawBytes.slice(offset);\n            if (bytes.length === 0) return ['', 0];\n\n            // Handle leading zeroes.\n            let trailIndex = bytes.findIndex(n => n !== 0);\n            trailIndex = trailIndex === -1 ? bytes.length : trailIndex;\n            const leadingZeroes = alphabet[0].repeat(trailIndex);\n            if (trailIndex === bytes.length) return [leadingZeroes, rawBytes.length];\n\n            // From bytes to base10.\n            const base10Number = bytes.slice(trailIndex).reduce((sum, byte) => sum * 256n + BigInt(byte), 0n);\n\n            // From base10 to baseX.\n            const tailChars = getBaseXFromBigInt(base10Number, alphabet);\n\n            return [leadingZeroes + tailChars, rawBytes.length];\n        },\n    });\n};\n\n/**\n * A string codec that requires a custom alphabet and uses\n * the length of that alphabet as the base. It then divides\n * the input by the base as many times as necessary to get\n * the output. It also supports leading zeroes by using the\n * first character of the alphabet as the zero character.\n *\n * This can be used to create codecs such as base10 or base58.\n */\nexport const getBaseXCodec = (alphabet: string): VariableSizeCodec<string> =>\n    combineCodec(getBaseXEncoder(alphabet), getBaseXDecoder(alphabet));\n\nfunction partitionLeadingZeroes(value: string, zeroCharacter: string): [string, string] {\n    const leadingZeroIndex = [...value].findIndex(c => c !== zeroCharacter);\n    return leadingZeroIndex === -1 ? [value, ''] : [value.slice(0, leadingZeroIndex), value.slice(leadingZeroIndex)];\n}\n\nfunction getBigIntFromBaseX(value: string, alphabet: string): bigint {\n    const base = BigInt(alphabet.length);\n    return [...value].reduce((sum, char) => sum * base + BigInt(alphabet.indexOf(char)), 0n);\n}\n\nfunction getBaseXFromBigInt(value: bigint, alphabet: string): string {\n    const base = BigInt(alphabet.length);\n    const tailChars = [];\n    while (value > 0n) {\n        tailChars.unshift(alphabet[Number(value % base)]);\n        value /= base;\n    }\n    return tailChars.join('');\n}\n", "import { getBaseXCodec, getBaseXDecoder, getBaseXEncoder } from './baseX';\n\nconst alphabet = '0123456789';\n\n/** Encodes strings in base10. */\nexport const getBase10Encoder = () => getBaseXEncoder(alphabet);\n\n/** Decodes strings in base10. */\nexport const getBase10Decoder = () => getBaseXDecoder(alphabet);\n\n/** Encodes and decodes strings in base10. */\nexport const getBase10Codec = () => getBaseXCodec(alphabet);\n", "import {\n    combineCodec,\n    createDecoder,\n    createEncoder,\n    VariableSizeCodec,\n    VariableSizeDecoder,\n    VariableSizeEncoder,\n} from '@solana/codecs-core';\n\nimport { assertValidBaseString } from './assertions';\n\n/** Encodes strings in base16. */\nexport const getBase16Encoder = (): VariableSizeEncoder<string> =>\n    createEncoder({\n        getSizeFromValue: (value: string) => Math.ceil(value.length / 2),\n        write(value: string, bytes, offset) {\n            const lowercaseValue = value.toLowerCase();\n            assertValidBaseString('0123456789abcdef', lowercaseValue, value);\n            const matches = lowercaseValue.match(/.{1,2}/g);\n            const hexBytes = matches ? matches.map((byte: string) => parseInt(byte, 16)) : [];\n            bytes.set(hexBytes, offset);\n            return hexBytes.length + offset;\n        },\n    });\n\n/** Decodes strings in base16. */\nexport const getBase16Decoder = (): VariableSizeDecoder<string> =>\n    createDecoder({\n        read(bytes, offset) {\n            const value = bytes.slice(offset).reduce((str, byte) => str + byte.toString(16).padStart(2, '0'), '');\n            return [value, bytes.length];\n        },\n    });\n\n/** Encodes and decodes strings in base16. */\nexport const getBase16Codec = (): VariableSizeCodec<string> => combineCodec(getBase16Encoder(), getBase16Decoder());\n", "import { getBaseXCodec, getBaseXDecoder, getBaseXEncoder } from './baseX';\n\nconst alphabet = '123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz';\n\n/** Encodes strings in base58. */\nexport const getBase58Encoder = () => getBaseXEncoder(alphabet);\n\n/** Decodes strings in base58. */\nexport const getBase58Decoder = () => getBaseXDecoder(alphabet);\n\n/** Encodes and decodes strings in base58. */\nexport const getBase58Codec = () => getBaseXCodec(alphabet);\n", "import {\n    combineCodec,\n    createDecoder,\n    createEncoder,\n    mapDecoder,\n    mapEncoder,\n    VariableSizeCodec,\n    VariableSizeDecoder,\n    VariableSizeEncoder,\n} from '@solana/codecs-core';\n\nimport { assertValidBaseString } from './assertions';\nimport { getBaseXResliceDecoder, getBaseXResliceEncoder } from './baseX-reslice';\n\nconst alphabet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';\n\n/** Encodes strings in base64. */\nexport const getBase64Encoder = (): VariableSizeEncoder<string> => {\n    if (__BROWSER__) {\n        return createEncoder({\n            getSizeFromValue: (value: string) => {\n                try {\n                    return (atob as Window['atob'])(value).length;\n                } catch (e) {\n                    // TODO: Coded error.\n                    throw new Error(`Expected a string of base 64, got [${value}].`);\n                }\n            },\n            write(value: string, bytes, offset) {\n                try {\n                    const bytesToAdd = (atob as Window['atob'])(value)\n                        .split('')\n                        .map(c => c.charCodeAt(0));\n                    bytes.set(bytesToAdd, offset);\n                    return bytesToAdd.length + offset;\n                } catch (e) {\n                    // TODO: Coded error.\n                    throw new Error(`Expected a string of base 64, got [${value}].`);\n                }\n            },\n        });\n    }\n\n    if (__NODEJS__) {\n        return createEncoder({\n            getSizeFromValue: (value: string) => Buffer.from(value, 'base64').length,\n            write(value: string, bytes, offset) {\n                assertValidBaseString(alphabet, value.replace(/=/g, ''));\n                const buffer = Buffer.from(value, 'base64');\n                bytes.set(buffer, offset);\n                return buffer.length + offset;\n            },\n        });\n    }\n\n    return mapEncoder(getBaseXResliceEncoder(alphabet, 6), (value: string): string => value.replace(/=/g, ''));\n};\n\n/** Decodes strings in base64. */\nexport const getBase64Decoder = (): VariableSizeDecoder<string> => {\n    if (__BROWSER__) {\n        return createDecoder({\n            read(bytes, offset = 0) {\n                const slice = bytes.slice(offset);\n                const value = (btoa as Window['btoa'])(String.fromCharCode(...slice));\n                return [value, bytes.length];\n            },\n        });\n    }\n\n    if (__NODEJS__) {\n        return createDecoder({\n            read: (bytes, offset = 0) => [Buffer.from(bytes, offset).toString('base64'), bytes.length],\n        });\n    }\n\n    return mapDecoder(getBaseXResliceDecoder(alphabet, 6), (value: string): string =>\n        value.padEnd(Math.ceil(value.length / 4) * 4, '='),\n    );\n};\n\n/** Encodes and decodes strings in base64. */\nexport const getBase64Codec = (): VariableSizeCodec<string> => combineCodec(getBase64Encoder(), getBase64Decoder());\n", "import {\n    combineCodec,\n    createDecode<PERSON>,\n    createEncoder,\n    VariableSizeCodec,\n    VariableSizeDecoder,\n    VariableSizeEncoder,\n} from '@solana/codecs-core';\n\nimport { assertValidBaseString } from './assertions';\n\n/**\n * Encodes a string using a custom alphabet by reslicing the bits of the byte array.\n * @see {@link getBaseXResliceCodec} for a more detailed description.\n */\nexport const getBaseXResliceEncoder = (alphabet: string, bits: number): VariableSizeEncoder<string> =>\n    createEncoder({\n        getSizeFromValue: (value: string) => Math.floor((value.length * bits) / 8),\n        write(value: string, bytes, offset) {\n            assertValidBaseString(alphabet, value);\n            if (value === '') return offset;\n            const charIndices = [...value].map(c => alphabet.indexOf(c));\n            const reslicedBytes = reslice(charIndices, bits, 8, false);\n            bytes.set(reslicedBytes, offset);\n            return reslicedBytes.length + offset;\n        },\n    });\n\n/**\n * Decodes a string using a custom alphabet by reslicing the bits of the byte array.\n * @see {@link getBaseXResliceCodec} for a more detailed description.\n */\nexport const getBaseXResliceDecoder = (alphabet: string, bits: number): VariableSizeDecoder<string> =>\n    createDecoder({\n        read(rawBytes, offset = 0): [string, number] {\n            const bytes = offset === 0 ? rawBytes : rawBytes.slice(offset);\n            if (bytes.length === 0) return ['', rawBytes.length];\n            const charIndices = reslice([...bytes], 8, bits, true);\n            return [charIndices.map(i => alphabet[i]).join(''), rawBytes.length];\n        },\n    });\n\n/**\n * A string serializer that reslices bytes into custom chunks\n * of bits that are then mapped to a custom alphabet.\n *\n * This can be used to create serializers whose alphabet\n * is a power of 2 such as base16 or base64.\n */\nexport const getBaseXResliceCodec = (alphabet: string, bits: number): VariableSizeCodec<string> =>\n    combineCodec(getBaseXResliceEncoder(alphabet, bits), getBaseXResliceDecoder(alphabet, bits));\n\n/** Helper function to reslice the bits inside bytes. */\nfunction reslice(input: number[], inputBits: number, outputBits: number, useRemainder: boolean): number[] {\n    const output = [];\n    let accumulator = 0;\n    let bitsInAccumulator = 0;\n    const mask = (1 << outputBits) - 1;\n    for (const value of input) {\n        accumulator = (accumulator << inputBits) | value;\n        bitsInAccumulator += inputBits;\n        while (bitsInAccumulator >= outputBits) {\n            bitsInAccumulator -= outputBits;\n            output.push((accumulator >> bitsInAccumulator) & mask);\n        }\n    }\n    if (useRemainder && bitsInAccumulator > 0) {\n        output.push((accumulator << (outputBits - bitsInAccumulator)) & mask);\n    }\n    return output;\n}\n", "/**Removes null characters from a string. */\nexport const removeNullCharacters = (value: string) =>\n    // eslint-disable-next-line no-control-regex\n    value.replace(/\\u0000/g, '');\n\n/** Pads a string with null characters at the end. */\nexport const padNullCharacters = (value: string, chars: number) => value.padEnd(chars, '\\u0000');\n", "import {\n    assertByteArrayHasEnoughBytesForCodec,\n    assertByteArrayIsNotEmptyForCodec,\n    Codec,\n    combineCodec,\n    createDecoder,\n    createEncoder,\n    Decoder,\n    Encoder,\n    fixDecoder,\n    FixedSizeCodec,\n    FixedSizeDecoder,\n    FixedSizeEncoder,\n    fixEncoder,\n    getEncodedSize,\n    VariableSizeCodec,\n    VariableSizeDecoder,\n    VariableSizeEncoder,\n} from '@solana/codecs-core';\nimport { getU32Decoder, getU32Encoder, NumberCodec, NumberDecoder, NumberEncoder } from '@solana/codecs-numbers';\n\nimport { getUtf8Decoder, getUtf8Encoder } from './utf8';\n\n/** Defines the config for string codecs. */\nexport type StringCodecConfig<\n    TPrefix extends NumberCodec | NumberEncoder | NumberDecoder,\n    TEncoding extends Codec<string> | Encoder<string> | Decoder<string>,\n> = {\n    /**\n     * The size of the string. It can be one of the following:\n     * - a {@link NumberCodec} that prefixes the string with its size.\n     * - a fixed number of bytes.\n     * - or `'variable'` to use the rest of the byte array.\n     * @defaultValue u32 prefix.\n     */\n    size?: TPrefix | number | 'variable';\n\n    /**\n     * The codec to use for encoding and decoding the content.\n     * @defaultValue UTF-8 encoding.\n     */\n    encoding?: TEncoding;\n};\n\n/** Encodes strings from a given encoding and size strategy. */\nexport function getStringEncoder<TSize extends number>(\n    config: StringCodecConfig<NumberEncoder, Encoder<string>> & { size: TSize },\n): FixedSizeEncoder<string, TSize>;\nexport function getStringEncoder<TSize extends number>(\n    config: StringCodecConfig<NumberEncoder, Encoder<string>> & {\n        size: 'variable';\n        encoding: FixedSizeEncoder<string, TSize>;\n    },\n): FixedSizeEncoder<string, TSize>;\nexport function getStringEncoder(\n    config?: StringCodecConfig<NumberEncoder, Encoder<string>>,\n): VariableSizeEncoder<string>;\nexport function getStringEncoder(config: StringCodecConfig<NumberEncoder, Encoder<string>> = {}): Encoder<string> {\n    const size = config.size ?? getU32Encoder();\n    const encoding = config.encoding ?? getUtf8Encoder();\n\n    if (size === 'variable') {\n        return encoding;\n    }\n\n    if (typeof size === 'number') {\n        return fixEncoder(encoding, size);\n    }\n\n    return createEncoder({\n        getSizeFromValue: (value: string) => {\n            const contentSize = getEncodedSize(value, encoding);\n            return getEncodedSize(contentSize, size) + contentSize;\n        },\n        write: (value: string, bytes, offset) => {\n            const contentSize = getEncodedSize(value, encoding);\n            offset = size.write(contentSize, bytes, offset);\n            return encoding.write(value, bytes, offset);\n        },\n    });\n}\n\n/** Decodes strings from a given encoding and size strategy. */\nexport function getStringDecoder<TSize extends number>(\n    config: StringCodecConfig<NumberDecoder, Decoder<string>> & { size: TSize },\n): FixedSizeDecoder<string, TSize>;\nexport function getStringDecoder<TSize extends number>(\n    config: StringCodecConfig<NumberDecoder, Decoder<string>> & {\n        size: 'variable';\n        encoding: FixedSizeDecoder<string, TSize>;\n    },\n): FixedSizeDecoder<string, TSize>;\nexport function getStringDecoder(\n    config?: StringCodecConfig<NumberDecoder, Decoder<string>>,\n): VariableSizeDecoder<string>;\nexport function getStringDecoder(config: StringCodecConfig<NumberDecoder, Decoder<string>> = {}): Decoder<string> {\n    const size = config.size ?? getU32Decoder();\n    const encoding = config.encoding ?? getUtf8Decoder();\n\n    if (size === 'variable') {\n        return encoding;\n    }\n\n    if (typeof size === 'number') {\n        return fixDecoder(encoding, size);\n    }\n\n    return createDecoder({\n        read: (bytes: Uint8Array, offset = 0) => {\n            assertByteArrayIsNotEmptyForCodec('string', bytes, offset);\n            const [lengthBigInt, lengthOffset] = size.read(bytes, offset);\n            const length = Number(lengthBigInt);\n            offset = lengthOffset;\n            const contentBytes = bytes.slice(offset, offset + length);\n            assertByteArrayHasEnoughBytesForCodec('string', length, contentBytes);\n            const [value, contentOffset] = encoding.read(contentBytes, 0);\n            offset += contentOffset;\n            return [value, offset];\n        },\n    });\n}\n\n/** Encodes and decodes strings from a given encoding and size strategy. */\nexport function getStringCodec<TSize extends number>(\n    config: StringCodecConfig<NumberCodec, Codec<string>> & { size: TSize },\n): FixedSizeCodec<string, string, TSize>;\nexport function getStringCodec<TSize extends number>(\n    config: StringCodecConfig<NumberCodec, Codec<string>> & {\n        size: 'variable';\n        encoding: FixedSizeCodec<string, string, TSize>;\n    },\n): FixedSizeCodec<string, string, TSize>;\nexport function getStringCodec(config?: StringCodecConfig<NumberCodec, Codec<string>>): VariableSizeCodec<string>;\nexport function getStringCodec(config: StringCodecConfig<NumberCodec, Codec<string>> = {}): Codec<string> {\n    return combineCodec(getStringEncoder(config), getStringDecoder(config));\n}\n", "import {\n    Codec,\n    combineCodec,\n    createDecoder,\n    createEncoder,\n    VariableSizeDecoder,\n    VariableSizeEncoder,\n} from '@solana/codecs-core';\nimport { TextDecoder, TextEncoder } from 'text-encoding-impl';\n\nimport { removeNullCharacters } from './null-characters';\n\n/** Encodes UTF-8 strings using the native `TextEncoder` API. */\nexport const getUtf8Encoder = (): VariableSizeEncoder<string> => {\n    let textEncoder: TextEncoder;\n    return createEncoder({\n        getSizeFromValue: value => (textEncoder ||= new TextEncoder()).encode(value).length,\n        write: (value: string, bytes, offset) => {\n            const bytesToAdd = (textEncoder ||= new TextEncoder()).encode(value);\n            bytes.set(bytesToAdd, offset);\n            return offset + bytesToAdd.length;\n        },\n    });\n};\n\n/** Decodes UTF-8 strings using the native `TextDecoder` API. */\nexport const getUtf8Decoder = (): VariableSizeDecoder<string> => {\n    let textDecoder: TextDecoder;\n    return createDecoder({\n        read(bytes, offset) {\n            const value = (textDecoder ||= new TextDecoder()).decode(bytes.slice(offset));\n            return [removeNullCharacters(value), bytes.length];\n        },\n    });\n};\n\n/** Encodes and decodes UTF-8 strings using the native `TextEncoder` and `TextDecoder` API. */\nexport const getUtf8Codec = (): Codec<string> => combineCodec(getUtf8Encoder(), getUtf8Decoder());\n", "export const TextDecoder = globalThis.TextDecoder;\nexport const TextEncoder = globalThis.TextEncoder;\n"]}