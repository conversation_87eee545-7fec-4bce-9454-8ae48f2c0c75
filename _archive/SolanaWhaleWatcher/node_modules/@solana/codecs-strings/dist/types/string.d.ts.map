{"version": 3, "file": "string.d.ts", "sourceRoot": "", "sources": ["../../src/string.ts"], "names": [], "mappings": "AAAA,OAAO,EAGH,KAAK,EAIL,OAAO,EACP,OAAO,EAEP,cAAc,EACd,gBAAgB,EAChB,gBAAgB,EAGhB,iBAAiB,EACjB,mBAAmB,EACnB,mBAAmB,EACtB,MAAM,qBAAqB,CAAC;AAC7B,OAAO,EAAgC,WAAW,EAAE,aAAa,EAAE,aAAa,EAAE,MAAM,wBAAwB,CAAC;AAIjH,4CAA4C;AAC5C,MAAM,MAAM,iBAAiB,CACzB,OAAO,SAAS,WAAW,GAAG,aAAa,GAAG,aAAa,EAC3D,SAAS,SAAS,KAAK,CAAC,MAAM,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC,IACnE;IACA;;;;;;OAMG;IACH,IAAI,CAAC,EAAE,OAAO,GAAG,MAAM,GAAG,UAAU,CAAC;IAErC;;;OAGG;IACH,QAAQ,CAAC,EAAE,SAAS,CAAC;CACxB,CAAC;AAEF,+DAA+D;AAC/D,wBAAgB,gBAAgB,CAAC,KAAK,SAAS,MAAM,EACjD,MAAM,EAAE,iBAAiB,CAAC,aAAa,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG;IAAE,IAAI,EAAE,KAAK,CAAA;CAAE,GAC5E,gBAAgB,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AACnC,wBAAgB,gBAAgB,CAAC,KAAK,SAAS,MAAM,EACjD,MAAM,EAAE,iBAAiB,CAAC,aAAa,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG;IACxD,IAAI,EAAE,UAAU,CAAC;IACjB,QAAQ,EAAE,gBAAgB,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;CAC7C,GACF,gBAAgB,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AACnC,wBAAgB,gBAAgB,CAC5B,MAAM,CAAC,EAAE,iBAAiB,CAAC,aAAa,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,GAC3D,mBAAmB,CAAC,MAAM,CAAC,CAAC;AA0B/B,+DAA+D;AAC/D,wBAAgB,gBAAgB,CAAC,KAAK,SAAS,MAAM,EACjD,MAAM,EAAE,iBAAiB,CAAC,aAAa,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG;IAAE,IAAI,EAAE,KAAK,CAAA;CAAE,GAC5E,gBAAgB,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AACnC,wBAAgB,gBAAgB,CAAC,KAAK,SAAS,MAAM,EACjD,MAAM,EAAE,iBAAiB,CAAC,aAAa,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG;IACxD,IAAI,EAAE,UAAU,CAAC;IACjB,QAAQ,EAAE,gBAAgB,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;CAC7C,GACF,gBAAgB,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AACnC,wBAAgB,gBAAgB,CAC5B,MAAM,CAAC,EAAE,iBAAiB,CAAC,aAAa,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,GAC3D,mBAAmB,CAAC,MAAM,CAAC,CAAC;AA4B/B,2EAA2E;AAC3E,wBAAgB,cAAc,CAAC,KAAK,SAAS,MAAM,EAC/C,MAAM,EAAE,iBAAiB,CAAC,WAAW,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG;IAAE,IAAI,EAAE,KAAK,CAAA;CAAE,GACxE,cAAc,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;AACzC,wBAAgB,cAAc,CAAC,KAAK,SAAS,MAAM,EAC/C,MAAM,EAAE,iBAAiB,CAAC,WAAW,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG;IACpD,IAAI,EAAE,UAAU,CAAC;IACjB,QAAQ,EAAE,cAAc,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;CACnD,GACF,cAAc,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;AACzC,wBAAgB,cAAc,CAAC,MAAM,CAAC,EAAE,iBAAiB,CAAC,WAAW,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,iBAAiB,CAAC,MAAM,CAAC,CAAC"}