import { Code<PERSON>, Decoder, Encoder, FixedSizeCodec, FixedSizeDecoder, FixedSizeEncoder, VariableSizeCodec, VariableSizeDecoder, VariableSizeEncoder } from '@solana/codecs-core';
import { NumberCodec, NumberDecoder, NumberEncoder } from '@solana/codecs-numbers';
/** Defines the config for string codecs. */
export type StringCodecConfig<TPrefix extends NumberCodec | NumberEncoder | NumberDecoder, TEncoding extends Codec<string> | Encoder<string> | Decoder<string>> = {
    /**
     * The size of the string. It can be one of the following:
     * - a {@link NumberCodec} that prefixes the string with its size.
     * - a fixed number of bytes.
     * - or `'variable'` to use the rest of the byte array.
     * @defaultValue u32 prefix.
     */
    size?: TPrefix | number | 'variable';
    /**
     * The codec to use for encoding and decoding the content.
     * @defaultValue UTF-8 encoding.
     */
    encoding?: TEncoding;
};
/** Encodes strings from a given encoding and size strategy. */
export declare function getStringEncoder<TSize extends number>(config: StringCodecConfig<NumberEncoder, Encoder<string>> & {
    size: TSize;
}): FixedSizeEncoder<string, TSize>;
export declare function getStringEncoder<TSize extends number>(config: StringCodecConfig<NumberEncoder, Encoder<string>> & {
    size: 'variable';
    encoding: FixedSizeEncoder<string, TSize>;
}): FixedSizeEncoder<string, TSize>;
export declare function getStringEncoder(config?: StringCodecConfig<NumberEncoder, Encoder<string>>): VariableSizeEncoder<string>;
/** Decodes strings from a given encoding and size strategy. */
export declare function getStringDecoder<TSize extends number>(config: StringCodecConfig<NumberDecoder, Decoder<string>> & {
    size: TSize;
}): FixedSizeDecoder<string, TSize>;
export declare function getStringDecoder<TSize extends number>(config: StringCodecConfig<NumberDecoder, Decoder<string>> & {
    size: 'variable';
    encoding: FixedSizeDecoder<string, TSize>;
}): FixedSizeDecoder<string, TSize>;
export declare function getStringDecoder(config?: StringCodecConfig<NumberDecoder, Decoder<string>>): VariableSizeDecoder<string>;
/** Encodes and decodes strings from a given encoding and size strategy. */
export declare function getStringCodec<TSize extends number>(config: StringCodecConfig<NumberCodec, Codec<string>> & {
    size: TSize;
}): FixedSizeCodec<string, string, TSize>;
export declare function getStringCodec<TSize extends number>(config: StringCodecConfig<NumberCodec, Codec<string>> & {
    size: 'variable';
    encoding: FixedSizeCodec<string, string, TSize>;
}): FixedSizeCodec<string, string, TSize>;
export declare function getStringCodec(config?: StringCodecConfig<NumberCodec, Codec<string>>): VariableSizeCodec<string>;
//# sourceMappingURL=string.d.ts.map