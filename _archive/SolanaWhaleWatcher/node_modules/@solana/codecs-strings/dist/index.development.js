this.globalThis = this.globalThis || {};
this.globalThis.solanaWeb3 = (function (exports) {
  'use strict';

  // src/assertions.ts
  function assertValidBaseString(alphabet4, testValue, givenValue = testValue) {
    if (!testValue.match(new RegExp(`^[${alphabet4}]*$`))) {
      throw new Error(`Expected a string of base ${alphabet4.length}, got [${givenValue}].`);
    }
  }

  // ../codecs-core/dist/index.browser.js
  function assertByteArrayIsNotEmptyForCodec(codecDescription, bytes, offset = 0) {
    if (bytes.length - offset <= 0) {
      throw new Error(`Codec [${codecDescription}] cannot decode empty byte arrays.`);
    }
  }
  function assertByteArrayHasEnoughBytesForCodec(codecDescription, expected, bytes, offset = 0) {
    const bytesLength = bytes.length - offset;
    if (bytesLength < expected) {
      throw new Error(`Codec [${codecDescription}] expected ${expected} bytes, got ${bytesLength}.`);
    }
  }
  var padBytes = (bytes, length) => {
    if (bytes.length >= length)
      return bytes;
    const paddedBytes = new Uint8Array(length).fill(0);
    paddedBytes.set(bytes);
    return paddedBytes;
  };
  var fixBytes = (bytes, length) => padBytes(bytes.length <= length ? bytes : bytes.slice(0, length), length);
  function getEncodedSize(value, encoder) {
    return "fixedSize" in encoder ? encoder.fixedSize : encoder.getSizeFromValue(value);
  }
  function createEncoder(encoder) {
    return Object.freeze({
      ...encoder,
      encode: (value) => {
        const bytes = new Uint8Array(getEncodedSize(value, encoder));
        encoder.write(value, bytes, 0);
        return bytes;
      }
    });
  }
  function createDecoder(decoder) {
    return Object.freeze({
      ...decoder,
      decode: (bytes, offset = 0) => decoder.read(bytes, offset)[0]
    });
  }
  function isFixedSize(codec) {
    return "fixedSize" in codec && typeof codec.fixedSize === "number";
  }
  function combineCodec(encoder, decoder) {
    if (isFixedSize(encoder) !== isFixedSize(decoder)) {
      throw new Error(`Encoder and decoder must either both be fixed-size or variable-size.`);
    }
    if (isFixedSize(encoder) && isFixedSize(decoder) && encoder.fixedSize !== decoder.fixedSize) {
      throw new Error(
        `Encoder and decoder must have the same fixed size, got [${encoder.fixedSize}] and [${decoder.fixedSize}].`
      );
    }
    if (!isFixedSize(encoder) && !isFixedSize(decoder) && encoder.maxSize !== decoder.maxSize) {
      throw new Error(
        `Encoder and decoder must have the same max size, got [${encoder.maxSize}] and [${decoder.maxSize}].`
      );
    }
    return {
      ...decoder,
      ...encoder,
      decode: decoder.decode,
      encode: encoder.encode,
      read: decoder.read,
      write: encoder.write
    };
  }
  function fixEncoder(encoder, fixedBytes) {
    return createEncoder({
      fixedSize: fixedBytes,
      write: (value, bytes, offset) => {
        const variableByteArray = encoder.encode(value);
        const fixedByteArray = variableByteArray.length > fixedBytes ? variableByteArray.slice(0, fixedBytes) : variableByteArray;
        bytes.set(fixedByteArray, offset);
        return offset + fixedBytes;
      }
    });
  }
  function fixDecoder(decoder, fixedBytes) {
    return createDecoder({
      fixedSize: fixedBytes,
      read: (bytes, offset) => {
        assertByteArrayHasEnoughBytesForCodec("fixCodec", fixedBytes, bytes, offset);
        if (offset > 0 || bytes.length > fixedBytes) {
          bytes = bytes.slice(offset, offset + fixedBytes);
        }
        if (isFixedSize(decoder)) {
          bytes = fixBytes(bytes, decoder.fixedSize);
        }
        const [value] = decoder.read(bytes, 0);
        return [value, offset + fixedBytes];
      }
    });
  }

  // src/baseX.ts
  var getBaseXEncoder = (alphabet4) => {
    return createEncoder({
      getSizeFromValue: (value) => {
        const [leadingZeroes, tailChars] = partitionLeadingZeroes(value, alphabet4[0]);
        if (tailChars === "")
          return value.length;
        const base10Number = getBigIntFromBaseX(tailChars, alphabet4);
        return leadingZeroes.length + Math.ceil(base10Number.toString(16).length / 2);
      },
      write(value, bytes, offset) {
        assertValidBaseString(alphabet4, value);
        if (value === "")
          return offset;
        const [leadingZeroes, tailChars] = partitionLeadingZeroes(value, alphabet4[0]);
        if (tailChars === "") {
          bytes.set(new Uint8Array(leadingZeroes.length).fill(0), offset);
          return offset + leadingZeroes.length;
        }
        let base10Number = getBigIntFromBaseX(tailChars, alphabet4);
        const tailBytes = [];
        while (base10Number > 0n) {
          tailBytes.unshift(Number(base10Number % 256n));
          base10Number /= 256n;
        }
        const bytesToAdd = [...Array(leadingZeroes.length).fill(0), ...tailBytes];
        bytes.set(bytesToAdd, offset);
        return offset + bytesToAdd.length;
      }
    });
  };
  var getBaseXDecoder = (alphabet4) => {
    return createDecoder({
      read(rawBytes, offset) {
        const bytes = offset === 0 ? rawBytes : rawBytes.slice(offset);
        if (bytes.length === 0)
          return ["", 0];
        let trailIndex = bytes.findIndex((n) => n !== 0);
        trailIndex = trailIndex === -1 ? bytes.length : trailIndex;
        const leadingZeroes = alphabet4[0].repeat(trailIndex);
        if (trailIndex === bytes.length)
          return [leadingZeroes, rawBytes.length];
        const base10Number = bytes.slice(trailIndex).reduce((sum, byte) => sum * 256n + BigInt(byte), 0n);
        const tailChars = getBaseXFromBigInt(base10Number, alphabet4);
        return [leadingZeroes + tailChars, rawBytes.length];
      }
    });
  };
  var getBaseXCodec = (alphabet4) => combineCodec(getBaseXEncoder(alphabet4), getBaseXDecoder(alphabet4));
  function partitionLeadingZeroes(value, zeroCharacter) {
    const leadingZeroIndex = [...value].findIndex((c) => c !== zeroCharacter);
    return leadingZeroIndex === -1 ? [value, ""] : [value.slice(0, leadingZeroIndex), value.slice(leadingZeroIndex)];
  }
  function getBigIntFromBaseX(value, alphabet4) {
    const base = BigInt(alphabet4.length);
    return [...value].reduce((sum, char) => sum * base + BigInt(alphabet4.indexOf(char)), 0n);
  }
  function getBaseXFromBigInt(value, alphabet4) {
    const base = BigInt(alphabet4.length);
    const tailChars = [];
    while (value > 0n) {
      tailChars.unshift(alphabet4[Number(value % base)]);
      value /= base;
    }
    return tailChars.join("");
  }

  // src/base10.ts
  var alphabet = "0123456789";
  var getBase10Encoder = () => getBaseXEncoder(alphabet);
  var getBase10Decoder = () => getBaseXDecoder(alphabet);
  var getBase10Codec = () => getBaseXCodec(alphabet);

  // src/base16.ts
  var getBase16Encoder = () => createEncoder({
    getSizeFromValue: (value) => Math.ceil(value.length / 2),
    write(value, bytes, offset) {
      const lowercaseValue = value.toLowerCase();
      assertValidBaseString("0123456789abcdef", lowercaseValue, value);
      const matches = lowercaseValue.match(/.{1,2}/g);
      const hexBytes = matches ? matches.map((byte) => parseInt(byte, 16)) : [];
      bytes.set(hexBytes, offset);
      return hexBytes.length + offset;
    }
  });
  var getBase16Decoder = () => createDecoder({
    read(bytes, offset) {
      const value = bytes.slice(offset).reduce((str, byte) => str + byte.toString(16).padStart(2, "0"), "");
      return [value, bytes.length];
    }
  });
  var getBase16Codec = () => combineCodec(getBase16Encoder(), getBase16Decoder());

  // src/base58.ts
  var alphabet2 = "123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz";
  var getBase58Encoder = () => getBaseXEncoder(alphabet2);
  var getBase58Decoder = () => getBaseXDecoder(alphabet2);
  var getBase58Codec = () => getBaseXCodec(alphabet2);

  // src/baseX-reslice.ts
  var getBaseXResliceEncoder = (alphabet4, bits) => createEncoder({
    getSizeFromValue: (value) => Math.floor(value.length * bits / 8),
    write(value, bytes, offset) {
      assertValidBaseString(alphabet4, value);
      if (value === "")
        return offset;
      const charIndices = [...value].map((c) => alphabet4.indexOf(c));
      const reslicedBytes = reslice(charIndices, bits, 8, false);
      bytes.set(reslicedBytes, offset);
      return reslicedBytes.length + offset;
    }
  });
  var getBaseXResliceDecoder = (alphabet4, bits) => createDecoder({
    read(rawBytes, offset = 0) {
      const bytes = offset === 0 ? rawBytes : rawBytes.slice(offset);
      if (bytes.length === 0)
        return ["", rawBytes.length];
      const charIndices = reslice([...bytes], 8, bits, true);
      return [charIndices.map((i) => alphabet4[i]).join(""), rawBytes.length];
    }
  });
  var getBaseXResliceCodec = (alphabet4, bits) => combineCodec(getBaseXResliceEncoder(alphabet4, bits), getBaseXResliceDecoder(alphabet4, bits));
  function reslice(input, inputBits, outputBits, useRemainder) {
    const output = [];
    let accumulator = 0;
    let bitsInAccumulator = 0;
    const mask = (1 << outputBits) - 1;
    for (const value of input) {
      accumulator = accumulator << inputBits | value;
      bitsInAccumulator += inputBits;
      while (bitsInAccumulator >= outputBits) {
        bitsInAccumulator -= outputBits;
        output.push(accumulator >> bitsInAccumulator & mask);
      }
    }
    if (useRemainder && bitsInAccumulator > 0) {
      output.push(accumulator << outputBits - bitsInAccumulator & mask);
    }
    return output;
  }
  var getBase64Encoder = () => {
    {
      return createEncoder({
        getSizeFromValue: (value) => {
          try {
            return atob(value).length;
          } catch (e2) {
            throw new Error(`Expected a string of base 64, got [${value}].`);
          }
        },
        write(value, bytes, offset) {
          try {
            const bytesToAdd = atob(value).split("").map((c) => c.charCodeAt(0));
            bytes.set(bytesToAdd, offset);
            return bytesToAdd.length + offset;
          } catch (e2) {
            throw new Error(`Expected a string of base 64, got [${value}].`);
          }
        }
      });
    }
  };
  var getBase64Decoder = () => {
    {
      return createDecoder({
        read(bytes, offset = 0) {
          const slice = bytes.slice(offset);
          const value = btoa(String.fromCharCode(...slice));
          return [value, bytes.length];
        }
      });
    }
  };
  var getBase64Codec = () => combineCodec(getBase64Encoder(), getBase64Decoder());

  // src/null-characters.ts
  var removeNullCharacters = (value) => (
    // eslint-disable-next-line no-control-regex
    value.replace(/\u0000/g, "")
  );
  var padNullCharacters = (value, chars) => value.padEnd(chars, "\0");

  // ../codecs-numbers/dist/index.browser.js
  function assertNumberIsBetweenForCodec(codecDescription, min, max, value) {
    if (value < min || value > max) {
      throw new Error(
        `Codec [${codecDescription}] expected number to be in the range [${min}, ${max}], got ${value}.`
      );
    }
  }
  function isLittleEndian(config) {
    return (config == null ? void 0 : config.endian) === 1 ? false : true;
  }
  function numberEncoderFactory(input) {
    return createEncoder({
      fixedSize: input.size,
      write(value, bytes, offset) {
        if (input.range) {
          assertNumberIsBetweenForCodec(input.name, input.range[0], input.range[1], value);
        }
        const arrayBuffer = new ArrayBuffer(input.size);
        input.set(new DataView(arrayBuffer), value, isLittleEndian(input.config));
        bytes.set(new Uint8Array(arrayBuffer), offset);
        return offset + input.size;
      }
    });
  }
  function numberDecoderFactory(input) {
    return createDecoder({
      fixedSize: input.size,
      read(bytes, offset = 0) {
        assertByteArrayIsNotEmptyForCodec(input.name, bytes, offset);
        assertByteArrayHasEnoughBytesForCodec(input.name, input.size, bytes, offset);
        const view = new DataView(toArrayBuffer(bytes, offset, input.size));
        return [input.get(view, isLittleEndian(input.config)), offset + input.size];
      }
    });
  }
  function toArrayBuffer(bytes, offset, length) {
    const bytesOffset = bytes.byteOffset + (offset != null ? offset : 0);
    const bytesLength = length != null ? length : bytes.byteLength;
    return bytes.buffer.slice(bytesOffset, bytesOffset + bytesLength);
  }
  var getU32Encoder = (config = {}) => numberEncoderFactory({
    config,
    name: "u32",
    range: [0, Number("0xffffffff")],
    set: (view, value, le) => view.setUint32(0, value, le),
    size: 4
  });
  var getU32Decoder = (config = {}) => numberDecoderFactory({
    config,
    get: (view, le) => view.getUint32(0, le),
    name: "u32",
    size: 4
  });

  // ../text-encoding-impl/dist/index.browser.js
  var e = globalThis.TextDecoder;
  var o = globalThis.TextEncoder;

  // src/utf8.ts
  var getUtf8Encoder = () => {
    let textEncoder;
    return createEncoder({
      getSizeFromValue: (value) => (textEncoder || (textEncoder = new o())).encode(value).length,
      write: (value, bytes, offset) => {
        const bytesToAdd = (textEncoder || (textEncoder = new o())).encode(value);
        bytes.set(bytesToAdd, offset);
        return offset + bytesToAdd.length;
      }
    });
  };
  var getUtf8Decoder = () => {
    let textDecoder;
    return createDecoder({
      read(bytes, offset) {
        const value = (textDecoder || (textDecoder = new e())).decode(bytes.slice(offset));
        return [removeNullCharacters(value), bytes.length];
      }
    });
  };
  var getUtf8Codec = () => combineCodec(getUtf8Encoder(), getUtf8Decoder());

  // src/string.ts
  function getStringEncoder(config = {}) {
    var _a, _b;
    const size = (_a = config.size) != null ? _a : getU32Encoder();
    const encoding = (_b = config.encoding) != null ? _b : getUtf8Encoder();
    if (size === "variable") {
      return encoding;
    }
    if (typeof size === "number") {
      return fixEncoder(encoding, size);
    }
    return createEncoder({
      getSizeFromValue: (value) => {
        const contentSize = getEncodedSize(value, encoding);
        return getEncodedSize(contentSize, size) + contentSize;
      },
      write: (value, bytes, offset) => {
        const contentSize = getEncodedSize(value, encoding);
        offset = size.write(contentSize, bytes, offset);
        return encoding.write(value, bytes, offset);
      }
    });
  }
  function getStringDecoder(config = {}) {
    var _a, _b;
    const size = (_a = config.size) != null ? _a : getU32Decoder();
    const encoding = (_b = config.encoding) != null ? _b : getUtf8Decoder();
    if (size === "variable") {
      return encoding;
    }
    if (typeof size === "number") {
      return fixDecoder(encoding, size);
    }
    return createDecoder({
      read: (bytes, offset = 0) => {
        assertByteArrayIsNotEmptyForCodec("string", bytes, offset);
        const [lengthBigInt, lengthOffset] = size.read(bytes, offset);
        const length = Number(lengthBigInt);
        offset = lengthOffset;
        const contentBytes = bytes.slice(offset, offset + length);
        assertByteArrayHasEnoughBytesForCodec("string", length, contentBytes);
        const [value, contentOffset] = encoding.read(contentBytes, 0);
        offset += contentOffset;
        return [value, offset];
      }
    });
  }
  function getStringCodec(config = {}) {
    return combineCodec(getStringEncoder(config), getStringDecoder(config));
  }

  exports.assertValidBaseString = assertValidBaseString;
  exports.getBase10Codec = getBase10Codec;
  exports.getBase10Decoder = getBase10Decoder;
  exports.getBase10Encoder = getBase10Encoder;
  exports.getBase16Codec = getBase16Codec;
  exports.getBase16Decoder = getBase16Decoder;
  exports.getBase16Encoder = getBase16Encoder;
  exports.getBase58Codec = getBase58Codec;
  exports.getBase58Decoder = getBase58Decoder;
  exports.getBase58Encoder = getBase58Encoder;
  exports.getBase64Codec = getBase64Codec;
  exports.getBase64Decoder = getBase64Decoder;
  exports.getBase64Encoder = getBase64Encoder;
  exports.getBaseXCodec = getBaseXCodec;
  exports.getBaseXDecoder = getBaseXDecoder;
  exports.getBaseXEncoder = getBaseXEncoder;
  exports.getBaseXResliceCodec = getBaseXResliceCodec;
  exports.getBaseXResliceDecoder = getBaseXResliceDecoder;
  exports.getBaseXResliceEncoder = getBaseXResliceEncoder;
  exports.getStringCodec = getStringCodec;
  exports.getStringDecoder = getStringDecoder;
  exports.getStringEncoder = getStringEncoder;
  exports.getUtf8Codec = getUtf8Codec;
  exports.getUtf8Decoder = getUtf8Decoder;
  exports.getUtf8Encoder = getUtf8Encoder;
  exports.padNullCharacters = padNullCharacters;
  exports.removeNullCharacters = removeNullCharacters;

  return exports;

})({});
//# sourceMappingURL=out.js.map
//# sourceMappingURL=index.development.js.map