import { createEncoder, createDecoder, combineCodec, mapEncoder, mapDecoder, fixEncoder, getEncodedSize, fixDecoder, assertByteArrayIsNotEmptyForCodec, assertByteArrayHasEnoughBytesForCodec } from '@solana/codecs-core';
import { getU32Encoder, getU32Decoder } from '@solana/codecs-numbers';

// src/assertions.ts
function assertValidBaseString(alphabet4, testValue, givenValue = testValue) {
  if (!testValue.match(new RegExp(`^[${alphabet4}]*$`))) {
    throw new Error(`Expected a string of base ${alphabet4.length}, got [${givenValue}].`);
  }
}
var getBaseXEncoder = (alphabet4) => {
  return createEncoder({
    getSizeFromValue: (value) => {
      const [leadingZeroes, tailChars] = partitionLeadingZeroes(value, alphabet4[0]);
      if (tailChars === "")
        return value.length;
      const base10Number = getBigIntFromBaseX(tailChars, alphabet4);
      return leadingZeroes.length + Math.ceil(base10Number.toString(16).length / 2);
    },
    write(value, bytes, offset) {
      assertValidBaseString(alphabet4, value);
      if (value === "")
        return offset;
      const [leadingZeroes, tailChars] = partitionLeadingZeroes(value, alphabet4[0]);
      if (tailChars === "") {
        bytes.set(new Uint8Array(leadingZeroes.length).fill(0), offset);
        return offset + leadingZeroes.length;
      }
      let base10Number = getBigIntFromBaseX(tailChars, alphabet4);
      const tailBytes = [];
      while (base10Number > 0n) {
        tailBytes.unshift(Number(base10Number % 256n));
        base10Number /= 256n;
      }
      const bytesToAdd = [...Array(leadingZeroes.length).fill(0), ...tailBytes];
      bytes.set(bytesToAdd, offset);
      return offset + bytesToAdd.length;
    }
  });
};
var getBaseXDecoder = (alphabet4) => {
  return createDecoder({
    read(rawBytes, offset) {
      const bytes = offset === 0 ? rawBytes : rawBytes.slice(offset);
      if (bytes.length === 0)
        return ["", 0];
      let trailIndex = bytes.findIndex((n) => n !== 0);
      trailIndex = trailIndex === -1 ? bytes.length : trailIndex;
      const leadingZeroes = alphabet4[0].repeat(trailIndex);
      if (trailIndex === bytes.length)
        return [leadingZeroes, rawBytes.length];
      const base10Number = bytes.slice(trailIndex).reduce((sum, byte) => sum * 256n + BigInt(byte), 0n);
      const tailChars = getBaseXFromBigInt(base10Number, alphabet4);
      return [leadingZeroes + tailChars, rawBytes.length];
    }
  });
};
var getBaseXCodec = (alphabet4) => combineCodec(getBaseXEncoder(alphabet4), getBaseXDecoder(alphabet4));
function partitionLeadingZeroes(value, zeroCharacter) {
  const leadingZeroIndex = [...value].findIndex((c) => c !== zeroCharacter);
  return leadingZeroIndex === -1 ? [value, ""] : [value.slice(0, leadingZeroIndex), value.slice(leadingZeroIndex)];
}
function getBigIntFromBaseX(value, alphabet4) {
  const base = BigInt(alphabet4.length);
  return [...value].reduce((sum, char) => sum * base + BigInt(alphabet4.indexOf(char)), 0n);
}
function getBaseXFromBigInt(value, alphabet4) {
  const base = BigInt(alphabet4.length);
  const tailChars = [];
  while (value > 0n) {
    tailChars.unshift(alphabet4[Number(value % base)]);
    value /= base;
  }
  return tailChars.join("");
}

// src/base10.ts
var alphabet = "0123456789";
var getBase10Encoder = () => getBaseXEncoder(alphabet);
var getBase10Decoder = () => getBaseXDecoder(alphabet);
var getBase10Codec = () => getBaseXCodec(alphabet);
var getBase16Encoder = () => createEncoder({
  getSizeFromValue: (value) => Math.ceil(value.length / 2),
  write(value, bytes, offset) {
    const lowercaseValue = value.toLowerCase();
    assertValidBaseString("0123456789abcdef", lowercaseValue, value);
    const matches = lowercaseValue.match(/.{1,2}/g);
    const hexBytes = matches ? matches.map((byte) => parseInt(byte, 16)) : [];
    bytes.set(hexBytes, offset);
    return hexBytes.length + offset;
  }
});
var getBase16Decoder = () => createDecoder({
  read(bytes, offset) {
    const value = bytes.slice(offset).reduce((str, byte) => str + byte.toString(16).padStart(2, "0"), "");
    return [value, bytes.length];
  }
});
var getBase16Codec = () => combineCodec(getBase16Encoder(), getBase16Decoder());

// src/base58.ts
var alphabet2 = "123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz";
var getBase58Encoder = () => getBaseXEncoder(alphabet2);
var getBase58Decoder = () => getBaseXDecoder(alphabet2);
var getBase58Codec = () => getBaseXCodec(alphabet2);
var getBaseXResliceEncoder = (alphabet4, bits) => createEncoder({
  getSizeFromValue: (value) => Math.floor(value.length * bits / 8),
  write(value, bytes, offset) {
    assertValidBaseString(alphabet4, value);
    if (value === "")
      return offset;
    const charIndices = [...value].map((c) => alphabet4.indexOf(c));
    const reslicedBytes = reslice(charIndices, bits, 8, false);
    bytes.set(reslicedBytes, offset);
    return reslicedBytes.length + offset;
  }
});
var getBaseXResliceDecoder = (alphabet4, bits) => createDecoder({
  read(rawBytes, offset = 0) {
    const bytes = offset === 0 ? rawBytes : rawBytes.slice(offset);
    if (bytes.length === 0)
      return ["", rawBytes.length];
    const charIndices = reslice([...bytes], 8, bits, true);
    return [charIndices.map((i) => alphabet4[i]).join(""), rawBytes.length];
  }
});
var getBaseXResliceCodec = (alphabet4, bits) => combineCodec(getBaseXResliceEncoder(alphabet4, bits), getBaseXResliceDecoder(alphabet4, bits));
function reslice(input, inputBits, outputBits, useRemainder) {
  const output = [];
  let accumulator = 0;
  let bitsInAccumulator = 0;
  const mask = (1 << outputBits) - 1;
  for (const value of input) {
    accumulator = accumulator << inputBits | value;
    bitsInAccumulator += inputBits;
    while (bitsInAccumulator >= outputBits) {
      bitsInAccumulator -= outputBits;
      output.push(accumulator >> bitsInAccumulator & mask);
    }
  }
  if (useRemainder && bitsInAccumulator > 0) {
    output.push(accumulator << outputBits - bitsInAccumulator & mask);
  }
  return output;
}

// src/base64.ts
var alphabet3 = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";
var getBase64Encoder = () => {
  return mapEncoder(getBaseXResliceEncoder(alphabet3, 6), (value) => value.replace(/=/g, ""));
};
var getBase64Decoder = () => {
  return mapDecoder(
    getBaseXResliceDecoder(alphabet3, 6),
    (value) => value.padEnd(Math.ceil(value.length / 4) * 4, "=")
  );
};
var getBase64Codec = () => combineCodec(getBase64Encoder(), getBase64Decoder());

// src/null-characters.ts
var removeNullCharacters = (value) => (
  // eslint-disable-next-line no-control-regex
  value.replace(/\u0000/g, "")
);
var padNullCharacters = (value, chars) => value.padEnd(chars, "\0");

// ../text-encoding-impl/dist/index.browser.js
var e = globalThis.TextDecoder;
var o = globalThis.TextEncoder;

// src/utf8.ts
var getUtf8Encoder = () => {
  let textEncoder;
  return createEncoder({
    getSizeFromValue: (value) => (textEncoder ||= new o()).encode(value).length,
    write: (value, bytes, offset) => {
      const bytesToAdd = (textEncoder ||= new o()).encode(value);
      bytes.set(bytesToAdd, offset);
      return offset + bytesToAdd.length;
    }
  });
};
var getUtf8Decoder = () => {
  let textDecoder;
  return createDecoder({
    read(bytes, offset) {
      const value = (textDecoder ||= new e()).decode(bytes.slice(offset));
      return [removeNullCharacters(value), bytes.length];
    }
  });
};
var getUtf8Codec = () => combineCodec(getUtf8Encoder(), getUtf8Decoder());

// src/string.ts
function getStringEncoder(config = {}) {
  const size = config.size ?? getU32Encoder();
  const encoding = config.encoding ?? getUtf8Encoder();
  if (size === "variable") {
    return encoding;
  }
  if (typeof size === "number") {
    return fixEncoder(encoding, size);
  }
  return createEncoder({
    getSizeFromValue: (value) => {
      const contentSize = getEncodedSize(value, encoding);
      return getEncodedSize(contentSize, size) + contentSize;
    },
    write: (value, bytes, offset) => {
      const contentSize = getEncodedSize(value, encoding);
      offset = size.write(contentSize, bytes, offset);
      return encoding.write(value, bytes, offset);
    }
  });
}
function getStringDecoder(config = {}) {
  const size = config.size ?? getU32Decoder();
  const encoding = config.encoding ?? getUtf8Decoder();
  if (size === "variable") {
    return encoding;
  }
  if (typeof size === "number") {
    return fixDecoder(encoding, size);
  }
  return createDecoder({
    read: (bytes, offset = 0) => {
      assertByteArrayIsNotEmptyForCodec("string", bytes, offset);
      const [lengthBigInt, lengthOffset] = size.read(bytes, offset);
      const length = Number(lengthBigInt);
      offset = lengthOffset;
      const contentBytes = bytes.slice(offset, offset + length);
      assertByteArrayHasEnoughBytesForCodec("string", length, contentBytes);
      const [value, contentOffset] = encoding.read(contentBytes, 0);
      offset += contentOffset;
      return [value, offset];
    }
  });
}
function getStringCodec(config = {}) {
  return combineCodec(getStringEncoder(config), getStringDecoder(config));
}

export { assertValidBaseString, getBase10Codec, getBase10Decoder, getBase10Encoder, getBase16Codec, getBase16Decoder, getBase16Encoder, getBase58Codec, getBase58Decoder, getBase58Encoder, getBase64Codec, getBase64Decoder, getBase64Encoder, getBaseXCodec, getBaseXDecoder, getBaseXEncoder, getBaseXResliceCodec, getBaseXResliceDecoder, getBaseXResliceEncoder, getStringCodec, getStringDecoder, getStringEncoder, getUtf8Codec, getUtf8Decoder, getUtf8Encoder, padNullCharacters, removeNullCharacters };
