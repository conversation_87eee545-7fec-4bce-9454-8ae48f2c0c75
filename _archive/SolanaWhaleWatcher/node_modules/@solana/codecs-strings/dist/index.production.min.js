this.globalThis = this.globalThis || {};
this.globalThis.solanaWeb3 = (function (exports) {
	'use strict';

	function l(e,r,t=r){if(!r.match(new RegExp(`^[${e}]*$`)))throw new Error(`Expected a string of base ${e.length}, got [${t}].`)}function C(e,r,t=0){if(r.length-t<=0)throw new Error(`Codec [${e}] cannot decode empty byte arrays.`)}function z(e,r,t,n=0){let i=t.length-n;if(i<r)throw new Error(`Codec [${e}] expected ${r} bytes, got ${i}.`)}var Z=(e,r)=>{if(e.length>=r)return e;let t=new Uint8Array(r).fill(0);return t.set(e),t},H=(e,r)=>Z(e.length<=r?e:e.slice(0,r),r);function S(e,r){return "fixedSize"in r?r.fixedSize:r.getSizeFromValue(e)}function a(e){return Object.freeze({...e,encode:r=>{let t=new Uint8Array(S(r,e));return e.write(r,t,0),t}})}function s(e){return Object.freeze({...e,decode:(r,t=0)=>e.read(r,t)[0]})}function m(e){return "fixedSize"in e&&typeof e.fixedSize=="number"}function d(e,r){if(m(e)!==m(r))throw new Error("Encoder and decoder must either both be fixed-size or variable-size.");if(m(e)&&m(r)&&e.fixedSize!==r.fixedSize)throw new Error(`Encoder and decoder must have the same fixed size, got [${e.fixedSize}] and [${r.fixedSize}].`);if(!m(e)&&!m(r)&&e.maxSize!==r.maxSize)throw new Error(`Encoder and decoder must have the same max size, got [${e.maxSize}] and [${r.maxSize}].`);return {...r,...e,decode:r.decode,encode:e.encode,read:r.read,write:e.write}}function F(e,r){return a({fixedSize:r,write:(t,n,i)=>{let c=e.encode(t),o=c.length>r?c.slice(0,r):c;return n.set(o,i),i+r}})}function N(e,r){return s({fixedSize:r,read:(t,n)=>{z("fixCodec",r,t,n),(n>0||t.length>r)&&(t=t.slice(n,n+r)),m(e)&&(t=H(t,e.fixedSize));let[i]=e.read(t,0);return [i,n+r]}})}var x=e=>a({getSizeFromValue:r=>{let[t,n]=A(r,e[0]);if(n==="")return r.length;let i=O(n,e);return t.length+Math.ceil(i.toString(16).length/2)},write(r,t,n){if(l(e,r),r==="")return n;let[i,c]=A(r,e[0]);if(c==="")return t.set(new Uint8Array(i.length).fill(0),n),n+i.length;let o=O(c,e),g=[];for(;o>0n;)g.unshift(Number(o%256n)),o/=256n;let u=[...Array(i.length).fill(0),...g];return t.set(u,n),n+u.length}}),b=e=>s({read(r,t){let n=t===0?r:r.slice(t);if(n.length===0)return ["",0];let i=n.findIndex(u=>u!==0);i=i===-1?n.length:i;let c=e[0].repeat(i);if(i===n.length)return [c,r.length];let o=n.slice(i).reduce((u,E)=>u*256n+BigInt(E),0n),g=J(o,e);return [c+g,r.length]}}),h=e=>d(x(e),b(e));function A(e,r){let t=[...e].findIndex(n=>n!==r);return t===-1?[e,""]:[e.slice(0,t),e.slice(t)]}function O(e,r){let t=BigInt(r.length);return [...e].reduce((n,i)=>n*t+BigInt(r.indexOf(i)),0n)}function J(e,r){let t=BigInt(r.length),n=[];for(;e>0n;)n.unshift(r[Number(e%t)]),e/=t;return n.join("")}var p="0123456789",be=()=>x(p),Ee=()=>b(p),Ce=()=>h(p);var P=()=>a({getSizeFromValue:e=>Math.ceil(e.length/2),write(e,r,t){let n=e.toLowerCase();l("0123456789abcdef",n,e);let i=n.match(/.{1,2}/g),c=i?i.map(o=>parseInt(o,16)):[];return r.set(c,t),c.length+t}}),q=()=>s({read(e,r){return [e.slice(r).reduce((n,i)=>n+i.toString(16).padStart(2,"0"),""),e.length]}}),Ve=()=>d(P(),q());var D="123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz",Te=()=>x(D),ye=()=>b(D),Ae=()=>h(D);var B=(e,r)=>a({getSizeFromValue:t=>Math.floor(t.length*r/8),write(t,n,i){if(l(e,t),t==="")return i;let c=[...t].map(g=>e.indexOf(g)),o=_(c,r,8,!1);return n.set(o,i),o.length+i}}),I=(e,r)=>s({read(t,n=0){let i=n===0?t:t.slice(n);return i.length===0?["",t.length]:[_([...i],8,r,!0).map(o=>e[o]).join(""),t.length]}}),je=(e,r)=>d(B(e,r),I(e,r));function _(e,r,t,n){let i=[],c=0,o=0,g=(1<<t)-1;for(let u of e)for(c=c<<r|u,o+=r;o>=t;)o-=t,i.push(c>>o&g);return n&&o>0&&i.push(c<<t-o&g),i}var K=()=>a({getSizeFromValue:e=>{try{return atob(e).length}catch{throw new Error(`Expected a string of base 64, got [${e}].`)}},write(e,r,t){try{let n=atob(e).split("").map(i=>i.charCodeAt(0));return r.set(n,t),n.length+t}catch{throw new Error(`Expected a string of base 64, got [${e}].`)}}}),Q=()=>s({read(e,r=0){let t=e.slice(r);return [btoa(String.fromCharCode(...t)),e.length]}}),qe=()=>d(K(),Q());var $=e=>e.replace(/\u0000/g,""),Ye=(e,r)=>e.padEnd(r,"\0");function Y(e,r,t,n){if(n<r||n>t)throw new Error(`Codec [${e}] expected number to be in the range [${r}, ${t}], got ${n}.`)}function L(e){return (e==null?void 0:e.endian)!==1}function ee(e){return a({fixedSize:e.size,write(r,t,n){e.range&&Y(e.name,e.range[0],e.range[1],r);let i=new ArrayBuffer(e.size);return e.set(new DataView(i),r,L(e.config)),t.set(new Uint8Array(i),n),n+e.size}})}function re(e){return s({fixedSize:e.size,read(r,t=0){C(e.name,r,t),z(e.name,e.size,r,t);let n=new DataView(te(r,t,e.size));return [e.get(n,L(e.config)),t+e.size]}})}function te(e,r,t){let n=e.byteOffset+(r!=null?r:0),i=t!=null?t:e.byteLength;return e.buffer.slice(n,n+i)}var R=(e={})=>ee({config:e,name:"u32",range:[0,+"0xffffffff"],set:(r,t,n)=>r.setUint32(0,t,n),size:4}),M=(e={})=>re({config:e,get:(r,t)=>r.getUint32(0,t),name:"u32",size:4});var j=globalThis.TextDecoder,v=globalThis.TextEncoder;var w=()=>{let e;return a({getSizeFromValue:r=>(e||(e=new v)).encode(r).length,write:(r,t,n)=>{let i=(e||(e=new v)).encode(r);return t.set(i,n),n+i.length}})},V=()=>{let e;return s({read(r,t){let n=(e||(e=new j)).decode(r.slice(t));return [$(n),r.length]}})},mr=()=>d(w(),V());function ne(e={}){var n,i;let r=(n=e.size)!=null?n:R(),t=(i=e.encoding)!=null?i:w();return r==="variable"?t:typeof r=="number"?F(t,r):a({getSizeFromValue:c=>{let o=S(c,t);return S(o,r)+o},write:(c,o,g)=>{let u=S(c,t);return g=r.write(u,o,g),t.write(c,o,g)}})}function ie(e={}){var n,i;let r=(n=e.size)!=null?n:M(),t=(i=e.encoding)!=null?i:V();return r==="variable"?t:typeof r=="number"?N(t,r):s({read:(c,o=0)=>{C("string",c,o);let[g,u]=r.read(c,o),E=Number(g);o=u;let U=c.slice(o,o+E);z("string",E,U);let[k,W]=t.read(U,0);return o+=W,[k,o]}})}function Nr(e={}){return d(ne(e),ie(e))}

	exports.assertValidBaseString = l;
	exports.getBase10Codec = Ce;
	exports.getBase10Decoder = Ee;
	exports.getBase10Encoder = be;
	exports.getBase16Codec = Ve;
	exports.getBase16Decoder = q;
	exports.getBase16Encoder = P;
	exports.getBase58Codec = Ae;
	exports.getBase58Decoder = ye;
	exports.getBase58Encoder = Te;
	exports.getBase64Codec = qe;
	exports.getBase64Decoder = Q;
	exports.getBase64Encoder = K;
	exports.getBaseXCodec = h;
	exports.getBaseXDecoder = b;
	exports.getBaseXEncoder = x;
	exports.getBaseXResliceCodec = je;
	exports.getBaseXResliceDecoder = I;
	exports.getBaseXResliceEncoder = B;
	exports.getStringCodec = Nr;
	exports.getStringDecoder = ie;
	exports.getStringEncoder = ne;
	exports.getUtf8Codec = mr;
	exports.getUtf8Decoder = V;
	exports.getUtf8Encoder = w;
	exports.padNullCharacters = Ye;
	exports.removeNullCharacters = $;

	return exports;

})({});
