{"version": 3, "sources": ["../src/assertions.ts", "../../codecs-core/src/assertions.ts", "../../codecs-core/src/bytes.ts", "../../codecs-core/src/codec.ts", "../../codecs-core/src/combine-codec.ts", "../../codecs-core/src/fix-codec.ts", "../../codecs-core/src/map-codec.ts", "../src/baseX.ts", "../src/base10.ts", "../src/base16.ts", "../src/base58.ts", "../src/baseX-reslice.ts", "../src/base64.ts", "../src/null-characters.ts", "../../codecs-numbers/src/assertions.ts", "../../codecs-numbers/src/f32.ts", "../../codecs-numbers/src/utils.ts", "../../codecs-numbers/src/u128.ts", "../../text-encoding-impl/src/index.browser.ts", "../src/utf8.ts", "../src/string.ts"], "names": ["alphabet", "e", "TextDecoder", "TextEncoder"], "mappings": ";AAGO,SAAS,sBAAsBA,WAAkB,WAAmB,aAAa,WAAW;AAC/F,MAAI,CAAC,UAAU,MAAM,IAAI,OAAO,KAAKA,SAAQ,KAAK,CAAC,GAAG;AAElD,UAAM,IAAI,MAAM,6BAA6BA,UAAS,MAAM,UAAU,UAAU,IAAI;AAAA,EACxF;AACJ;;;ACLO,SAAS,kCAAkC,kBAA0B,OAAmB,SAAS,GAAG;AACvG,MAAI,MAAM,SAAS,UAAU,GAAG;AAE5B,UAAM,IAAI,MAAM,UAAU,gBAAgB,oCAAoC;EAClF;AACJ;AAKO,SAAS,sCACZ,kBACA,UACA,OACA,SAAS,GACX;AACE,QAAM,cAAc,MAAM,SAAS;AACnC,MAAI,cAAc,UAAU;AAExB,UAAM,IAAI,MAAM,UAAU,gBAAgB,cAAc,QAAQ,eAAe,WAAW,GAAG;EACjG;AACJ;ACIO,IAAM,WAAW,CAAC,OAAmB,WAA+B;AACvE,MAAI,MAAM,UAAU;AAAQ,WAAO;AACnC,QAAM,cAAc,IAAI,WAAW,MAAM,EAAE,KAAK,CAAC;AACjD,cAAY,IAAI,KAAK;AACrB,SAAO;AACX;AAOO,IAAM,WAAW,CAAC,OAAmB,WACxC,SAAS,MAAM,UAAU,SAAS,QAAQ,MAAM,MAAM,GAAG,MAAM,GAAG,MAAM;ACsCrE,SAAS,eACZ,OACA,SACM;AACN,SAAO,eAAe,UAAU,QAAQ,YAAY,QAAQ,iBAAiB,KAAK;AACtF;AAUO,SAAS,cACZ,SACc;AACd,SAAO,OAAO,OAAO;IACjB,GAAG;IACH,QAAQ,CAAA,UAAS;AACb,YAAM,QAAQ,IAAI,WAAW,eAAe,OAAO,OAAO,CAAC;AAC3D,cAAQ,MAAM,OAAO,OAAO,CAAC;AAC7B,aAAO;IACX;EACJ,CAAC;AACL;AAUO,SAAS,cACZ,SACY;AACZ,SAAO,OAAO,OAAO;IACjB,GAAG;IACH,QAAQ,CAAC,OAAO,SAAS,MAAM,QAAQ,KAAK,OAAO,MAAM,EAAE,CAAC;EAChE,CAAC;AACL;AA0CO,SAAS,YAAY,OAAqF;AAC7G,SAAO,eAAe,SAAS,OAAO,MAAM,cAAc;AAC9D;AAkCO,SAAS,eAAe,OAAoF;AAC/G,SAAO,CAAC,YAAY,KAAK;AAC7B;AC5KO,SAAS,aACZ,SACA,SACiB;AACjB,MAAI,YAAY,OAAO,MAAM,YAAY,OAAO,GAAG;AAE/C,UAAM,IAAI,MAAM,sEAAsE;EAC1F;AAEA,MAAI,YAAY,OAAO,KAAK,YAAY,OAAO,KAAK,QAAQ,cAAc,QAAQ,WAAW;AAEzF,UAAM,IAAI;MACN,2DAA2D,QAAQ,SAAS,UAAU,QAAQ,SAAS;IAC3G;EACJ;AAEA,MAAI,CAAC,YAAY,OAAO,KAAK,CAAC,YAAY,OAAO,KAAK,QAAQ,YAAY,QAAQ,SAAS;AAEvF,UAAM,IAAI;MACN,yDAAyD,QAAQ,OAAO,UAAU,QAAQ,OAAO;IACrG;EACJ;AAEA,SAAO;IACH,GAAG;IACH,GAAG;IACH,QAAQ,QAAQ;IAChB,QAAQ,QAAQ;IAChB,MAAM,QAAQ;IACd,OAAO,QAAQ;EACnB;AACJ;ACvCO,SAAS,WACZ,SACA,YAC8B;AAC9B,SAAO,cAAc;IACjB,WAAW;IACX,OAAO,CAAC,OAAc,OAAmB,WAAmB;AAIxD,YAAM,oBAAoB,QAAQ,OAAO,KAAK;AAC9C,YAAM,iBACF,kBAAkB,SAAS,aAAa,kBAAkB,MAAM,GAAG,UAAU,IAAI;AACrF,YAAM,IAAI,gBAAgB,MAAM;AAChC,aAAO,SAAS;IACpB;EACJ,CAAC;AACL;AAQO,SAAS,WACZ,SACA,YAC4B;AAC5B,SAAO,cAAc;IACjB,WAAW;IACX,MAAM,CAAC,OAAmB,WAAmB;AACzC,4CAAsC,YAAY,YAAY,OAAO,MAAM;AAE3E,UAAI,SAAS,KAAK,MAAM,SAAS,YAAY;AACzC,gBAAQ,MAAM,MAAM,QAAQ,SAAS,UAAU;MACnD;AAEA,UAAI,YAAY,OAAO,GAAG;AACtB,gBAAQ,SAAS,OAAO,QAAQ,SAAS;MAC7C;AAEA,YAAM,CAAC,KAAK,IAAI,QAAQ,KAAK,OAAO,CAAC;AACrC,aAAO,CAAC,OAAO,SAAS,UAAU;IACtC;EACJ,CAAC;AACL;ACrCO,SAAS,WACZ,SACA,OACiB;AACjB,SAAO,cAAc;IACjB,GAAI,eAAe,OAAO,IACpB,EAAE,GAAG,SAAS,kBAAkB,CAAC,UAAoB,QAAQ,iBAAiB,MAAM,KAAK,CAAC,EAAE,IAC5F;IACN,OAAO,CAAC,OAAiB,OAAO,WAAW,QAAQ,MAAM,MAAM,KAAK,GAAG,OAAO,MAAM;EACxF,CAAC;AACL;AAiBO,SAAS,WACZ,SACA,KACe;AACf,SAAO,cAAc;IACjB,GAAG;IACH,MAAM,CAAC,OAAmB,WAAW;AACjC,YAAM,CAAC,OAAO,SAAS,IAAI,QAAQ,KAAK,OAAO,MAAM;AACrD,aAAO,CAAC,IAAI,OAAO,OAAO,MAAM,GAAG,SAAS;IAChD;EACJ,CAAC;AACL;;;ACrDO,IAAM,kBAAkB,CAACA,cAAkD;AAC9E,SAAO,cAAc;AAAA,IACjB,kBAAkB,CAAC,UAA0B;AACzC,YAAM,CAAC,eAAe,SAAS,IAAI,uBAAuB,OAAOA,UAAS,CAAC,CAAC;AAC5E,UAAI,cAAc;AAAI,eAAO,MAAM;AAEnC,YAAM,eAAe,mBAAmB,WAAWA,SAAQ;AAC3D,aAAO,cAAc,SAAS,KAAK,KAAK,aAAa,SAAS,EAAE,EAAE,SAAS,CAAC;AAAA,IAChF;AAAA,IACA,MAAM,OAAe,OAAO,QAAQ;AAEhC,4BAAsBA,WAAU,KAAK;AACrC,UAAI,UAAU;AAAI,eAAO;AAGzB,YAAM,CAAC,eAAe,SAAS,IAAI,uBAAuB,OAAOA,UAAS,CAAC,CAAC;AAC5E,UAAI,cAAc,IAAI;AAClB,cAAM,IAAI,IAAI,WAAW,cAAc,MAAM,EAAE,KAAK,CAAC,GAAG,MAAM;AAC9D,eAAO,SAAS,cAAc;AAAA,MAClC;AAGA,UAAI,eAAe,mBAAmB,WAAWA,SAAQ;AAGzD,YAAM,YAAsB,CAAC;AAC7B,aAAO,eAAe,IAAI;AACtB,kBAAU,QAAQ,OAAO,eAAe,IAAI,CAAC;AAC7C,wBAAgB;AAAA,MACpB;AAEA,YAAM,aAAa,CAAC,GAAG,MAAM,cAAc,MAAM,EAAE,KAAK,CAAC,GAAG,GAAG,SAAS;AACxE,YAAM,IAAI,YAAY,MAAM;AAC5B,aAAO,SAAS,WAAW;AAAA,IAC/B;AAAA,EACJ,CAAC;AACL;AAOO,IAAM,kBAAkB,CAACA,cAAkD;AAC9E,SAAO,cAAc;AAAA,IACjB,KAAK,UAAU,QAA0B;AACrC,YAAM,QAAQ,WAAW,IAAI,WAAW,SAAS,MAAM,MAAM;AAC7D,UAAI,MAAM,WAAW;AAAG,eAAO,CAAC,IAAI,CAAC;AAGrC,UAAI,aAAa,MAAM,UAAU,OAAK,MAAM,CAAC;AAC7C,mBAAa,eAAe,KAAK,MAAM,SAAS;AAChD,YAAM,gBAAgBA,UAAS,CAAC,EAAE,OAAO,UAAU;AACnD,UAAI,eAAe,MAAM;AAAQ,eAAO,CAAC,eAAe,SAAS,MAAM;AAGvE,YAAM,eAAe,MAAM,MAAM,UAAU,EAAE,OAAO,CAAC,KAAK,SAAS,MAAM,OAAO,OAAO,IAAI,GAAG,EAAE;AAGhG,YAAM,YAAY,mBAAmB,cAAcA,SAAQ;AAE3D,aAAO,CAAC,gBAAgB,WAAW,SAAS,MAAM;AAAA,IACtD;AAAA,EACJ,CAAC;AACL;AAWO,IAAM,gBAAgB,CAACA,cAC1B,aAAa,gBAAgBA,SAAQ,GAAG,gBAAgBA,SAAQ,CAAC;AAErE,SAAS,uBAAuB,OAAe,eAAyC;AACpF,QAAM,mBAAmB,CAAC,GAAG,KAAK,EAAE,UAAU,OAAK,MAAM,aAAa;AACtE,SAAO,qBAAqB,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,MAAM,GAAG,gBAAgB,GAAG,MAAM,MAAM,gBAAgB,CAAC;AACnH;AAEA,SAAS,mBAAmB,OAAeA,WAA0B;AACjE,QAAM,OAAO,OAAOA,UAAS,MAAM;AACnC,SAAO,CAAC,GAAG,KAAK,EAAE,OAAO,CAAC,KAAK,SAAS,MAAM,OAAO,OAAOA,UAAS,QAAQ,IAAI,CAAC,GAAG,EAAE;AAC3F;AAEA,SAAS,mBAAmB,OAAeA,WAA0B;AACjE,QAAM,OAAO,OAAOA,UAAS,MAAM;AACnC,QAAM,YAAY,CAAC;AACnB,SAAO,QAAQ,IAAI;AACf,cAAU,QAAQA,UAAS,OAAO,QAAQ,IAAI,CAAC,CAAC;AAChD,aAAS;AAAA,EACb;AACA,SAAO,UAAU,KAAK,EAAE;AAC5B;;;AC9GA,IAAM,WAAW;AAGV,IAAM,mBAAmB,MAAM,gBAAgB,QAAQ;AAGvD,IAAM,mBAAmB,MAAM,gBAAgB,QAAQ;AAGvD,IAAM,iBAAiB,MAAM,cAAc,QAAQ;;;ACCnD,IAAM,mBAAmB,MAC5B,cAAc;AAAA,EACV,kBAAkB,CAAC,UAAkB,KAAK,KAAK,MAAM,SAAS,CAAC;AAAA,EAC/D,MAAM,OAAe,OAAO,QAAQ;AAChC,UAAM,iBAAiB,MAAM,YAAY;AACzC,0BAAsB,oBAAoB,gBAAgB,KAAK;AAC/D,UAAM,UAAU,eAAe,MAAM,SAAS;AAC9C,UAAM,WAAW,UAAU,QAAQ,IAAI,CAAC,SAAiB,SAAS,MAAM,EAAE,CAAC,IAAI,CAAC;AAChF,UAAM,IAAI,UAAU,MAAM;AAC1B,WAAO,SAAS,SAAS;AAAA,EAC7B;AACJ,CAAC;AAGE,IAAM,mBAAmB,MAC5B,cAAc;AAAA,EACV,KAAK,OAAO,QAAQ;AAChB,UAAM,QAAQ,MAAM,MAAM,MAAM,EAAE,OAAO,CAAC,KAAK,SAAS,MAAM,KAAK,SAAS,EAAE,EAAE,SAAS,GAAG,GAAG,GAAG,EAAE;AACpG,WAAO,CAAC,OAAO,MAAM,MAAM;AAAA,EAC/B;AACJ,CAAC;AAGE,IAAM,iBAAiB,MAAiC,aAAa,iBAAiB,GAAG,iBAAiB,CAAC;;;ACjClH,IAAMA,YAAW;AAGV,IAAM,mBAAmB,MAAM,gBAAgBA,SAAQ;AAGvD,IAAM,mBAAmB,MAAM,gBAAgBA,SAAQ;AAGvD,IAAM,iBAAiB,MAAM,cAAcA,SAAQ;;;ACInD,IAAM,yBAAyB,CAACA,WAAkB,SACrD,cAAc;AAAA,EACV,kBAAkB,CAAC,UAAkB,KAAK,MAAO,MAAM,SAAS,OAAQ,CAAC;AAAA,EACzE,MAAM,OAAe,OAAO,QAAQ;AAChC,0BAAsBA,WAAU,KAAK;AACrC,QAAI,UAAU;AAAI,aAAO;AACzB,UAAM,cAAc,CAAC,GAAG,KAAK,EAAE,IAAI,OAAKA,UAAS,QAAQ,CAAC,CAAC;AAC3D,UAAM,gBAAgB,QAAQ,aAAa,MAAM,GAAG,KAAK;AACzD,UAAM,IAAI,eAAe,MAAM;AAC/B,WAAO,cAAc,SAAS;AAAA,EAClC;AACJ,CAAC;AAME,IAAM,yBAAyB,CAACA,WAAkB,SACrD,cAAc;AAAA,EACV,KAAK,UAAU,SAAS,GAAqB;AACzC,UAAM,QAAQ,WAAW,IAAI,WAAW,SAAS,MAAM,MAAM;AAC7D,QAAI,MAAM,WAAW;AAAG,aAAO,CAAC,IAAI,SAAS,MAAM;AACnD,UAAM,cAAc,QAAQ,CAAC,GAAG,KAAK,GAAG,GAAG,MAAM,IAAI;AACrD,WAAO,CAAC,YAAY,IAAI,OAAKA,UAAS,CAAC,CAAC,EAAE,KAAK,EAAE,GAAG,SAAS,MAAM;AAAA,EACvE;AACJ,CAAC;AASE,IAAM,uBAAuB,CAACA,WAAkB,SACnD,aAAa,uBAAuBA,WAAU,IAAI,GAAG,uBAAuBA,WAAU,IAAI,CAAC;AAG/F,SAAS,QAAQ,OAAiB,WAAmB,YAAoB,cAAiC;AACtG,QAAM,SAAS,CAAC;AAChB,MAAI,cAAc;AAClB,MAAI,oBAAoB;AACxB,QAAM,QAAQ,KAAK,cAAc;AACjC,aAAW,SAAS,OAAO;AACvB,kBAAe,eAAe,YAAa;AAC3C,yBAAqB;AACrB,WAAO,qBAAqB,YAAY;AACpC,2BAAqB;AACrB,aAAO,KAAM,eAAe,oBAAqB,IAAI;AAAA,IACzD;AAAA,EACJ;AACA,MAAI,gBAAgB,oBAAoB,GAAG;AACvC,WAAO,KAAM,eAAgB,aAAa,oBAAsB,IAAI;AAAA,EACxE;AACA,SAAO;AACX;;;ACxDA,IAAMA,YAAW;AAGV,IAAM,mBAAmB,MAAmC;AAC/D,MAAI,MAAa;AACb,WAAO,cAAc;AAAA,MACjB,kBAAkB,CAAC,UAAkB;AACjC,YAAI;AACA,iBAAQ,KAAwB,KAAK,EAAE;AAAA,QAC3C,SAASC,IAAG;AAER,gBAAM,IAAI,MAAM,sCAAsC,KAAK,IAAI;AAAA,QACnE;AAAA,MACJ;AAAA,MACA,MAAM,OAAe,OAAO,QAAQ;AAChC,YAAI;AACA,gBAAM,aAAc,KAAwB,KAAK,EAC5C,MAAM,EAAE,EACR,IAAI,OAAK,EAAE,WAAW,CAAC,CAAC;AAC7B,gBAAM,IAAI,YAAY,MAAM;AAC5B,iBAAO,WAAW,SAAS;AAAA,QAC/B,SAASA,IAAG;AAER,gBAAM,IAAI,MAAM,sCAAsC,KAAK,IAAI;AAAA,QACnE;AAAA,MACJ;AAAA,IACJ,CAAC;AAAA,EACL;AAEA,MAAI,OAAY;AACZ,WAAO,cAAc;AAAA,MACjB,kBAAkB,CAAC,UAAkB,OAAO,KAAK,OAAO,QAAQ,EAAE;AAAA,MAClE,MAAM,OAAe,OAAO,QAAQ;AAChC,8BAAsBD,WAAU,MAAM,QAAQ,MAAM,EAAE,CAAC;AACvD,cAAM,SAAS,OAAO,KAAK,OAAO,QAAQ;AAC1C,cAAM,IAAI,QAAQ,MAAM;AACxB,eAAO,OAAO,SAAS;AAAA,MAC3B;AAAA,IACJ,CAAC;AAAA,EACL;AAEA,SAAO,WAAW,uBAAuBA,WAAU,CAAC,GAAG,CAAC,UAA0B,MAAM,QAAQ,MAAM,EAAE,CAAC;AAC7G;AAGO,IAAM,mBAAmB,MAAmC;AAC/D,MAAI,MAAa;AACb,WAAO,cAAc;AAAA,MACjB,KAAK,OAAO,SAAS,GAAG;AACpB,cAAM,QAAQ,MAAM,MAAM,MAAM;AAChC,cAAM,QAAS,KAAwB,OAAO,aAAa,GAAG,KAAK,CAAC;AACpE,eAAO,CAAC,OAAO,MAAM,MAAM;AAAA,MAC/B;AAAA,IACJ,CAAC;AAAA,EACL;AAEA,MAAI,OAAY;AACZ,WAAO,cAAc;AAAA,MACjB,MAAM,CAAC,OAAO,SAAS,MAAM,CAAC,OAAO,KAAK,OAAO,MAAM,EAAE,SAAS,QAAQ,GAAG,MAAM,MAAM;AAAA,IAC7F,CAAC;AAAA,EACL;AAEA,SAAO;AAAA,IAAW,uBAAuBA,WAAU,CAAC;AAAA,IAAG,CAAC,UACpD,MAAM,OAAO,KAAK,KAAK,MAAM,SAAS,CAAC,IAAI,GAAG,GAAG;AAAA,EACrD;AACJ;AAGO,IAAM,iBAAiB,MAAiC,aAAa,iBAAiB,GAAG,iBAAiB,CAAC;;;ACjF3G,IAAM,uBAAuB,CAAC;AAAA;AAAA,EAEjC,MAAM,QAAQ,WAAW,EAAE;AAAA;AAGxB,IAAM,oBAAoB,CAAC,OAAe,UAAkB,MAAM,OAAO,OAAO,IAAQ;;;ACKvF,SAAA,8BAAU,kBAAA,KAAA,KAAA,OAAA;AAAA,MACN,QAAA,OAAU,QAAA,KAAgB;AAC9B,UAAA,IAAA;MACJ,UAAA,gBAAA,yCAAA,GAAA,KAAA,GAAA,UAAA,KAAA;IACJ;;;ACfA,SAAS,eAAA,QAAwE;;;ACAjF,SAAA,qBAAA,OAAA;AACI,SAAA,cAAA;IACA,WAAA,MAAA;IACA,MAAA,OAAA,OAAA,QAAA;AACA,UAAA,MAAA,OAAA;AAIG,sCAAA,MAAA,MAAA,MAAA,MAAA,CAAA,GAAA,MAAA,MAAA,CAAA,GAAA,KAAA;MAoBP;AACI,YAAO,cAAQ,IAAA,YAAwB,MAAA,IAAQ;AACnD,YAAA,IAAA,IAAA,SAAA,WAAA,GAAA,OAAA,eAAA,MAAA,MAAA,CAAA;AAEO,YAAS,IAAA,IAAA,WACZ,WAC8B,GAAA,MAAA;AAC9B,aAAO,SAAA,MAAc;IACjB;EAAiB,CAAA;AAEb;AACI,SAAA,qBAAA,OAAA;AAA+E,SACnF,cAAA;IACA,WAAM,MAAA;IACN,KAAA,OAAU,SAAI,GAAA;AACd,wCAAyB,MAAc,MAAM,OAAA,MAAA;AAC7C,4CAAsB,MAAA,MAAA,MAAA,MAAA,OAAA,MAAA;AAC1B,YAAA,OAAA,IAAA,SAAA,cAAA,OAAA,QAAA,MAAA,IAAA,CAAA;AACH,aAAA,CAAA,MAAA,IAAA,MAAA,eAAA,MAAA,MAAA,CAAA,GAAA,SAAA,MAAA,IAAA;IACL;EAEO,CAAA;AAGH;AAAqB,SACjB,cAAiB,OAAA,QAAA,QAAA;AAAA,QACjB,cAAY,MAA2B,cAAA,0BAAA;AACnC,QAAA,cAAA,0BAAA,MAAA;AACA,SAAA,MAAA,OAAA,MAAA,aAAA,cAA4C,WAAY;AACxD;ACjDE,IACN,gBAAkB,CAAA,SAAA,CAAA,MAAA,qBAAqC;EACvD;EACI,MAAA;EACA,OAAA,CAAM,GAAA,OAAA,YAAmB,CAAI;EAC7B,KAAA,CAAA,MAAM,OAAA,OAAY,KAAA,UAAA,GAAA,OAAA,EAAA;EAClB,MAAA;AACA,CAAA;AAA4D,IAChE,gBAAA,CAAA,SAAA,CAAA,MAAA,qBAAA;EACA;EACH,KAAA,CAAA,MAAA,OAAA,KAAA,UAAA,GAAA,EAAA;EAEE,MAAM;EAEL,MAAA;AAAA,CAAA;;;ACtBD,IAAME,IAAc,WAAW;AAA/B,IACMC,IAAc,WAAW;;;ACY/B,IAAM,iBAAiB,MAAmC;AAC7D,MAAI;AACJ,SAAO,cAAc;AAAA,IACjB,kBAAkB,YAAU,8BAAgB,IAAI,EAAY,IAAG,OAAO,KAAK,EAAE;AAAA,IAC7E,OAAO,CAAC,OAAe,OAAO,WAAW;AACrC,YAAM,cAAc,8BAAgB,IAAI,EAAY,IAAG,OAAO,KAAK;AACnE,YAAM,IAAI,YAAY,MAAM;AAC5B,aAAO,SAAS,WAAW;AAAA,IAC/B;AAAA,EACJ,CAAC;AACL;AAGO,IAAM,iBAAiB,MAAmC;AAC7D,MAAI;AACJ,SAAO,cAAc;AAAA,IACjB,KAAK,OAAO,QAAQ;AAChB,YAAM,SAAS,8BAAgB,IAAI,EAAY,IAAG,OAAO,MAAM,MAAM,MAAM,CAAC;AAC5E,aAAO,CAAC,qBAAqB,KAAK,GAAG,MAAM,MAAM;AAAA,IACrD;AAAA,EACJ,CAAC;AACL;AAGO,IAAM,eAAe,MAAqB,aAAa,eAAe,GAAG,eAAe,CAAC;;;ACoBzF,SAAS,iBAAiB,SAA4D,CAAC,GAAoB;AAzDlH;AA0DI,QAAM,QAAO,YAAO,SAAP,YAAe,cAAc;AAC1C,QAAM,YAAW,YAAO,aAAP,YAAmB,eAAe;AAEnD,MAAI,SAAS,YAAY;AACrB,WAAO;AAAA,EACX;AAEA,MAAI,OAAO,SAAS,UAAU;AAC1B,WAAO,WAAW,UAAU,IAAI;AAAA,EACpC;AAEA,SAAO,cAAc;AAAA,IACjB,kBAAkB,CAAC,UAAkB;AACjC,YAAM,cAAc,eAAe,OAAO,QAAQ;AAClD,aAAO,eAAe,aAAa,IAAI,IAAI;AAAA,IAC/C;AAAA,IACA,OAAO,CAAC,OAAe,OAAO,WAAW;AACrC,YAAM,cAAc,eAAe,OAAO,QAAQ;AAClD,eAAS,KAAK,MAAM,aAAa,OAAO,MAAM;AAC9C,aAAO,SAAS,MAAM,OAAO,OAAO,MAAM;AAAA,IAC9C;AAAA,EACJ,CAAC;AACL;AAeO,SAAS,iBAAiB,SAA4D,CAAC,GAAoB;AA/FlH;AAgGI,QAAM,QAAO,YAAO,SAAP,YAAe,cAAc;AAC1C,QAAM,YAAW,YAAO,aAAP,YAAmB,eAAe;AAEnD,MAAI,SAAS,YAAY;AACrB,WAAO;AAAA,EACX;AAEA,MAAI,OAAO,SAAS,UAAU;AAC1B,WAAO,WAAW,UAAU,IAAI;AAAA,EACpC;AAEA,SAAO,cAAc;AAAA,IACjB,MAAM,CAAC,OAAmB,SAAS,MAAM;AACrC,wCAAkC,UAAU,OAAO,MAAM;AACzD,YAAM,CAAC,cAAc,YAAY,IAAI,KAAK,KAAK,OAAO,MAAM;AAC5D,YAAM,SAAS,OAAO,YAAY;AAClC,eAAS;AACT,YAAM,eAAe,MAAM,MAAM,QAAQ,SAAS,MAAM;AACxD,4CAAsC,UAAU,QAAQ,YAAY;AACpE,YAAM,CAAC,OAAO,aAAa,IAAI,SAAS,KAAK,cAAc,CAAC;AAC5D,gBAAU;AACV,aAAO,CAAC,OAAO,MAAM;AAAA,IACzB;AAAA,EACJ,CAAC;AACL;AAaO,SAAS,eAAe,SAAwD,CAAC,GAAkB;AACtG,SAAO,aAAa,iBAAiB,MAAM,GAAG,iBAAiB,MAAM,CAAC;AAC1E", "sourcesContent": ["/**\n * Asserts that a given string matches a given alphabet.\n */\nexport function assertValidBaseString(alphabet: string, testValue: string, givenValue = testValue) {\n    if (!testValue.match(new RegExp(`^[${alphabet}]*$`))) {\n        // TODO: Coded error.\n        throw new Error(`Expected a string of base ${alphabet.length}, got [${givenValue}].`);\n    }\n}\n", "/**\n * Asserts that a given byte array is not empty.\n */\nexport function assertByteArrayIsNotEmptyForCodec(codecDescription: string, bytes: Uint8Array, offset = 0) {\n    if (bytes.length - offset <= 0) {\n        // TODO: Coded error.\n        throw new Error(`Codec [${codecDescription}] cannot decode empty byte arrays.`);\n    }\n}\n\n/**\n * Asserts that a given byte array has enough bytes to decode.\n */\nexport function assertByteArrayHasEnoughBytesForCodec(\n    codecDescription: string,\n    expected: number,\n    bytes: Uint8Array,\n    offset = 0,\n) {\n    const bytesLength = bytes.length - offset;\n    if (bytesLength < expected) {\n        // TODO: Coded error.\n        throw new Error(`Codec [${codecDescription}] expected ${expected} bytes, got ${bytesLength}.`);\n    }\n}\n", "/**\n * Concatenates an array of `Uint8Array`s into a single `Uint8Array`.\n * Reuses the original byte array when applicable.\n */\nexport const mergeBytes = (byteArrays: Uint8Array[]): Uint8Array => {\n    const nonEmptyByteArrays = byteArrays.filter(arr => arr.length);\n    if (nonEmptyByteArrays.length === 0) {\n        return byteArrays.length ? byteArrays[0] : new Uint8Array();\n    }\n\n    if (nonEmptyByteArrays.length === 1) {\n        return nonEmptyByteArrays[0];\n    }\n\n    const totalLength = nonEmptyByteArrays.reduce((total, arr) => total + arr.length, 0);\n    const result = new Uint8Array(totalLength);\n    let offset = 0;\n    nonEmptyByteArrays.forEach(arr => {\n        result.set(arr, offset);\n        offset += arr.length;\n    });\n    return result;\n};\n\n/**\n * Pads a `Uint8Array` with zeroes to the specified length.\n * If the array is longer than the specified length, it is returned as-is.\n */\nexport const padBytes = (bytes: Uint8Array, length: number): Uint8Array => {\n    if (bytes.length >= length) return bytes;\n    const paddedBytes = new Uint8Array(length).fill(0);\n    paddedBytes.set(bytes);\n    return paddedBytes;\n};\n\n/**\n * Fixes a `Uint8Array` to the specified length.\n * If the array is longer than the specified length, it is truncated.\n * If the array is shorter than the specified length, it is padded with zeroes.\n */\nexport const fixBytes = (bytes: Uint8Array, length: number): Uint8Array =>\n    padBytes(bytes.length <= length ? bytes : bytes.slice(0, length), length);\n", "/**\n * Defines an offset in bytes.\n */\nexport type Offset = number;\n\ntype BaseEncoder<TFrom> = {\n    /** Encode the provided value and return the encoded bytes directly. */\n    readonly encode: (value: TFrom) => Uint8Array;\n    /**\n     * Writes the encoded value into the provided byte array at the given offset.\n     * Returns the offset of the next byte after the encoded value.\n     */\n    readonly write: (value: TFrom, bytes: Uint8Array, offset: Offset) => Offset;\n};\n\nexport type FixedSizeEncoder<TFrom, TSize extends number = number> = BaseEncoder<TFrom> & {\n    /** The fixed size of the encoded value in bytes. */\n    readonly fixedSize: TSize;\n};\n\nexport type VariableSizeEncoder<TFrom> = BaseEncoder<TFrom> & {\n    /** The total size of the encoded value in bytes. */\n    readonly getSizeFromValue: (value: TFrom) => number;\n    /** The maximum size an encoded value can be in bytes, if applicable. */\n    readonly maxSize?: number;\n};\n\n/**\n * An object that can encode a value to a `Uint8Array`.\n */\nexport type Encoder<TFrom> = FixedSizeEncoder<TFrom> | VariableSizeEncoder<TFrom>;\n\ntype BaseDecoder<TTo> = {\n    /** Decodes the provided byte array at the given offset (or zero) and returns the value directly. */\n    readonly decode: (bytes: Uint8Array, offset?: Offset) => TTo;\n    /**\n     * Reads the encoded value from the provided byte array at the given offset.\n     * Returns the decoded value and the offset of the next byte after the encoded value.\n     */\n    readonly read: (bytes: Uint8Array, offset: Offset) => [TTo, Offset];\n};\n\nexport type FixedSizeDecoder<TTo, TSize extends number = number> = BaseDecoder<TTo> & {\n    /** The fixed size of the encoded value in bytes. */\n    readonly fixedSize: TSize;\n};\n\nexport type VariableSizeDecoder<TTo> = BaseDecoder<TTo> & {\n    /** The maximum size an encoded value can be in bytes, if applicable. */\n    readonly maxSize?: number;\n};\n\n/**\n * An object that can decode a value from a `Uint8Array`.\n */\nexport type Decoder<TTo> = FixedSizeDecoder<TTo> | VariableSizeDecoder<TTo>;\n\nexport type FixedSizeCodec<TFrom, TTo extends TFrom = TFrom, TSize extends number = number> = FixedSizeEncoder<\n    TFrom,\n    TSize\n> &\n    FixedSizeDecoder<TTo, TSize>;\n\nexport type VariableSizeCodec<TFrom, TTo extends TFrom = TFrom> = VariableSizeEncoder<TFrom> & VariableSizeDecoder<TTo>;\n\n/**\n * An object that can encode and decode a value to and from a `Uint8Array`.\n * It supports encoding looser types than it decodes for convenience.\n * For example, a `bigint` encoder will always decode to a `bigint`\n * but can be used to encode a `number`.\n *\n * @typeParam TFrom - The type of the value to encode.\n * @typeParam TTo - The type of the decoded value. Defaults to `TFrom`.\n */\nexport type Codec<TFrom, TTo extends TFrom = TFrom> = FixedSizeCodec<TFrom, TTo> | VariableSizeCodec<TFrom, TTo>;\n\n/**\n * Get the encoded size of a given value in bytes.\n */\nexport function getEncodedSize<TFrom>(\n    value: TFrom,\n    encoder: { fixedSize: number } | { getSizeFromValue: (value: TFrom) => number },\n): number {\n    return 'fixedSize' in encoder ? encoder.fixedSize : encoder.getSizeFromValue(value);\n}\n\n/** Fills the missing `encode` function using the existing `write` function. */\nexport function createEncoder<TFrom, TSize extends number>(\n    encoder: Omit<FixedSizeEncoder<TFrom, TSize>, 'encode'>,\n): FixedSizeEncoder<TFrom, TSize>;\nexport function createEncoder<TFrom>(encoder: Omit<VariableSizeEncoder<TFrom>, 'encode'>): VariableSizeEncoder<TFrom>;\nexport function createEncoder<TFrom>(\n    encoder: Omit<FixedSizeEncoder<TFrom>, 'encode'> | Omit<VariableSizeEncoder<TFrom>, 'encode'>,\n): Encoder<TFrom>;\nexport function createEncoder<TFrom>(\n    encoder: Omit<FixedSizeEncoder<TFrom>, 'encode'> | Omit<VariableSizeEncoder<TFrom>, 'encode'>,\n): Encoder<TFrom> {\n    return Object.freeze({\n        ...encoder,\n        encode: value => {\n            const bytes = new Uint8Array(getEncodedSize(value, encoder));\n            encoder.write(value, bytes, 0);\n            return bytes;\n        },\n    });\n}\n\n/** Fills the missing `decode` function using the existing `read` function. */\nexport function createDecoder<TTo, TSize extends number>(\n    decoder: Omit<FixedSizeDecoder<TTo, TSize>, 'decode'>,\n): FixedSizeDecoder<TTo, TSize>;\nexport function createDecoder<TTo>(decoder: Omit<VariableSizeDecoder<TTo>, 'decode'>): VariableSizeDecoder<TTo>;\nexport function createDecoder<TTo>(\n    decoder: Omit<FixedSizeDecoder<TTo>, 'decode'> | Omit<VariableSizeDecoder<TTo>, 'decode'>,\n): Decoder<TTo>;\nexport function createDecoder<TTo>(\n    decoder: Omit<FixedSizeDecoder<TTo>, 'decode'> | Omit<VariableSizeDecoder<TTo>, 'decode'>,\n): Decoder<TTo> {\n    return Object.freeze({\n        ...decoder,\n        decode: (bytes, offset = 0) => decoder.read(bytes, offset)[0],\n    });\n}\n\n/** Fills the missing `encode` and `decode` function using the existing `write` and `read` functions. */\nexport function createCodec<TFrom, TTo extends TFrom = TFrom, TSize extends number = number>(\n    codec: Omit<FixedSizeCodec<TFrom, TTo, TSize>, 'encode' | 'decode'>,\n): FixedSizeCodec<TFrom, TTo, TSize>;\nexport function createCodec<TFrom, TTo extends TFrom = TFrom>(\n    codec: Omit<VariableSizeCodec<TFrom, TTo>, 'encode' | 'decode'>,\n): VariableSizeCodec<TFrom, TTo>;\nexport function createCodec<TFrom, TTo extends TFrom = TFrom>(\n    codec:\n        | Omit<FixedSizeCodec<TFrom, TTo>, 'encode' | 'decode'>\n        | Omit<VariableSizeCodec<TFrom, TTo>, 'encode' | 'decode'>,\n): Codec<TFrom, TTo>;\nexport function createCodec<TFrom, TTo extends TFrom = TFrom>(\n    codec:\n        | Omit<FixedSizeCodec<TFrom, TTo>, 'encode' | 'decode'>\n        | Omit<VariableSizeCodec<TFrom, TTo>, 'encode' | 'decode'>,\n): Codec<TFrom, TTo> {\n    return Object.freeze({\n        ...codec,\n        decode: (bytes, offset = 0) => codec.read(bytes, offset)[0],\n        encode: value => {\n            const bytes = new Uint8Array(getEncodedSize(value, codec));\n            codec.write(value, bytes, 0);\n            return bytes;\n        },\n    });\n}\n\nexport function isFixedSize<TFrom, TSize extends number>(\n    encoder: FixedSizeEncoder<TFrom, TSize> | VariableSizeEncoder<TFrom>,\n): encoder is FixedSizeEncoder<TFrom, TSize>;\nexport function isFixedSize<TTo, TSize extends number>(\n    decoder: FixedSizeDecoder<TTo, TSize> | VariableSizeDecoder<TTo>,\n): decoder is FixedSizeDecoder<TTo, TSize>;\nexport function isFixedSize<TFrom, TTo extends TFrom, TSize extends number>(\n    codec: FixedSizeCodec<TFrom, TTo, TSize> | VariableSizeCodec<TFrom, TTo>,\n): codec is FixedSizeCodec<TFrom, TTo, TSize>;\nexport function isFixedSize<TSize extends number>(\n    codec: { fixedSize: TSize } | { maxSize?: number },\n): codec is { fixedSize: TSize };\nexport function isFixedSize(codec: { fixedSize: number } | { maxSize?: number }): codec is { fixedSize: number } {\n    return 'fixedSize' in codec && typeof codec.fixedSize === 'number';\n}\n\nexport function assertIsFixedSize<TFrom, TSize extends number>(\n    encoder: FixedSizeEncoder<TFrom, TSize> | VariableSizeEncoder<TFrom>,\n    message?: string,\n): asserts encoder is FixedSizeEncoder<TFrom, TSize>;\nexport function assertIsFixedSize<TTo, TSize extends number>(\n    decoder: FixedSizeDecoder<TTo, TSize> | VariableSizeDecoder<TTo>,\n    message?: string,\n): asserts decoder is FixedSizeDecoder<TTo, TSize>;\nexport function assertIsFixedSize<TFrom, TTo extends TFrom, TSize extends number>(\n    codec: FixedSizeCodec<TFrom, TTo, TSize> | VariableSizeCodec<TFrom, TTo>,\n    message?: string,\n): asserts codec is FixedSizeCodec<TFrom, TTo, TSize>;\nexport function assertIsFixedSize<TSize extends number>(\n    codec: { fixedSize: TSize } | { maxSize?: number },\n    message?: string,\n): asserts codec is { fixedSize: TSize };\nexport function assertIsFixedSize(\n    codec: { fixedSize: number } | { maxSize?: number },\n    message?: string,\n): asserts codec is { fixedSize: number } {\n    if (!isFixedSize(codec)) {\n        // TODO: Coded error.\n        throw new Error(message ?? 'Expected a fixed-size codec, got a variable-size one.');\n    }\n}\n\nexport function isVariableSize<TFrom>(encoder: Encoder<TFrom>): encoder is VariableSizeEncoder<TFrom>;\nexport function isVariableSize<TTo>(decoder: Decoder<TTo>): decoder is VariableSizeDecoder<TTo>;\nexport function isVariableSize<TFrom, TTo extends TFrom>(\n    codec: Codec<TFrom, TTo>,\n): codec is VariableSizeCodec<TFrom, TTo>;\nexport function isVariableSize(codec: { fixedSize: number } | { maxSize?: number }): codec is { maxSize?: number };\nexport function isVariableSize(codec: { fixedSize: number } | { maxSize?: number }): codec is { maxSize?: number } {\n    return !isFixedSize(codec);\n}\n\nexport function assertIsVariableSize<T>(\n    encoder: Encoder<T>,\n    message?: string,\n): asserts encoder is VariableSizeEncoder<T>;\nexport function assertIsVariableSize<T>(\n    decoder: Decoder<T>,\n    message?: string,\n): asserts decoder is VariableSizeDecoder<T>;\nexport function assertIsVariableSize<TFrom, TTo extends TFrom>(\n    codec: Codec<TFrom, TTo>,\n    message?: string,\n): asserts codec is VariableSizeCodec<TFrom, TTo>;\nexport function assertIsVariableSize(\n    codec: { fixedSize: number } | { maxSize?: number },\n    message?: string,\n): asserts codec is { maxSize?: number };\nexport function assertIsVariableSize(\n    codec: { fixedSize: number } | { maxSize?: number },\n    message?: string,\n): asserts codec is { maxSize?: number } {\n    if (!isVariableSize(codec)) {\n        // TODO: Coded error.\n        throw new Error(message ?? 'Expected a variable-size codec, got a fixed-size one.');\n    }\n}\n", "import {\n    Codec,\n    Decoder,\n    Encoder,\n    FixedSizeCodec,\n    FixedSizeDecoder,\n    FixedSizeEncoder,\n    isFixedSize,\n    VariableSizeCodec,\n    VariableSizeDecoder,\n    VariableSizeEncoder,\n} from './codec';\n\n/**\n * Combines an encoder and a decoder into a codec.\n * The encoder and decoder must have the same fixed size, max size and description.\n * If a description is provided, it will override the encoder and decoder descriptions.\n */\nexport function combineCodec<TFrom, TTo extends TFrom, TSize extends number>(\n    encoder: FixedSizeEncoder<TFrom, TSize>,\n    decoder: FixedSizeDecoder<TTo, TSize>,\n): FixedSizeCodec<TFrom, TTo, TSize>;\nexport function combineCodec<TFrom, TTo extends TFrom>(\n    encoder: VariableSizeEncoder<TFrom>,\n    decoder: VariableSizeDecoder<TTo>,\n): VariableSizeCodec<TFrom, TTo>;\nexport function combineCodec<TFrom, TTo extends TFrom>(\n    encoder: Encoder<TFrom>,\n    decoder: Decoder<TTo>,\n): Codec<TFrom, TTo>;\nexport function combineCodec<TFrom, TTo extends TFrom>(\n    encoder: Encoder<TFrom>,\n    decoder: Decoder<TTo>,\n): Codec<TFrom, TTo> {\n    if (isFixedSize(encoder) !== isFixedSize(decoder)) {\n        // TODO: Coded error.\n        throw new Error(`Encoder and decoder must either both be fixed-size or variable-size.`);\n    }\n\n    if (isFixedSize(encoder) && isFixedSize(decoder) && encoder.fixedSize !== decoder.fixedSize) {\n        // TODO: Coded error.\n        throw new Error(\n            `Encoder and decoder must have the same fixed size, got [${encoder.fixedSize}] and [${decoder.fixedSize}].`,\n        );\n    }\n\n    if (!isFixedSize(encoder) && !isFixedSize(decoder) && encoder.maxSize !== decoder.maxSize) {\n        // TODO: Coded error.\n        throw new Error(\n            `Encoder and decoder must have the same max size, got [${encoder.maxSize}] and [${decoder.maxSize}].`,\n        );\n    }\n\n    return {\n        ...decoder,\n        ...encoder,\n        decode: decoder.decode,\n        encode: encoder.encode,\n        read: decoder.read,\n        write: encoder.write,\n    };\n}\n", "import { assertByteArrayHasEnoughBytesForCodec } from './assertions';\nimport { fixBytes } from './bytes';\nimport {\n    Codec,\n    createDecoder,\n    createEncoder,\n    Decoder,\n    Encoder,\n    FixedSizeCodec,\n    FixedSizeDecoder,\n    FixedSizeEncoder,\n    isFixedSize,\n    Offset,\n} from './codec';\nimport { combineCodec } from './combine-codec';\n\n/**\n * Creates a fixed-size encoder from a given encoder.\n *\n * @param encoder - The encoder to wrap into a fixed-size encoder.\n * @param fixedBytes - The fixed number of bytes to write.\n */\nexport function fixEncoder<TFrom, TSize extends number>(\n    encoder: Encoder<TFrom>,\n    fixedBytes: TSize,\n): FixedSizeEncoder<TFrom, TSize> {\n    return createEncoder({\n        fixedSize: fixedBytes,\n        write: (value: TFrom, bytes: Uint8Array, offset: Offset) => {\n            // Here we exceptionally use the `encode` function instead of the `write`\n            // function as using the nested `write` function on a fixed-sized byte\n            // array may result in a out-of-bounds error on the nested encoder.\n            const variableByteArray = encoder.encode(value);\n            const fixedByteArray =\n                variableByteArray.length > fixedBytes ? variableByteArray.slice(0, fixedBytes) : variableByteArray;\n            bytes.set(fixedByteArray, offset);\n            return offset + fixedBytes;\n        },\n    });\n}\n\n/**\n * Creates a fixed-size decoder from a given decoder.\n *\n * @param decoder - The decoder to wrap into a fixed-size decoder.\n * @param fixedBytes - The fixed number of bytes to read.\n */\nexport function fixDecoder<TTo, TSize extends number>(\n    decoder: Decoder<TTo>,\n    fixedBytes: TSize,\n): FixedSizeDecoder<TTo, TSize> {\n    return createDecoder({\n        fixedSize: fixedBytes,\n        read: (bytes: Uint8Array, offset: Offset) => {\n            assertByteArrayHasEnoughBytesForCodec('fixCodec', fixedBytes, bytes, offset);\n            // Slice the byte array to the fixed size if necessary.\n            if (offset > 0 || bytes.length > fixedBytes) {\n                bytes = bytes.slice(offset, offset + fixedBytes);\n            }\n            // If the nested decoder is fixed-size, pad and truncate the byte array accordingly.\n            if (isFixedSize(decoder)) {\n                bytes = fixBytes(bytes, decoder.fixedSize);\n            }\n            // Decode the value using the nested decoder.\n            const [value] = decoder.read(bytes, 0);\n            return [value, offset + fixedBytes];\n        },\n    });\n}\n\n/**\n * Creates a fixed-size codec from a given codec.\n *\n * @param codec - The codec to wrap into a fixed-size codec.\n * @param fixedBytes - The fixed number of bytes to read/write.\n */\nexport function fixCodec<TFrom, TTo extends TFrom, TSize extends number>(\n    codec: Codec<TFrom, TTo>,\n    fixedBytes: TSize,\n): FixedSizeCodec<TFrom, TTo, TSize> {\n    return combineCodec(fixEncoder(codec, fixedBytes), fixDecoder(codec, fixedBytes));\n}\n", "import {\n    Codec,\n    createCodec,\n    createDecoder,\n    createEncoder,\n    Decoder,\n    Encoder,\n    FixedSizeCodec,\n    FixedSizeDecoder,\n    FixedSizeEncoder,\n    isVariableSize,\n    VariableSizeCodec,\n    VariableSizeDecoder,\n    VariableSizeEncoder,\n} from './codec';\n\n/**\n * Converts an encoder A to a encoder B by mapping their values.\n */\nexport function mapEncoder<TOldFrom, TNewFrom, TSize extends number>(\n    encoder: FixedSizeEncoder<TOldFrom, TSize>,\n    unmap: (value: TNewFrom) => TOldFrom,\n): FixedSizeEncoder<TNewFrom, TSize>;\nexport function mapEncoder<TOldFrom, TNewFrom>(\n    encoder: VariableSizeEncoder<TOldFrom>,\n    unmap: (value: TNewFrom) => TOldFrom,\n): VariableSizeEncoder<TNewFrom>;\nexport function mapEncoder<TOldFrom, TNewFrom>(\n    encoder: Encoder<TOldFrom>,\n    unmap: (value: TNewFrom) => TOldFrom,\n): Encoder<TNewFrom>;\nexport function mapEncoder<TOldFrom, TNewFrom>(\n    encoder: Encoder<TOldFrom>,\n    unmap: (value: TNewFrom) => TOldFrom,\n): Encoder<TNewFrom> {\n    return createEncoder({\n        ...(isVariableSize(encoder)\n            ? { ...encoder, getSizeFromValue: (value: TNewFrom) => encoder.getSizeFromValue(unmap(value)) }\n            : encoder),\n        write: (value: TNewFrom, bytes, offset) => encoder.write(unmap(value), bytes, offset),\n    });\n}\n\n/**\n * Converts an decoder A to a decoder B by mapping their values.\n */\nexport function mapDecoder<TOldTo, TNewTo, TSize extends number>(\n    decoder: FixedSizeDecoder<TOldTo, TSize>,\n    map: (value: TOldTo, bytes: Uint8Array, offset: number) => TNewTo,\n): FixedSizeDecoder<TNewTo, TSize>;\nexport function mapDecoder<TOldTo, TNewTo>(\n    decoder: VariableSizeDecoder<TOldTo>,\n    map: (value: TOldTo, bytes: Uint8Array, offset: number) => TNewTo,\n): VariableSizeDecoder<TNewTo>;\nexport function mapDecoder<TOldTo, TNewTo>(\n    decoder: Decoder<TOldTo>,\n    map: (value: TOldTo, bytes: Uint8Array, offset: number) => TNewTo,\n): Decoder<TNewTo>;\nexport function mapDecoder<TOldTo, TNewTo>(\n    decoder: Decoder<TOldTo>,\n    map: (value: TOldTo, bytes: Uint8Array, offset: number) => TNewTo,\n): Decoder<TNewTo> {\n    return createDecoder({\n        ...decoder,\n        read: (bytes: Uint8Array, offset) => {\n            const [value, newOffset] = decoder.read(bytes, offset);\n            return [map(value, bytes, offset), newOffset];\n        },\n    });\n}\n\n/**\n * Converts a codec A to a codec B by mapping their values.\n */\nexport function mapCodec<TOldFrom, TNewFrom, TTo extends TNewFrom & TOldFrom, TSize extends number>(\n    codec: FixedSizeCodec<TOldFrom, TTo, TSize>,\n    unmap: (value: TNewFrom) => TOldFrom,\n): FixedSizeCodec<TNewFrom, TTo, TSize>;\nexport function mapCodec<TOldFrom, TNewFrom, TTo extends TNewFrom & TOldFrom>(\n    codec: VariableSizeCodec<TOldFrom, TTo>,\n    unmap: (value: TNewFrom) => TOldFrom,\n): VariableSizeCodec<TNewFrom, TTo>;\nexport function mapCodec<TOldFrom, TNewFrom, TTo extends TNewFrom & TOldFrom>(\n    codec: Codec<TOldFrom, TTo>,\n    unmap: (value: TNewFrom) => TOldFrom,\n): Codec<TNewFrom, TTo>;\nexport function mapCodec<TOldFrom, TNewFrom, TOldTo extends TOldFrom, TNewTo extends TNewFrom, TSize extends number>(\n    codec: FixedSizeCodec<TOldFrom, TOldTo, TSize>,\n    unmap: (value: TNewFrom) => TOldFrom,\n    map: (value: TOldTo, bytes: Uint8Array, offset: number) => TNewTo,\n): FixedSizeCodec<TNewFrom, TNewTo, TSize>;\nexport function mapCodec<TOldFrom, TNewFrom, TOldTo extends TOldFrom, TNewTo extends TNewFrom>(\n    codec: VariableSizeCodec<TOldFrom, TOldTo>,\n    unmap: (value: TNewFrom) => TOldFrom,\n    map: (value: TOldTo, bytes: Uint8Array, offset: number) => TNewTo,\n): VariableSizeCodec<TNewFrom, TNewTo>;\nexport function mapCodec<TOldFrom, TNewFrom, TOldTo extends TOldFrom, TNewTo extends TNewFrom>(\n    codec: Codec<TOldFrom, TOldTo>,\n    unmap: (value: TNewFrom) => TOldFrom,\n    map: (value: TOldTo, bytes: Uint8Array, offset: number) => TNewTo,\n): Codec<TNewFrom, TNewTo>;\nexport function mapCodec<TOldFrom, TNewFrom, TOldTo extends TOldFrom, TNewTo extends TNewFrom>(\n    codec: Codec<TOldFrom, TOldTo>,\n    unmap: (value: TNewFrom) => TOldFrom,\n    map?: (value: TOldTo, bytes: Uint8Array, offset: number) => TNewTo,\n): Codec<TNewFrom, TNewTo> {\n    return createCodec({\n        ...mapEncoder(codec, unmap),\n        read: map ? mapDecoder(codec, map).read : (codec.read as unknown as Decoder<TNewTo>['read']),\n    });\n}\n", "import {\n    combineCodec,\n    createDecoder,\n    createEncoder,\n    VariableSizeCodec,\n    VariableSizeDecoder,\n    VariableSizeEncoder,\n} from '@solana/codecs-core';\n\nimport { assertValidBaseString } from './assertions';\n\n/**\n * Encodes a string using a custom alphabet by dividing\n * by the base and handling leading zeroes.\n * @see {@link getBaseXCodec} for a more detailed description.\n */\nexport const getBaseXEncoder = (alphabet: string): VariableSizeEncoder<string> => {\n    return createEncoder({\n        getSizeFromValue: (value: string): number => {\n            const [leadingZeroes, tailChars] = partitionLeadingZeroes(value, alphabet[0]);\n            if (tailChars === '') return value.length;\n\n            const base10Number = getBigIntFromBaseX(tailChars, alphabet);\n            return leadingZeroes.length + Math.ceil(base10Number.toString(16).length / 2);\n        },\n        write(value: string, bytes, offset) {\n            // Check if the value is valid.\n            assertValidBaseString(alphabet, value);\n            if (value === '') return offset;\n\n            // Handle leading zeroes.\n            const [leadingZeroes, tailChars] = partitionLeadingZeroes(value, alphabet[0]);\n            if (tailChars === '') {\n                bytes.set(new Uint8Array(leadingZeroes.length).fill(0), offset);\n                return offset + leadingZeroes.length;\n            }\n\n            // From baseX to base10.\n            let base10Number = getBigIntFromBaseX(tailChars, alphabet);\n\n            // From base10 to bytes.\n            const tailBytes: number[] = [];\n            while (base10Number > 0n) {\n                tailBytes.unshift(Number(base10Number % 256n));\n                base10Number /= 256n;\n            }\n\n            const bytesToAdd = [...Array(leadingZeroes.length).fill(0), ...tailBytes];\n            bytes.set(bytesToAdd, offset);\n            return offset + bytesToAdd.length;\n        },\n    });\n};\n\n/**\n * Decodes a string using a custom alphabet by dividing\n * by the base and handling leading zeroes.\n * @see {@link getBaseXCodec} for a more detailed description.\n */\nexport const getBaseXDecoder = (alphabet: string): VariableSizeDecoder<string> => {\n    return createDecoder({\n        read(rawBytes, offset): [string, number] {\n            const bytes = offset === 0 ? rawBytes : rawBytes.slice(offset);\n            if (bytes.length === 0) return ['', 0];\n\n            // Handle leading zeroes.\n            let trailIndex = bytes.findIndex(n => n !== 0);\n            trailIndex = trailIndex === -1 ? bytes.length : trailIndex;\n            const leadingZeroes = alphabet[0].repeat(trailIndex);\n            if (trailIndex === bytes.length) return [leadingZeroes, rawBytes.length];\n\n            // From bytes to base10.\n            const base10Number = bytes.slice(trailIndex).reduce((sum, byte) => sum * 256n + BigInt(byte), 0n);\n\n            // From base10 to baseX.\n            const tailChars = getBaseXFromBigInt(base10Number, alphabet);\n\n            return [leadingZeroes + tailChars, rawBytes.length];\n        },\n    });\n};\n\n/**\n * A string codec that requires a custom alphabet and uses\n * the length of that alphabet as the base. It then divides\n * the input by the base as many times as necessary to get\n * the output. It also supports leading zeroes by using the\n * first character of the alphabet as the zero character.\n *\n * This can be used to create codecs such as base10 or base58.\n */\nexport const getBaseXCodec = (alphabet: string): VariableSizeCodec<string> =>\n    combineCodec(getBaseXEncoder(alphabet), getBaseXDecoder(alphabet));\n\nfunction partitionLeadingZeroes(value: string, zeroCharacter: string): [string, string] {\n    const leadingZeroIndex = [...value].findIndex(c => c !== zeroCharacter);\n    return leadingZeroIndex === -1 ? [value, ''] : [value.slice(0, leadingZeroIndex), value.slice(leadingZeroIndex)];\n}\n\nfunction getBigIntFromBaseX(value: string, alphabet: string): bigint {\n    const base = BigInt(alphabet.length);\n    return [...value].reduce((sum, char) => sum * base + BigInt(alphabet.indexOf(char)), 0n);\n}\n\nfunction getBaseXFromBigInt(value: bigint, alphabet: string): string {\n    const base = BigInt(alphabet.length);\n    const tailChars = [];\n    while (value > 0n) {\n        tailChars.unshift(alphabet[Number(value % base)]);\n        value /= base;\n    }\n    return tailChars.join('');\n}\n", "import { getBaseXCodec, getBaseXDecoder, getBaseXEncoder } from './baseX';\n\nconst alphabet = '0123456789';\n\n/** Encodes strings in base10. */\nexport const getBase10Encoder = () => getBaseXEncoder(alphabet);\n\n/** Decodes strings in base10. */\nexport const getBase10Decoder = () => getBaseXDecoder(alphabet);\n\n/** Encodes and decodes strings in base10. */\nexport const getBase10Codec = () => getBaseXCodec(alphabet);\n", "import {\n    combineCodec,\n    createDecoder,\n    createEncoder,\n    VariableSizeCodec,\n    VariableSizeDecoder,\n    VariableSizeEncoder,\n} from '@solana/codecs-core';\n\nimport { assertValidBaseString } from './assertions';\n\n/** Encodes strings in base16. */\nexport const getBase16Encoder = (): VariableSizeEncoder<string> =>\n    createEncoder({\n        getSizeFromValue: (value: string) => Math.ceil(value.length / 2),\n        write(value: string, bytes, offset) {\n            const lowercaseValue = value.toLowerCase();\n            assertValidBaseString('0123456789abcdef', lowercaseValue, value);\n            const matches = lowercaseValue.match(/.{1,2}/g);\n            const hexBytes = matches ? matches.map((byte: string) => parseInt(byte, 16)) : [];\n            bytes.set(hexBytes, offset);\n            return hexBytes.length + offset;\n        },\n    });\n\n/** Decodes strings in base16. */\nexport const getBase16Decoder = (): VariableSizeDecoder<string> =>\n    createDecoder({\n        read(bytes, offset) {\n            const value = bytes.slice(offset).reduce((str, byte) => str + byte.toString(16).padStart(2, '0'), '');\n            return [value, bytes.length];\n        },\n    });\n\n/** Encodes and decodes strings in base16. */\nexport const getBase16Codec = (): VariableSizeCodec<string> => combineCodec(getBase16Encoder(), getBase16Decoder());\n", "import { getBaseXCodec, getBaseXDecoder, getBaseXEncoder } from './baseX';\n\nconst alphabet = '123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz';\n\n/** Encodes strings in base58. */\nexport const getBase58Encoder = () => getBaseXEncoder(alphabet);\n\n/** Decodes strings in base58. */\nexport const getBase58Decoder = () => getBaseXDecoder(alphabet);\n\n/** Encodes and decodes strings in base58. */\nexport const getBase58Codec = () => getBaseXCodec(alphabet);\n", "import {\n    combineCodec,\n    createDecode<PERSON>,\n    createEncoder,\n    VariableSizeCodec,\n    VariableSizeDecoder,\n    VariableSizeEncoder,\n} from '@solana/codecs-core';\n\nimport { assertValidBaseString } from './assertions';\n\n/**\n * Encodes a string using a custom alphabet by reslicing the bits of the byte array.\n * @see {@link getBaseXResliceCodec} for a more detailed description.\n */\nexport const getBaseXResliceEncoder = (alphabet: string, bits: number): VariableSizeEncoder<string> =>\n    createEncoder({\n        getSizeFromValue: (value: string) => Math.floor((value.length * bits) / 8),\n        write(value: string, bytes, offset) {\n            assertValidBaseString(alphabet, value);\n            if (value === '') return offset;\n            const charIndices = [...value].map(c => alphabet.indexOf(c));\n            const reslicedBytes = reslice(charIndices, bits, 8, false);\n            bytes.set(reslicedBytes, offset);\n            return reslicedBytes.length + offset;\n        },\n    });\n\n/**\n * Decodes a string using a custom alphabet by reslicing the bits of the byte array.\n * @see {@link getBaseXResliceCodec} for a more detailed description.\n */\nexport const getBaseXResliceDecoder = (alphabet: string, bits: number): VariableSizeDecoder<string> =>\n    createDecoder({\n        read(rawBytes, offset = 0): [string, number] {\n            const bytes = offset === 0 ? rawBytes : rawBytes.slice(offset);\n            if (bytes.length === 0) return ['', rawBytes.length];\n            const charIndices = reslice([...bytes], 8, bits, true);\n            return [charIndices.map(i => alphabet[i]).join(''), rawBytes.length];\n        },\n    });\n\n/**\n * A string serializer that reslices bytes into custom chunks\n * of bits that are then mapped to a custom alphabet.\n *\n * This can be used to create serializers whose alphabet\n * is a power of 2 such as base16 or base64.\n */\nexport const getBaseXResliceCodec = (alphabet: string, bits: number): VariableSizeCodec<string> =>\n    combineCodec(getBaseXResliceEncoder(alphabet, bits), getBaseXResliceDecoder(alphabet, bits));\n\n/** Helper function to reslice the bits inside bytes. */\nfunction reslice(input: number[], inputBits: number, outputBits: number, useRemainder: boolean): number[] {\n    const output = [];\n    let accumulator = 0;\n    let bitsInAccumulator = 0;\n    const mask = (1 << outputBits) - 1;\n    for (const value of input) {\n        accumulator = (accumulator << inputBits) | value;\n        bitsInAccumulator += inputBits;\n        while (bitsInAccumulator >= outputBits) {\n            bitsInAccumulator -= outputBits;\n            output.push((accumulator >> bitsInAccumulator) & mask);\n        }\n    }\n    if (useRemainder && bitsInAccumulator > 0) {\n        output.push((accumulator << (outputBits - bitsInAccumulator)) & mask);\n    }\n    return output;\n}\n", "import {\n    combineCodec,\n    createDecoder,\n    createEncoder,\n    mapDecoder,\n    mapEncoder,\n    VariableSizeCodec,\n    VariableSizeDecoder,\n    VariableSizeEncoder,\n} from '@solana/codecs-core';\n\nimport { assertValidBaseString } from './assertions';\nimport { getBaseXResliceDecoder, getBaseXResliceEncoder } from './baseX-reslice';\n\nconst alphabet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';\n\n/** Encodes strings in base64. */\nexport const getBase64Encoder = (): VariableSizeEncoder<string> => {\n    if (__BROWSER__) {\n        return createEncoder({\n            getSizeFromValue: (value: string) => {\n                try {\n                    return (atob as Window['atob'])(value).length;\n                } catch (e) {\n                    // TODO: Coded error.\n                    throw new Error(`Expected a string of base 64, got [${value}].`);\n                }\n            },\n            write(value: string, bytes, offset) {\n                try {\n                    const bytesToAdd = (atob as Window['atob'])(value)\n                        .split('')\n                        .map(c => c.charCodeAt(0));\n                    bytes.set(bytesToAdd, offset);\n                    return bytesToAdd.length + offset;\n                } catch (e) {\n                    // TODO: Coded error.\n                    throw new Error(`Expected a string of base 64, got [${value}].`);\n                }\n            },\n        });\n    }\n\n    if (__NODEJS__) {\n        return createEncoder({\n            getSizeFromValue: (value: string) => Buffer.from(value, 'base64').length,\n            write(value: string, bytes, offset) {\n                assertValidBaseString(alphabet, value.replace(/=/g, ''));\n                const buffer = Buffer.from(value, 'base64');\n                bytes.set(buffer, offset);\n                return buffer.length + offset;\n            },\n        });\n    }\n\n    return mapEncoder(getBaseXResliceEncoder(alphabet, 6), (value: string): string => value.replace(/=/g, ''));\n};\n\n/** Decodes strings in base64. */\nexport const getBase64Decoder = (): VariableSizeDecoder<string> => {\n    if (__BROWSER__) {\n        return createDecoder({\n            read(bytes, offset = 0) {\n                const slice = bytes.slice(offset);\n                const value = (btoa as Window['btoa'])(String.fromCharCode(...slice));\n                return [value, bytes.length];\n            },\n        });\n    }\n\n    if (__NODEJS__) {\n        return createDecoder({\n            read: (bytes, offset = 0) => [Buffer.from(bytes, offset).toString('base64'), bytes.length],\n        });\n    }\n\n    return mapDecoder(getBaseXResliceDecoder(alphabet, 6), (value: string): string =>\n        value.padEnd(Math.ceil(value.length / 4) * 4, '='),\n    );\n};\n\n/** Encodes and decodes strings in base64. */\nexport const getBase64Codec = (): VariableSizeCodec<string> => combineCodec(getBase64Encoder(), getBase64Decoder());\n", "/**Removes null characters from a string. */\nexport const removeNullCharacters = (value: string) =>\n    // eslint-disable-next-line no-control-regex\n    value.replace(/\\u0000/g, '');\n\n/** Pads a string with null characters at the end. */\nexport const padNullCharacters = (value: string, chars: number) => value.padEnd(chars, '\\u0000');\n", "/**\n * Asserts that a given number is between a given range.\n */\nexport function assertNumberIsBetweenForCodec(\n    codecDescription: string,\n    min: number | bigint,\n    max: number | bigint,\n    value: number | bigint,\n) {\n    if (value < min || value > max) {\n        // TODO: Coded error.\n        throw new Error(\n            `Codec [${codecDescription}] expected number to be in the range [${min}, ${max}], got ${value}.`,\n        );\n    }\n}\n", "import { combineCodec, FixedSizeCodec, FixedSizeDecoder, FixedSizeEncoder } from '@solana/codecs-core';\n\nimport { NumberCodecConfig } from './common';\nimport { numberDecoderFactory, numberEncoderFactory } from './utils';\n\nexport const getF32Encoder = (config: NumberCodecConfig = {}): FixedSizeEncoder<number, 4> =>\n    numberEncoderFactory({\n        config,\n        name: 'f32',\n        set: (view, value, le) => view.setFloat32(0, value, le),\n        size: 4,\n    });\n\nexport const getF32Decoder = (config: NumberCodecConfig = {}): FixedSizeDecoder<number, 4> =>\n    numberDecoderFactory({\n        config,\n        get: (view, le) => view.getFloat32(0, le),\n        name: 'f32',\n        size: 4,\n    });\n\nexport const getF32Codec = (config: NumberCodecConfig = {}): FixedSizeCodec<number, number, 4> =>\n    combineCodec(getF32Encoder(config), getF32Decoder(config));\n", "import {\n    assertByteArrayHasEnoughBytesForCodec,\n    assertByteArrayIsNotEmptyForCodec,\n    createDecoder,\n    createEncoder,\n    FixedSizeDecoder,\n    FixedSizeEncoder,\n    Offset,\n} from '@solana/codecs-core';\n\nimport { assertNumberIsBetweenForCodec } from './assertions';\nimport { Endian, NumberCodecConfig } from './common';\n\ntype NumberFactorySharedInput<TSize extends number> = {\n    name: string;\n    size: TSize;\n    config?: NumberCodecConfig;\n};\n\ntype NumberFactoryEncoderInput<TFrom, TSize extends number> = NumberFactorySharedInput<TSize> & {\n    range?: [number | bigint, number | bigint];\n    set: (view: DataView, value: TFrom, littleEndian?: boolean) => void;\n};\n\ntype NumberFactoryDecoderInput<TTo, TSize extends number> = NumberFactorySharedInput<TSize> & {\n    get: (view: DataView, littleEndian?: boolean) => TTo;\n};\n\nfunction isLittleEndian(config?: NumberCodecConfig): boolean {\n    return config?.endian === Endian.BIG ? false : true;\n}\n\nexport function numberEncoderFactory<TFrom extends number | bigint, TSize extends number>(\n    input: NumberFactoryEncoderInput<TFrom, TSize>,\n): FixedSizeEncoder<TFrom, TSize> {\n    return createEncoder({\n        fixedSize: input.size,\n        write(value: TFrom, bytes: Uint8Array, offset: Offset): Offset {\n            if (input.range) {\n                assertNumberIsBetweenForCodec(input.name, input.range[0], input.range[1], value);\n            }\n            const arrayBuffer = new ArrayBuffer(input.size);\n            input.set(new DataView(arrayBuffer), value, isLittleEndian(input.config));\n            bytes.set(new Uint8Array(arrayBuffer), offset);\n            return offset + input.size;\n        },\n    });\n}\n\nexport function numberDecoderFactory<TTo extends number | bigint, TSize extends number>(\n    input: NumberFactoryDecoderInput<TTo, TSize>,\n): FixedSizeDecoder<TTo, TSize> {\n    return createDecoder({\n        fixedSize: input.size,\n        read(bytes, offset = 0): [TTo, number] {\n            assertByteArrayIsNotEmptyForCodec(input.name, bytes, offset);\n            assertByteArrayHasEnoughBytesForCodec(input.name, input.size, bytes, offset);\n            const view = new DataView(toArrayBuffer(bytes, offset, input.size));\n            return [input.get(view, isLittleEndian(input.config)), offset + input.size];\n        },\n    });\n}\n\n/**\n * Helper function to ensure that the ArrayBuffer is converted properly from a Uint8Array\n * Source: https://stackoverflow.com/questions/37228285/uint8array-to-arraybuffer\n */\nfunction toArrayBuffer(bytes: Uint8Array, offset?: number, length?: number): ArrayBuffer {\n    const bytesOffset = bytes.byteOffset + (offset ?? 0);\n    const bytesLength = length ?? bytes.byteLength;\n    return bytes.buffer.slice(bytesOffset, bytesOffset + bytesLength);\n}\n", "import { combineCodec, FixedSizeCodec, FixedSizeDecoder, FixedSizeEncoder } from '@solana/codecs-core';\n\nimport { NumberCodecConfig } from './common';\nimport { numberDecoderFactory, numberEncoderFactory } from './utils';\n\nexport const getU128Encoder = (config: NumberCodecConfig = {}): FixedSizeEncoder<number | bigint, 16> =>\n    numberEncoderFactory({\n        config,\n        name: 'u128',\n        range: [0, BigInt('0xffffffffffffffffffffffffffffffff')],\n        set: (view, value, le) => {\n            const leftOffset = le ? 8 : 0;\n            const rightOffset = le ? 0 : 8;\n            const rightMask = 0xffffffffffffffffn;\n            view.setBigUint64(leftOffset, BigInt(value) >> 64n, le);\n            view.setBigUint64(rightOffset, BigInt(value) & rightMask, le);\n        },\n        size: 16,\n    });\n\nexport const getU128Decoder = (config: NumberCodecConfig = {}): FixedSizeDecoder<bigint, 16> =>\n    numberDecoderFactory({\n        config,\n        get: (view, le) => {\n            const leftOffset = le ? 8 : 0;\n            const rightOffset = le ? 0 : 8;\n            const left = view.getBigUint64(leftOffset, le);\n            const right = view.getBigUint64(rightOffset, le);\n            return (left << 64n) + right;\n        },\n        name: 'u128',\n        size: 16,\n    });\n\nexport const getU128Codec = (config: NumberCodecConfig = {}): FixedSizeCodec<number | bigint, bigint, 16> =>\n    combineCodec(getU128Encoder(config), getU128Decoder(config));\n", "export const TextDecoder = globalThis.TextDecoder;\nexport const TextEncoder = globalThis.TextEncoder;\n", "import {\n    Codec,\n    combineCodec,\n    createDecoder,\n    createEncoder,\n    VariableSizeDecoder,\n    VariableSizeEncoder,\n} from '@solana/codecs-core';\nimport { TextDecoder, TextEncoder } from 'text-encoding-impl';\n\nimport { removeNullCharacters } from './null-characters';\n\n/** Encodes UTF-8 strings using the native `TextEncoder` API. */\nexport const getUtf8Encoder = (): VariableSizeEncoder<string> => {\n    let textEncoder: TextEncoder;\n    return createEncoder({\n        getSizeFromValue: value => (textEncoder ||= new TextEncoder()).encode(value).length,\n        write: (value: string, bytes, offset) => {\n            const bytesToAdd = (textEncoder ||= new TextEncoder()).encode(value);\n            bytes.set(bytesToAdd, offset);\n            return offset + bytesToAdd.length;\n        },\n    });\n};\n\n/** Decodes UTF-8 strings using the native `TextDecoder` API. */\nexport const getUtf8Decoder = (): VariableSizeDecoder<string> => {\n    let textDecoder: TextDecoder;\n    return createDecoder({\n        read(bytes, offset) {\n            const value = (textDecoder ||= new TextDecoder()).decode(bytes.slice(offset));\n            return [removeNullCharacters(value), bytes.length];\n        },\n    });\n};\n\n/** Encodes and decodes UTF-8 strings using the native `TextEncoder` and `TextDecoder` API. */\nexport const getUtf8Codec = (): Codec<string> => combineCodec(getUtf8Encoder(), getUtf8Decoder());\n", "import {\n    assertByteArrayHasEnoughBytesForCodec,\n    assertByteArrayIsNotEmptyForCodec,\n    Codec,\n    combineCodec,\n    createDecoder,\n    createEncoder,\n    Decoder,\n    Encoder,\n    fixDecoder,\n    FixedSizeCodec,\n    FixedSizeDecoder,\n    FixedSizeEncoder,\n    fixEncoder,\n    getEncodedSize,\n    VariableSizeCodec,\n    VariableSizeDecoder,\n    VariableSizeEncoder,\n} from '@solana/codecs-core';\nimport { getU32Decoder, getU32Encoder, NumberCodec, NumberDecoder, NumberEncoder } from '@solana/codecs-numbers';\n\nimport { getUtf8Decoder, getUtf8Encoder } from './utf8';\n\n/** Defines the config for string codecs. */\nexport type StringCodecConfig<\n    TPrefix extends NumberCodec | NumberEncoder | NumberDecoder,\n    TEncoding extends Codec<string> | Encoder<string> | Decoder<string>,\n> = {\n    /**\n     * The size of the string. It can be one of the following:\n     * - a {@link NumberCodec} that prefixes the string with its size.\n     * - a fixed number of bytes.\n     * - or `'variable'` to use the rest of the byte array.\n     * @defaultValue u32 prefix.\n     */\n    size?: TPrefix | number | 'variable';\n\n    /**\n     * The codec to use for encoding and decoding the content.\n     * @defaultValue UTF-8 encoding.\n     */\n    encoding?: TEncoding;\n};\n\n/** Encodes strings from a given encoding and size strategy. */\nexport function getStringEncoder<TSize extends number>(\n    config: StringCodecConfig<NumberEncoder, Encoder<string>> & { size: TSize },\n): FixedSizeEncoder<string, TSize>;\nexport function getStringEncoder<TSize extends number>(\n    config: StringCodecConfig<NumberEncoder, Encoder<string>> & {\n        size: 'variable';\n        encoding: FixedSizeEncoder<string, TSize>;\n    },\n): FixedSizeEncoder<string, TSize>;\nexport function getStringEncoder(\n    config?: StringCodecConfig<NumberEncoder, Encoder<string>>,\n): VariableSizeEncoder<string>;\nexport function getStringEncoder(config: StringCodecConfig<NumberEncoder, Encoder<string>> = {}): Encoder<string> {\n    const size = config.size ?? getU32Encoder();\n    const encoding = config.encoding ?? getUtf8Encoder();\n\n    if (size === 'variable') {\n        return encoding;\n    }\n\n    if (typeof size === 'number') {\n        return fixEncoder(encoding, size);\n    }\n\n    return createEncoder({\n        getSizeFromValue: (value: string) => {\n            const contentSize = getEncodedSize(value, encoding);\n            return getEncodedSize(contentSize, size) + contentSize;\n        },\n        write: (value: string, bytes, offset) => {\n            const contentSize = getEncodedSize(value, encoding);\n            offset = size.write(contentSize, bytes, offset);\n            return encoding.write(value, bytes, offset);\n        },\n    });\n}\n\n/** Decodes strings from a given encoding and size strategy. */\nexport function getStringDecoder<TSize extends number>(\n    config: StringCodecConfig<NumberDecoder, Decoder<string>> & { size: TSize },\n): FixedSizeDecoder<string, TSize>;\nexport function getStringDecoder<TSize extends number>(\n    config: StringCodecConfig<NumberDecoder, Decoder<string>> & {\n        size: 'variable';\n        encoding: FixedSizeDecoder<string, TSize>;\n    },\n): FixedSizeDecoder<string, TSize>;\nexport function getStringDecoder(\n    config?: StringCodecConfig<NumberDecoder, Decoder<string>>,\n): VariableSizeDecoder<string>;\nexport function getStringDecoder(config: StringCodecConfig<NumberDecoder, Decoder<string>> = {}): Decoder<string> {\n    const size = config.size ?? getU32Decoder();\n    const encoding = config.encoding ?? getUtf8Decoder();\n\n    if (size === 'variable') {\n        return encoding;\n    }\n\n    if (typeof size === 'number') {\n        return fixDecoder(encoding, size);\n    }\n\n    return createDecoder({\n        read: (bytes: Uint8Array, offset = 0) => {\n            assertByteArrayIsNotEmptyForCodec('string', bytes, offset);\n            const [lengthBigInt, lengthOffset] = size.read(bytes, offset);\n            const length = Number(lengthBigInt);\n            offset = lengthOffset;\n            const contentBytes = bytes.slice(offset, offset + length);\n            assertByteArrayHasEnoughBytesForCodec('string', length, contentBytes);\n            const [value, contentOffset] = encoding.read(contentBytes, 0);\n            offset += contentOffset;\n            return [value, offset];\n        },\n    });\n}\n\n/** Encodes and decodes strings from a given encoding and size strategy. */\nexport function getStringCodec<TSize extends number>(\n    config: StringCodecConfig<NumberCodec, Codec<string>> & { size: TSize },\n): FixedSizeCodec<string, string, TSize>;\nexport function getStringCodec<TSize extends number>(\n    config: StringCodecConfig<NumberCodec, Codec<string>> & {\n        size: 'variable';\n        encoding: FixedSizeCodec<string, string, TSize>;\n    },\n): FixedSizeCodec<string, string, TSize>;\nexport function getStringCodec(config?: StringCodecConfig<NumberCodec, Codec<string>>): VariableSizeCodec<string>;\nexport function getStringCodec(config: StringCodecConfig<NumberCodec, Codec<string>> = {}): Codec<string> {\n    return combineCodec(getStringEncoder(config), getStringDecoder(config));\n}\n"]}