{"name": "@solana/codecs-core", "version": "2.0.0-experimental.8618508", "description": "Core types and helpers for encoding and decoding byte arrays on Solana", "exports": {"browser": {"import": "./dist/index.browser.js", "require": "./dist/index.browser.cjs"}, "node": {"import": "./dist/index.node.js", "require": "./dist/index.node.cjs"}, "react-native": "./dist/index.native.js", "types": "./dist/types/index.d.ts"}, "browser": {"./dist/index.node.cjs": "./dist/index.browser.cjs", "./dist/index.node.js": "./dist/index.browser.js"}, "main": "./dist/index.node.cjs", "module": "./dist/index.node.js", "react-native": "./dist/index.native.js", "types": "./dist/types/index.d.ts", "type": "module", "files": ["./dist/"], "sideEffects": false, "keywords": ["blockchain", "solana", "web3"], "author": "Solana Labs Maintainers <<EMAIL>>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/solana-labs/solana-web3.js"}, "bugs": {"url": "http://github.com/solana-labs/solana-web3.js/issues"}, "browserslist": ["supports bigint and not dead", "maintained node versions"], "engine": {"node": ">=17.4"}, "devDependencies": {"@solana/eslint-config-solana": "^1.0.2", "@swc/jest": "^0.2.29", "@types/jest": "^29.5.6", "@typescript-eslint/eslint-plugin": "^6.7.0", "@typescript-eslint/parser": "^6.3.0", "agadoo": "^3.0.0", "eslint": "^8.45.0", "eslint-plugin-jest": "^27.4.2", "eslint-plugin-sort-keys-fix": "^1.1.2", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-runner-eslint": "^2.1.2", "jest-runner-prettier": "^1.0.0", "prettier": "^3.1", "tsup": "^8.0.1", "typescript": "^5.2.2", "version-from-git": "^1.1.1", "build-scripts": "0.0.0", "test-config": "0.0.0", "tsconfig": "0.0.0"}, "bundlewatch": {"defaultCompression": "gzip", "files": [{"path": "./dist/index*.js"}]}, "scripts": {"compile:js": "tsup --config build-scripts/tsup.config.library.ts", "compile:typedefs": "tsc -p ./tsconfig.declarations.json", "dev": "jest -c node_modules/test-config/jest-dev.config.ts --rootDir . --watch", "publish-packages": "pnpm publish --tag experimental --access public --no-git-checks", "style:fix": "pnpm eslint --fix src/* && pnpm prettier -w src/* package.json", "test:lint": "jest -c node_modules/test-config/jest-lint.config.ts --rootDir . --silent", "test:prettier": "jest -c node_modules/test-config/jest-prettier.config.ts --rootDir . --silent", "test:treeshakability:browser": "agadoo dist/index.browser.js", "test:treeshakability:native": "agadoo dist/index.native.js", "test:treeshakability:node": "agadoo dist/index.node.js", "test:typecheck": "tsc --noEmit", "test:unit:browser": "jest -c node_modules/test-config/jest-unit.config.browser.ts --rootDir . --silent", "test:unit:node": "jest -c node_modules/test-config/jest-unit.config.node.ts --rootDir . --silent"}}