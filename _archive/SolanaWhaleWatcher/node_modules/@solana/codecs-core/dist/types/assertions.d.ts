/**
 * Asserts that a given byte array is not empty.
 */
export declare function assertByteArrayIsNotEmptyForCodec(codecDescription: string, bytes: Uint8Array, offset?: number): void;
/**
 * Asserts that a given byte array has enough bytes to decode.
 */
export declare function assertByteArrayHasEnoughBytesForCodec(codecDescription: string, expected: number, bytes: Uint8Array, offset?: number): void;
//# sourceMappingURL=assertions.d.ts.map