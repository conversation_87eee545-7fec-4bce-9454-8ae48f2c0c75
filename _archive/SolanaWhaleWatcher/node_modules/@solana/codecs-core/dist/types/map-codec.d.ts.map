{"version": 3, "file": "map-codec.d.ts", "sourceRoot": "", "sources": ["../../src/map-codec.ts"], "names": [], "mappings": "AAAA,OAAO,EACH,KAAK,EAIL,OAAO,EACP,OAAO,EACP,cAAc,EACd,gBAAgB,EAChB,gBAAgB,EAEhB,iBAAiB,EACjB,mBAAmB,EACnB,mBAAmB,EACtB,MAAM,SAAS,CAAC;AAEjB;;GAEG;AACH,wBAAgB,UAAU,CAAC,QAAQ,EAAE,QAAQ,EAAE,KAAK,SAAS,MAAM,EAC/D,OAAO,EAAE,gBAAgB,CAAC,QAAQ,EAAE,KAAK,CAAC,EAC1C,KAAK,EAAE,CAAC,KAAK,EAAE,QAAQ,KAAK,QAAQ,GACrC,gBAAgB,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;AACrC,wBAAgB,UAAU,CAAC,QAAQ,EAAE,QAAQ,EACzC,OAAO,EAAE,mBAAmB,CAAC,QAAQ,CAAC,EACtC,KAAK,EAAE,CAAC,KAAK,EAAE,QAAQ,KAAK,QAAQ,GACrC,mBAAmB,CAAC,QAAQ,CAAC,CAAC;AACjC,wBAAgB,UAAU,CAAC,QAAQ,EAAE,QAAQ,EACzC,OAAO,EAAE,OAAO,CAAC,QAAQ,CAAC,EAC1B,KAAK,EAAE,CAAC,KAAK,EAAE,QAAQ,KAAK,QAAQ,GACrC,OAAO,CAAC,QAAQ,CAAC,CAAC;AAarB;;GAEG;AACH,wBAAgB,UAAU,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,SAAS,MAAM,EAC3D,OAAO,EAAE,gBAAgB,CAAC,MAAM,EAAE,KAAK,CAAC,EACxC,GAAG,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,MAAM,EAAE,MAAM,KAAK,MAAM,GAClE,gBAAgB,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AACnC,wBAAgB,UAAU,CAAC,MAAM,EAAE,MAAM,EACrC,OAAO,EAAE,mBAAmB,CAAC,MAAM,CAAC,EACpC,GAAG,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,MAAM,EAAE,MAAM,KAAK,MAAM,GAClE,mBAAmB,CAAC,MAAM,CAAC,CAAC;AAC/B,wBAAgB,UAAU,CAAC,MAAM,EAAE,MAAM,EACrC,OAAO,EAAE,OAAO,CAAC,MAAM,CAAC,EACxB,GAAG,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,MAAM,EAAE,MAAM,KAAK,MAAM,GAClE,OAAO,CAAC,MAAM,CAAC,CAAC;AAcnB;;GAEG;AACH,wBAAgB,QAAQ,CAAC,QAAQ,EAAE,QAAQ,EAAE,GAAG,SAAS,QAAQ,GAAG,QAAQ,EAAE,KAAK,SAAS,MAAM,EAC9F,KAAK,EAAE,cAAc,CAAC,QAAQ,EAAE,GAAG,EAAE,KAAK,CAAC,EAC3C,KAAK,EAAE,CAAC,KAAK,EAAE,QAAQ,KAAK,QAAQ,GACrC,cAAc,CAAC,QAAQ,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;AACxC,wBAAgB,QAAQ,CAAC,QAAQ,EAAE,QAAQ,EAAE,GAAG,SAAS,QAAQ,GAAG,QAAQ,EACxE,KAAK,EAAE,iBAAiB,CAAC,QAAQ,EAAE,GAAG,CAAC,EACvC,KAAK,EAAE,CAAC,KAAK,EAAE,QAAQ,KAAK,QAAQ,GACrC,iBAAiB,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;AACpC,wBAAgB,QAAQ,CAAC,QAAQ,EAAE,QAAQ,EAAE,GAAG,SAAS,QAAQ,GAAG,QAAQ,EACxE,KAAK,EAAE,KAAK,CAAC,QAAQ,EAAE,GAAG,CAAC,EAC3B,KAAK,EAAE,CAAC,KAAK,EAAE,QAAQ,KAAK,QAAQ,GACrC,KAAK,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;AACxB,wBAAgB,QAAQ,CAAC,QAAQ,EAAE,QAAQ,EAAE,MAAM,SAAS,QAAQ,EAAE,MAAM,SAAS,QAAQ,EAAE,KAAK,SAAS,MAAM,EAC/G,KAAK,EAAE,cAAc,CAAC,QAAQ,EAAE,MAAM,EAAE,KAAK,CAAC,EAC9C,KAAK,EAAE,CAAC,KAAK,EAAE,QAAQ,KAAK,QAAQ,EACpC,GAAG,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,MAAM,EAAE,MAAM,KAAK,MAAM,GAClE,cAAc,CAAC,QAAQ,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;AAC3C,wBAAgB,QAAQ,CAAC,QAAQ,EAAE,QAAQ,EAAE,MAAM,SAAS,QAAQ,EAAE,MAAM,SAAS,QAAQ,EACzF,KAAK,EAAE,iBAAiB,CAAC,QAAQ,EAAE,MAAM,CAAC,EAC1C,KAAK,EAAE,CAAC,KAAK,EAAE,QAAQ,KAAK,QAAQ,EACpC,GAAG,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,MAAM,EAAE,MAAM,KAAK,MAAM,GAClE,iBAAiB,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;AACvC,wBAAgB,QAAQ,CAAC,QAAQ,EAAE,QAAQ,EAAE,MAAM,SAAS,QAAQ,EAAE,MAAM,SAAS,QAAQ,EACzF,KAAK,EAAE,KAAK,CAAC,QAAQ,EAAE,MAAM,CAAC,EAC9B,KAAK,EAAE,CAAC,KAAK,EAAE,QAAQ,KAAK,QAAQ,EACpC,GAAG,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,MAAM,EAAE,MAAM,KAAK,MAAM,GAClE,KAAK,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC"}