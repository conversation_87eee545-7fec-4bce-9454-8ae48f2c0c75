this.globalThis = this.globalThis || {};
this.globalThis.solanaWeb3 = (function (exports) {
	'use strict';

	function N(e,o,r=0){if(o.length-r<=0)throw new Error(`Codec [${e}] cannot decode empty byte arrays.`)}function S(e,o,r,i=0){let T=r.length-i;if(T<o)throw new Error(`Codec [${e}] expected ${o} bytes, got ${T}.`)}var v=e=>{let o=e.filter(d=>d.length);if(o.length===0)return e.length?e[0]:new Uint8Array;if(o.length===1)return o[0];let r=o.reduce((d,F)=>d+F.length,0),i=new Uint8Array(r),T=0;return o.forEach(d=>{i.set(d,T),T+=d.length;}),i},f=(e,o)=>{if(e.length>=o)return e;let r=new Uint8Array(o).fill(0);return r.set(e),r},s=(e,o)=>f(e.length<=o?e:e.slice(0,o),o);function u(e,o){return "fixedSize"in o?o.fixedSize:o.getSizeFromValue(e)}function c(e){return Object.freeze({...e,encode:o=>{let r=new Uint8Array(u(o,e));return e.write(o,r,0),r}})}function m(e){return Object.freeze({...e,decode:(o,r=0)=>e.read(o,r)[0]})}function l(e){return Object.freeze({...e,decode:(o,r=0)=>e.read(o,r)[0],encode:o=>{let r=new Uint8Array(u(o,e));return e.write(o,r,0),r}})}function n(e){return "fixedSize"in e&&typeof e.fixedSize=="number"}function z(e,o){if(!n(e))throw new Error(o!=null?o:"Expected a fixed-size codec, got a variable-size one.")}function x(e){return !n(e)}function h(e,o){if(!x(e))throw new Error(o!=null?o:"Expected a variable-size codec, got a fixed-size one.")}function a(e,o){if(n(e)!==n(o))throw new Error("Encoder and decoder must either both be fixed-size or variable-size.");if(n(e)&&n(o)&&e.fixedSize!==o.fixedSize)throw new Error(`Encoder and decoder must have the same fixed size, got [${e.fixedSize}] and [${o.fixedSize}].`);if(!n(e)&&!n(o)&&e.maxSize!==o.maxSize)throw new Error(`Encoder and decoder must have the same max size, got [${e.maxSize}] and [${o.maxSize}].`);return {...o,...e,decode:o.decode,encode:e.encode,read:o.read,write:e.write}}function b(e,o){return c({fixedSize:o,write:(r,i,T)=>{let d=e.encode(r),F=d.length>o?d.slice(0,o):d;return i.set(F,T),T+o}})}function p(e,o){return m({fixedSize:o,read:(r,i)=>{S("fixCodec",o,r,i),(i>0||r.length>o)&&(r=r.slice(i,i+o)),n(e)&&(r=s(r,e.fixedSize));let[T]=e.read(r,0);return [T,i+o]}})}function Te(e,o){return a(b(e,o),p(e,o))}function w(e,o){return c({...x(e)?{...e,getSizeFromValue:r=>e.getSizeFromValue(o(r))}:e,write:(r,i,T)=>e.write(o(r),i,T)})}function O(e,o){return m({...e,read:(r,i)=>{let[T,d]=e.read(r,i);return [o(T,r,i),d]}})}function le(e,o,r){return l({...w(e,o),read:r?O(e,r).read:e.read})}function E(e){return z(e,"Cannot reverse a codec of variable size."),c({...e,write:(o,r,i)=>{let T=e.write(o,r,i),d=r.slice(i,i+e.fixedSize).reverse();return r.set(d,i),T}})}function C(e){return z(e,"Cannot reverse a codec of variable size."),m({...e,read:(o,r)=>{let i=r+e.fixedSize;if(r===0&&o.length===i)return e.read(o.reverse(),r);let T=o.slice();return T.set(o.slice(r,i).reverse(),r),e.read(T,r)}})}function De(e){return a(E(e),C(e))}

	exports.assertByteArrayHasEnoughBytesForCodec = S;
	exports.assertByteArrayIsNotEmptyForCodec = N;
	exports.assertIsFixedSize = z;
	exports.assertIsVariableSize = h;
	exports.combineCodec = a;
	exports.createCodec = l;
	exports.createDecoder = m;
	exports.createEncoder = c;
	exports.fixBytes = s;
	exports.fixCodec = Te;
	exports.fixDecoder = p;
	exports.fixEncoder = b;
	exports.getEncodedSize = u;
	exports.isFixedSize = n;
	exports.isVariableSize = x;
	exports.mapCodec = le;
	exports.mapDecoder = O;
	exports.mapEncoder = w;
	exports.mergeBytes = v;
	exports.padBytes = f;
	exports.reverseCodec = De;
	exports.reverseDecoder = C;
	exports.reverseEncoder = E;

	return exports;

})({});
