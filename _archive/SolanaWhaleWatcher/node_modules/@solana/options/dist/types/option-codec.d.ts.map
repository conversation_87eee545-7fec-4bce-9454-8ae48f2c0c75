{"version": 3, "file": "option-codec.d.ts", "sourceRoot": "", "sources": ["../../src/option-codec.ts"], "names": [], "mappings": "AAAA,OAAO,EAEH,KAAK,EAIL,OAAO,EACP,OAAO,EACP,cAAc,EACd,gBAAgB,EAChB,gBAAgB,EAGhB,iBAAiB,EACjB,mBAAmB,EACnB,mBAAmB,EACtB,MAAM,qBAAqB,CAAC;AAC7B,OAAO,EACH,oBAAoB,EACpB,sBAAsB,EACtB,sBAAsB,EAGtB,WAAW,EACX,aAAa,EACb,aAAa,EAChB,MAAM,wBAAwB,CAAC;AAEhC,OAAO,EAA0B,MAAM,EAAE,gBAAgB,EAAQ,MAAM,UAAU,CAAC;AAGlF,4CAA4C;AAC5C,MAAM,MAAM,iBAAiB,CAAC,OAAO,SAAS,WAAW,GAAG,aAAa,GAAG,aAAa,IAAI;IACzF;;;OAGG;IACH,MAAM,CAAC,EAAE,OAAO,CAAC;IAEjB;;;;;;;OAOG;IACH,KAAK,CAAC,EAAE,OAAO,CAAC;CACnB,CAAC;AAEF;;;;;GAKG;AACH,wBAAgB,gBAAgB,CAAC,KAAK,EAClC,IAAI,EAAE,gBAAgB,CAAC,KAAK,CAAC,EAC7B,MAAM,EAAE,iBAAiB,CAAC,sBAAsB,CAAC,GAAG;IAAE,KAAK,EAAE,IAAI,CAAA;CAAE,GACpE,gBAAgB,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC,CAAC;AAC7C,wBAAgB,gBAAgB,CAAC,KAAK,EAClC,IAAI,EAAE,gBAAgB,CAAC,KAAK,EAAE,CAAC,CAAC,EAChC,MAAM,CAAC,EAAE,iBAAiB,CAAC,sBAAsB,CAAC,GACnD,gBAAgB,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC,CAAC;AAC7C,wBAAgB,gBAAgB,CAAC,KAAK,EAClC,IAAI,EAAE,OAAO,CAAC,KAAK,CAAC,EACpB,MAAM,CAAC,EAAE,iBAAiB,CAAC,aAAa,CAAC,GAAG;IAAE,KAAK,CAAC,EAAE,KAAK,CAAA;CAAE,GAC9D,mBAAmB,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC,CAAC;AA8ChD;;;;;GAKG;AACH,wBAAgB,gBAAgB,CAAC,GAAG,EAChC,IAAI,EAAE,gBAAgB,CAAC,GAAG,CAAC,EAC3B,MAAM,EAAE,iBAAiB,CAAC,sBAAsB,CAAC,GAAG;IAAE,KAAK,EAAE,IAAI,CAAA;CAAE,GACpE,gBAAgB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;AACjC,wBAAgB,gBAAgB,CAAC,GAAG,EAChC,IAAI,EAAE,gBAAgB,CAAC,GAAG,EAAE,CAAC,CAAC,EAC9B,MAAM,CAAC,EAAE,iBAAiB,CAAC,sBAAsB,CAAC,GACnD,gBAAgB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;AACjC,wBAAgB,gBAAgB,CAAC,GAAG,EAChC,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,EAClB,MAAM,CAAC,EAAE,iBAAiB,CAAC,aAAa,CAAC,GAAG;IAAE,KAAK,CAAC,EAAE,KAAK,CAAA;CAAE,GAC9D,mBAAmB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;AAkCpC;;;;;GAKG;AACH,wBAAgB,cAAc,CAAC,KAAK,EAAE,GAAG,SAAS,KAAK,GAAG,KAAK,EAC3D,IAAI,EAAE,cAAc,CAAC,KAAK,EAAE,GAAG,CAAC,EAChC,MAAM,EAAE,iBAAiB,CAAC,oBAAoB,CAAC,GAAG;IAAE,KAAK,EAAE,IAAI,CAAA;CAAE,GAClE,cAAc,CAAC,gBAAgB,CAAC,KAAK,CAAC,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;AACxD,wBAAgB,cAAc,CAAC,KAAK,EAAE,GAAG,SAAS,KAAK,GAAG,KAAK,EAC3D,IAAI,EAAE,cAAc,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC,EACnC,MAAM,CAAC,EAAE,iBAAiB,CAAC,oBAAoB,CAAC,GACjD,cAAc,CAAC,gBAAgB,CAAC,KAAK,CAAC,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;AACxD,wBAAgB,cAAc,CAAC,KAAK,EAAE,GAAG,SAAS,KAAK,GAAG,KAAK,EAC3D,IAAI,EAAE,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC,EACvB,MAAM,CAAC,EAAE,iBAAiB,CAAC,WAAW,CAAC,GAAG;IAAE,KAAK,CAAC,EAAE,KAAK,CAAA;CAAE,GAC5D,iBAAiB,CAAC,gBAAgB,CAAC,KAAK,CAAC,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC"}