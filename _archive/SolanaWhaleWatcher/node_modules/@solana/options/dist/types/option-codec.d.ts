import { Codec, Decoder, Encoder, FixedSizeCodec, FixedSizeDecoder, FixedSizeEncoder, VariableSizeCodec, VariableSizeDecoder, VariableSizeEncoder } from '@solana/codecs-core';
import { FixedSizeNumberCodec, FixedSizeNumberDecoder, FixedSizeNumberEncoder, NumberCodec, NumberDecoder, NumberEncoder } from '@solana/codecs-numbers';
import { Option, OptionOrNullable } from './option';
/** Defines the config for option codecs. */
export type OptionCodecConfig<TPrefix extends NumberCodec | NumberEncoder | NumberDecoder> = {
    /**
     * The codec to use for the boolean prefix.
     * @defaultValue u8 prefix.
     */
    prefix?: TPrefix;
    /**
     * Whether the item codec should be of fixed size.
     *
     * When this is true, a `None` value will skip the bytes that would
     * have been used for the item. Note that this will only work if the
     * item codec is of fixed size.
     * @defaultValue `false`
     */
    fixed?: boolean;
};
/**
 * Creates a encoder for an optional value using `null` as the `None` value.
 *
 * @param item - The encoder to use for the value that may be present.
 * @param config - A set of config for the encoder.
 */
export declare function getOptionEncoder<TFrom>(item: FixedSizeEncoder<TFrom>, config: OptionCodecConfig<FixedSizeNumberEncoder> & {
    fixed: true;
}): FixedSizeEncoder<OptionOrNullable<TFrom>>;
export declare function getOptionEncoder<TFrom>(item: FixedSizeEncoder<TFrom, 0>, config?: OptionCodecConfig<FixedSizeNumberEncoder>): FixedSizeEncoder<OptionOrNullable<TFrom>>;
export declare function getOptionEncoder<TFrom>(item: Encoder<TFrom>, config?: OptionCodecConfig<NumberEncoder> & {
    fixed?: false;
}): VariableSizeEncoder<OptionOrNullable<TFrom>>;
/**
 * Creates a decoder for an optional value using `null` as the `None` value.
 *
 * @param item - The decoder to use for the value that may be present.
 * @param config - A set of config for the decoder.
 */
export declare function getOptionDecoder<TTo>(item: FixedSizeDecoder<TTo>, config: OptionCodecConfig<FixedSizeNumberDecoder> & {
    fixed: true;
}): FixedSizeDecoder<Option<TTo>>;
export declare function getOptionDecoder<TTo>(item: FixedSizeDecoder<TTo, 0>, config?: OptionCodecConfig<FixedSizeNumberDecoder>): FixedSizeDecoder<Option<TTo>>;
export declare function getOptionDecoder<TTo>(item: Decoder<TTo>, config?: OptionCodecConfig<NumberDecoder> & {
    fixed?: false;
}): VariableSizeDecoder<Option<TTo>>;
/**
 * Creates a codec for an optional value using `null` as the `None` value.
 *
 * @param item - The codec to use for the value that may be present.
 * @param config - A set of config for the codec.
 */
export declare function getOptionCodec<TFrom, TTo extends TFrom = TFrom>(item: FixedSizeCodec<TFrom, TTo>, config: OptionCodecConfig<FixedSizeNumberCodec> & {
    fixed: true;
}): FixedSizeCodec<OptionOrNullable<TFrom>, Option<TTo>>;
export declare function getOptionCodec<TFrom, TTo extends TFrom = TFrom>(item: FixedSizeCodec<TFrom, TTo, 0>, config?: OptionCodecConfig<FixedSizeNumberCodec>): FixedSizeCodec<OptionOrNullable<TFrom>, Option<TTo>>;
export declare function getOptionCodec<TFrom, TTo extends TFrom = TFrom>(item: Codec<TFrom, TTo>, config?: OptionCodecConfig<NumberCodec> & {
    fixed?: false;
}): VariableSizeCodec<OptionOrNullable<TFrom>, Option<TTo>>;
//# sourceMappingURL=option-codec.d.ts.map