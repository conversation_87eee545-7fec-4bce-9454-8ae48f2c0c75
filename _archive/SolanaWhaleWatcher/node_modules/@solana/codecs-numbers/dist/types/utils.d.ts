import { FixedSizeDecoder, FixedSizeEncoder } from '@solana/codecs-core';
import { NumberCodecConfig } from './common';
type NumberFactorySharedInput<TSize extends number> = {
    name: string;
    size: TSize;
    config?: NumberCodecConfig;
};
type NumberFactoryEncoderInput<TFrom, TSize extends number> = NumberFactorySharedInput<TSize> & {
    range?: [number | bigint, number | bigint];
    set: (view: DataView, value: TFrom, littleEndian?: boolean) => void;
};
type NumberFactoryDecoderInput<TTo, TSize extends number> = NumberFactorySharedInput<TSize> & {
    get: (view: DataView, littleEndian?: boolean) => TTo;
};
export declare function numberEncoderFactory<TFrom extends number | bigint, TSize extends number>(input: NumberFactoryEncoderInput<TFrom, TSize>): FixedSizeEncoder<TFrom, TSize>;
export declare function numberDecoderFactory<TTo extends number | bigint, TSize extends number>(input: NumberFactoryDecoderInput<TTo, TSize>): FixedSizeDecoder<TTo, TSize>;
export {};
//# sourceMappingURL=utils.d.ts.map