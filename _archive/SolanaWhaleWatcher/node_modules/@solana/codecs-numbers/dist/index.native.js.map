{"version": 3, "sources": ["../src/assertions.ts", "../src/common.ts", "../src/f32.ts", "../src/utils.ts", "../src/f64.ts", "../src/i128.ts", "../src/i16.ts", "../src/i32.ts", "../src/i64.ts", "../src/i8.ts", "../src/short-u16.ts", "../src/u128.ts", "../src/u16.ts", "../src/u32.ts", "../src/u64.ts", "../src/u8.ts"], "names": ["<PERSON><PERSON>", "combineCodec", "createDecoder", "createEncoder"], "mappings": ";AAGO,SAAS,8BACZ,kBACA,KACA,KACA,OACF;AACE,MAAI,QAAQ,OAAO,QAAQ,KAAK;AAE5B,UAAM,IAAI;AAAA,MACN,UAAU,gBAAgB,yCAAyC,GAAG,KAAK,GAAG,UAAU,KAAK;AAAA,IACjG;AAAA,EACJ;AACJ;;;ACqBO,IAAK,SAAL,kBAAKA,YAAL;AACH,EAAAA,gBAAA;AACA,EAAAA,gBAAA;AAFQ,SAAAA;AAAA,GAAA;;;ACpCZ,SAAS,oBAAwE;;;ACAjF;AAAA,EACI;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,OAIG;AAoBP,SAAS,eAAe,QAAqC;AACzD,SAAO,QAAQ,yBAAwB,QAAQ;AACnD;AAEO,SAAS,qBACZ,OAC8B;AAC9B,SAAO,cAAc;AAAA,IACjB,WAAW,MAAM;AAAA,IACjB,MAAM,OAAc,OAAmB,QAAwB;AAC3D,UAAI,MAAM,OAAO;AACb,sCAA8B,MAAM,MAAM,MAAM,MAAM,CAAC,GAAG,MAAM,MAAM,CAAC,GAAG,KAAK;AAAA,MACnF;AACA,YAAM,cAAc,IAAI,YAAY,MAAM,IAAI;AAC9C,YAAM,IAAI,IAAI,SAAS,WAAW,GAAG,OAAO,eAAe,MAAM,MAAM,CAAC;AACxE,YAAM,IAAI,IAAI,WAAW,WAAW,GAAG,MAAM;AAC7C,aAAO,SAAS,MAAM;AAAA,IAC1B;AAAA,EACJ,CAAC;AACL;AAEO,SAAS,qBACZ,OAC4B;AAC5B,SAAO,cAAc;AAAA,IACjB,WAAW,MAAM;AAAA,IACjB,KAAK,OAAO,SAAS,GAAkB;AACnC,wCAAkC,MAAM,MAAM,OAAO,MAAM;AAC3D,4CAAsC,MAAM,MAAM,MAAM,MAAM,OAAO,MAAM;AAC3E,YAAM,OAAO,IAAI,SAAS,cAAc,OAAO,QAAQ,MAAM,IAAI,CAAC;AAClE,aAAO,CAAC,MAAM,IAAI,MAAM,eAAe,MAAM,MAAM,CAAC,GAAG,SAAS,MAAM,IAAI;AAAA,IAC9E;AAAA,EACJ,CAAC;AACL;AAMA,SAAS,cAAc,OAAmB,QAAiB,QAA8B;AACrF,QAAM,cAAc,MAAM,cAAc,UAAU;AAClD,QAAM,cAAc,UAAU,MAAM;AACpC,SAAO,MAAM,OAAO,MAAM,aAAa,cAAc,WAAW;AACpE;;;ADlEO,IAAM,gBAAgB,CAAC,SAA4B,CAAC,MACvD,qBAAqB;AAAA,EACjB;AAAA,EACA,MAAM;AAAA,EACN,KAAK,CAAC,MAAM,OAAO,OAAO,KAAK,WAAW,GAAG,OAAO,EAAE;AAAA,EACtD,MAAM;AACV,CAAC;AAEE,IAAM,gBAAgB,CAAC,SAA4B,CAAC,MACvD,qBAAqB;AAAA,EACjB;AAAA,EACA,KAAK,CAAC,MAAM,OAAO,KAAK,WAAW,GAAG,EAAE;AAAA,EACxC,MAAM;AAAA,EACN,MAAM;AACV,CAAC;AAEE,IAAM,cAAc,CAAC,SAA4B,CAAC,MACrD,aAAa,cAAc,MAAM,GAAG,cAAc,MAAM,CAAC;;;AEtB7D,SAAS,gBAAAC,qBAAwE;AAK1E,IAAM,gBAAgB,CAAC,SAA4B,CAAC,MACvD,qBAAqB;AAAA,EACjB;AAAA,EACA,MAAM;AAAA,EACN,KAAK,CAAC,MAAM,OAAO,OAAO,KAAK,WAAW,GAAG,OAAO,EAAE;AAAA,EACtD,MAAM;AACV,CAAC;AAEE,IAAM,gBAAgB,CAAC,SAA4B,CAAC,MACvD,qBAAqB;AAAA,EACjB;AAAA,EACA,KAAK,CAAC,MAAM,OAAO,KAAK,WAAW,GAAG,EAAE;AAAA,EACxC,MAAM;AAAA,EACN,MAAM;AACV,CAAC;AAEE,IAAM,cAAc,CAAC,SAA4B,CAAC,MACrDA,cAAa,cAAc,MAAM,GAAG,cAAc,MAAM,CAAC;;;ACtB7D,SAAS,gBAAAA,qBAAwE;AAK1E,IAAM,iBAAiB,CAAC,SAA4B,CAAC,MACxD,qBAAqB;AAAA,EACjB;AAAA,EACA,MAAM;AAAA,EACN,OAAO,CAAC,CAAC,OAAO,oCAAoC,IAAI,IAAI,OAAO,oCAAoC,CAAC;AAAA,EACxG,KAAK,CAAC,MAAM,OAAO,OAAO;AACtB,UAAM,aAAa,KAAK,IAAI;AAC5B,UAAM,cAAc,KAAK,IAAI;AAC7B,UAAM,YAAY;AAClB,SAAK,YAAY,YAAY,OAAO,KAAK,KAAK,KAAK,EAAE;AACrD,SAAK,aAAa,aAAa,OAAO,KAAK,IAAI,WAAW,EAAE;AAAA,EAChE;AAAA,EACA,MAAM;AACV,CAAC;AAEE,IAAM,iBAAiB,CAAC,SAA4B,CAAC,MACxD,qBAAqB;AAAA,EACjB;AAAA,EACA,KAAK,CAAC,MAAM,OAAO;AACf,UAAM,aAAa,KAAK,IAAI;AAC5B,UAAM,cAAc,KAAK,IAAI;AAC7B,UAAM,OAAO,KAAK,YAAY,YAAY,EAAE;AAC5C,UAAM,QAAQ,KAAK,aAAa,aAAa,EAAE;AAC/C,YAAQ,QAAQ,OAAO;AAAA,EAC3B;AAAA,EACA,MAAM;AAAA,EACN,MAAM;AACV,CAAC;AAEE,IAAM,eAAe,CAAC,SAA4B,CAAC,MACtDA,cAAa,eAAe,MAAM,GAAG,eAAe,MAAM,CAAC;;;ACnC/D,SAAS,gBAAAA,qBAAwE;AAK1E,IAAM,gBAAgB,CAAC,SAA4B,CAAC,MACvD,qBAAqB;AAAA,EACjB;AAAA,EACA,MAAM;AAAA,EACN,OAAO,CAAC,CAAC,OAAO,QAAQ,IAAI,GAAG,OAAO,QAAQ,CAAC;AAAA,EAC/C,KAAK,CAAC,MAAM,OAAO,OAAO,KAAK,SAAS,GAAG,OAAO,EAAE;AAAA,EACpD,MAAM;AACV,CAAC;AAEE,IAAM,gBAAgB,CAAC,SAA4B,CAAC,MACvD,qBAAqB;AAAA,EACjB;AAAA,EACA,KAAK,CAAC,MAAM,OAAO,KAAK,SAAS,GAAG,EAAE;AAAA,EACtC,MAAM;AAAA,EACN,MAAM;AACV,CAAC;AAEE,IAAM,cAAc,CAAC,SAA4B,CAAC,MACrDA,cAAa,cAAc,MAAM,GAAG,cAAc,MAAM,CAAC;;;ACvB7D,SAAS,gBAAAA,qBAAwE;AAK1E,IAAM,gBAAgB,CAAC,SAA4B,CAAC,MACvD,qBAAqB;AAAA,EACjB;AAAA,EACA,MAAM;AAAA,EACN,OAAO,CAAC,CAAC,OAAO,YAAY,IAAI,GAAG,OAAO,YAAY,CAAC;AAAA,EACvD,KAAK,CAAC,MAAM,OAAO,OAAO,KAAK,SAAS,GAAG,OAAO,EAAE;AAAA,EACpD,MAAM;AACV,CAAC;AAEE,IAAM,gBAAgB,CAAC,SAA4B,CAAC,MACvD,qBAAqB;AAAA,EACjB;AAAA,EACA,KAAK,CAAC,MAAM,OAAO,KAAK,SAAS,GAAG,EAAE;AAAA,EACtC,MAAM;AAAA,EACN,MAAM;AACV,CAAC;AAEE,IAAM,cAAc,CAAC,SAA4B,CAAC,MACrDA,cAAa,cAAc,MAAM,GAAG,cAAc,MAAM,CAAC;;;ACvB7D,SAAS,gBAAAA,qBAAwE;AAK1E,IAAM,gBAAgB,CAAC,SAA4B,CAAC,MACvD,qBAAqB;AAAA,EACjB;AAAA,EACA,MAAM;AAAA,EACN,OAAO,CAAC,CAAC,OAAO,oBAAoB,IAAI,IAAI,OAAO,oBAAoB,CAAC;AAAA,EACxE,KAAK,CAAC,MAAM,OAAO,OAAO,KAAK,YAAY,GAAG,OAAO,KAAK,GAAG,EAAE;AAAA,EAC/D,MAAM;AACV,CAAC;AAEE,IAAM,gBAAgB,CAAC,SAA4B,CAAC,MACvD,qBAAqB;AAAA,EACjB;AAAA,EACA,KAAK,CAAC,MAAM,OAAO,KAAK,YAAY,GAAG,EAAE;AAAA,EACzC,MAAM;AAAA,EACN,MAAM;AACV,CAAC;AAEE,IAAM,cAAc,CAAC,SAA4B,CAAC,MACrDA,cAAa,cAAc,MAAM,GAAG,cAAc,MAAM,CAAC;;;ACvB7D,SAAS,gBAAAA,qBAAwE;AAI1E,IAAM,eAAe,MACxB,qBAAqB;AAAA,EACjB,MAAM;AAAA,EACN,OAAO,CAAC,CAAC,OAAO,MAAM,IAAI,GAAG,OAAO,MAAM,CAAC;AAAA,EAC3C,KAAK,CAAC,MAAM,UAAU,KAAK,QAAQ,GAAG,KAAK;AAAA,EAC3C,MAAM;AACV,CAAC;AAEE,IAAM,eAAe,MACxB,qBAAqB;AAAA,EACjB,KAAK,UAAQ,KAAK,QAAQ,CAAC;AAAA,EAC3B,MAAM;AAAA,EACN,MAAM;AACV,CAAC;AAEE,IAAM,aAAa,MAAyCA,cAAa,aAAa,GAAG,aAAa,CAAC;;;ACnB9G;AAAA,EACI,gBAAAA;AAAA,EACA,iBAAAC;AAAA,EACA,iBAAAC;AAAA,OAKG;AAQA,IAAM,qBAAqB,MAC9BA,eAAc;AAAA,EACV,kBAAkB,CAAC,UAA0B;AACzC,QAAI,SAAS;AAAY,aAAO;AAChC,QAAI,SAAS;AAAoB,aAAO;AACxC,WAAO;AAAA,EACX;AAAA,EACA,SAAS;AAAA,EACT,OAAO,CAAC,OAAe,OAAmB,WAA2B;AACjE,kCAA8B,YAAY,GAAG,OAAO,KAAK;AACzD,UAAM,gBAAgB,CAAC,CAAC;AACxB,aAAS,KAAK,KAAK,MAAM,GAAG;AAExB,YAAM,eAAe,SAAU,KAAK;AACpC,UAAI,iBAAiB,GAAG;AAEpB;AAAA,MACJ;AAEA,YAAM,gBAAgB,MAAY;AAClC,oBAAc,EAAE,IAAI;AACpB,UAAI,KAAK,GAAG;AAER,sBAAc,KAAK,CAAC,KAAK;AAAA,MAC7B;AAAA,IACJ;AACA,UAAM,IAAI,eAAe,MAAM;AAC/B,WAAO,SAAS,cAAc;AAAA,EAClC;AACJ,CAAC;AAME,IAAM,qBAAqB,MAC9BD,eAAc;AAAA,EACV,SAAS;AAAA,EACT,MAAM,CAAC,OAAmB,WAA6B;AACnD,QAAI,QAAQ;AACZ,QAAI,YAAY;AAChB,WAAO,EAAE,WAAW;AAChB,YAAM,YAAY,YAAY;AAC9B,YAAM,cAAc,MAAM,SAAS,SAAS;AAC5C,YAAM,gBAAgB,MAAY;AAElC,eAAS,iBAAkB,YAAY;AACvC,WAAK,cAAc,SAAgB,GAAG;AAElC;AAAA,MACJ;AAAA,IACJ;AACA,WAAO,CAAC,OAAO,SAAS,SAAS;AAAA,EACrC;AACJ,CAAC;AAWE,IAAM,mBAAmB,MAC5BD,cAAa,mBAAmB,GAAG,mBAAmB,CAAC;;;AClF3D,SAAS,gBAAAA,qBAAwE;AAK1E,IAAM,iBAAiB,CAAC,SAA4B,CAAC,MACxD,qBAAqB;AAAA,EACjB;AAAA,EACA,MAAM;AAAA,EACN,OAAO,CAAC,GAAG,OAAO,oCAAoC,CAAC;AAAA,EACvD,KAAK,CAAC,MAAM,OAAO,OAAO;AACtB,UAAM,aAAa,KAAK,IAAI;AAC5B,UAAM,cAAc,KAAK,IAAI;AAC7B,UAAM,YAAY;AAClB,SAAK,aAAa,YAAY,OAAO,KAAK,KAAK,KAAK,EAAE;AACtD,SAAK,aAAa,aAAa,OAAO,KAAK,IAAI,WAAW,EAAE;AAAA,EAChE;AAAA,EACA,MAAM;AACV,CAAC;AAEE,IAAM,iBAAiB,CAAC,SAA4B,CAAC,MACxD,qBAAqB;AAAA,EACjB;AAAA,EACA,KAAK,CAAC,MAAM,OAAO;AACf,UAAM,aAAa,KAAK,IAAI;AAC5B,UAAM,cAAc,KAAK,IAAI;AAC7B,UAAM,OAAO,KAAK,aAAa,YAAY,EAAE;AAC7C,UAAM,QAAQ,KAAK,aAAa,aAAa,EAAE;AAC/C,YAAQ,QAAQ,OAAO;AAAA,EAC3B;AAAA,EACA,MAAM;AAAA,EACN,MAAM;AACV,CAAC;AAEE,IAAM,eAAe,CAAC,SAA4B,CAAC,MACtDA,cAAa,eAAe,MAAM,GAAG,eAAe,MAAM,CAAC;;;ACnC/D,SAAS,gBAAAA,sBAAwE;AAK1E,IAAM,gBAAgB,CAAC,SAA4B,CAAC,MACvD,qBAAqB;AAAA,EACjB;AAAA,EACA,MAAM;AAAA,EACN,OAAO,CAAC,GAAG,OAAO,QAAQ,CAAC;AAAA,EAC3B,KAAK,CAAC,MAAM,OAAO,OAAO,KAAK,UAAU,GAAG,OAAO,EAAE;AAAA,EACrD,MAAM;AACV,CAAC;AAEE,IAAM,gBAAgB,CAAC,SAA4B,CAAC,MACvD,qBAAqB;AAAA,EACjB;AAAA,EACA,KAAK,CAAC,MAAM,OAAO,KAAK,UAAU,GAAG,EAAE;AAAA,EACvC,MAAM;AAAA,EACN,MAAM;AACV,CAAC;AAEE,IAAM,cAAc,CAAC,SAA4B,CAAC,MACrDA,eAAa,cAAc,MAAM,GAAG,cAAc,MAAM,CAAC;;;ACvB7D,SAAS,gBAAAA,sBAAwE;AAK1E,IAAM,gBAAgB,CAAC,SAA4B,CAAC,MACvD,qBAAqB;AAAA,EACjB;AAAA,EACA,MAAM;AAAA,EACN,OAAO,CAAC,GAAG,OAAO,YAAY,CAAC;AAAA,EAC/B,KAAK,CAAC,MAAM,OAAO,OAAO,KAAK,UAAU,GAAG,OAAO,EAAE;AAAA,EACrD,MAAM;AACV,CAAC;AAEE,IAAM,gBAAgB,CAAC,SAA4B,CAAC,MACvD,qBAAqB;AAAA,EACjB;AAAA,EACA,KAAK,CAAC,MAAM,OAAO,KAAK,UAAU,GAAG,EAAE;AAAA,EACvC,MAAM;AAAA,EACN,MAAM;AACV,CAAC;AAEE,IAAM,cAAc,CAAC,SAA4B,CAAC,MACrDA,eAAa,cAAc,MAAM,GAAG,cAAc,MAAM,CAAC;;;ACvB7D,SAAS,gBAAAA,sBAAwE;AAK1E,IAAM,gBAAgB,CAAC,SAA4B,CAAC,MACvD,qBAAqB;AAAA,EACjB;AAAA,EACA,MAAM;AAAA,EACN,OAAO,CAAC,GAAG,OAAO,oBAAoB,CAAC;AAAA,EACvC,KAAK,CAAC,MAAM,OAAO,OAAO,KAAK,aAAa,GAAG,OAAO,KAAK,GAAG,EAAE;AAAA,EAChE,MAAM;AACV,CAAC;AAEE,IAAM,gBAAgB,CAAC,SAA4B,CAAC,MACvD,qBAAqB;AAAA,EACjB;AAAA,EACA,KAAK,CAAC,MAAM,OAAO,KAAK,aAAa,GAAG,EAAE;AAAA,EAC1C,MAAM;AAAA,EACN,MAAM;AACV,CAAC;AAEE,IAAM,cAAc,CAAC,SAA4B,CAAC,MACrDA,eAAa,cAAc,MAAM,GAAG,cAAc,MAAM,CAAC;;;ACvB7D,SAAS,gBAAAA,sBAAwE;AAI1E,IAAM,eAAe,MACxB,qBAAqB;AAAA,EACjB,MAAM;AAAA,EACN,OAAO,CAAC,GAAG,OAAO,MAAM,CAAC;AAAA,EACzB,KAAK,CAAC,MAAM,UAAU,KAAK,SAAS,GAAG,KAAK;AAAA,EAC5C,MAAM;AACV,CAAC;AAEE,IAAM,eAAe,MACxB,qBAAqB;AAAA,EACjB,KAAK,UAAQ,KAAK,SAAS,CAAC;AAAA,EAC5B,MAAM;AAAA,EACN,MAAM;AACV,CAAC;AAEE,IAAM,aAAa,MAAyCA,eAAa,aAAa,GAAG,aAAa,CAAC", "sourcesContent": ["/**\n * Asserts that a given number is between a given range.\n */\nexport function assertNumberIsBetweenForCodec(\n    codecDescription: string,\n    min: number | bigint,\n    max: number | bigint,\n    value: number | bigint,\n) {\n    if (value < min || value > max) {\n        // TODO: Coded error.\n        throw new Error(\n            `Codec [${codecDescription}] expected number to be in the range [${min}, ${max}], got ${value}.`,\n        );\n    }\n}\n", "import { Codec, Decoder, Encoder, FixedSizeCodec, FixedSizeDecoder, FixedSizeEncoder } from '@solana/codecs-core';\n\n/** Defines a encoder for numbers and bigints. */\nexport type NumberEncoder = Encoder<number> | Encoder<number | bigint>;\n\n/** Defines a fixed-size encoder for numbers and bigints. */\nexport type FixedSizeNumberEncoder<TSize extends number = number> =\n    | FixedSizeEncoder<number, TSize>\n    | FixedSizeEncoder<number | bigint, TSize>;\n\n/** Defines a decoder for numbers and bigints. */\nexport type NumberDecoder = Decoder<number> | Decoder<bigint>;\n\n/** Defines a fixed-size decoder for numbers and bigints. */\nexport type FixedSizeNumberDecoder<TSize extends number = number> =\n    | FixedSizeDecoder<number, TSize>\n    | FixedSizeDecoder<bigint, TSize>;\n\n/** Defines a codec for numbers and bigints. */\nexport type NumberCodec = Codec<number> | Codec<number | bigint, bigint>;\n\n/** Defines a fixed-size codec for numbers and bigints. */\nexport type FixedSizeNumberCodec<TSize extends number = number> =\n    | FixedSizeCodec<number, number, TSize>\n    | FixedSizeCodec<number | bigint, bigint, TSize>;\n\n/** Defines the config for number codecs that use more than one byte. */\nexport type NumberCodecConfig = {\n    /**\n     * Whether the serializer should use little-endian or big-endian encoding.\n     * @defaultValue `Endian.LITTLE`\n     */\n    endian?: Endian;\n};\n\n/** Defines the endianness of a number serializer. */\nexport enum Endian {\n    LITTLE,\n    BIG,\n}\n", "import { combineCodec, FixedSizeCodec, FixedSizeDecoder, FixedSizeEncoder } from '@solana/codecs-core';\n\nimport { NumberCodecConfig } from './common';\nimport { numberDecoderFactory, numberEncoderFactory } from './utils';\n\nexport const getF32Encoder = (config: NumberCodecConfig = {}): FixedSizeEncoder<number, 4> =>\n    numberEncoderFactory({\n        config,\n        name: 'f32',\n        set: (view, value, le) => view.setFloat32(0, value, le),\n        size: 4,\n    });\n\nexport const getF32Decoder = (config: NumberCodecConfig = {}): FixedSizeDecoder<number, 4> =>\n    numberDecoderFactory({\n        config,\n        get: (view, le) => view.getFloat32(0, le),\n        name: 'f32',\n        size: 4,\n    });\n\nexport const getF32Codec = (config: NumberCodecConfig = {}): FixedSizeCodec<number, number, 4> =>\n    combineCodec(getF32Encoder(config), getF32Decoder(config));\n", "import {\n    assertByteArrayHasEnoughBytesForCodec,\n    assertByteArrayIsNotEmptyForCodec,\n    createDecoder,\n    createEncoder,\n    FixedSizeDecoder,\n    FixedSizeEncoder,\n    Offset,\n} from '@solana/codecs-core';\n\nimport { assertNumberIsBetweenForCodec } from './assertions';\nimport { Endian, NumberCodecConfig } from './common';\n\ntype NumberFactorySharedInput<TSize extends number> = {\n    name: string;\n    size: TSize;\n    config?: NumberCodecConfig;\n};\n\ntype NumberFactoryEncoderInput<TFrom, TSize extends number> = NumberFactorySharedInput<TSize> & {\n    range?: [number | bigint, number | bigint];\n    set: (view: DataView, value: TFrom, littleEndian?: boolean) => void;\n};\n\ntype NumberFactoryDecoderInput<TTo, TSize extends number> = NumberFactorySharedInput<TSize> & {\n    get: (view: DataView, littleEndian?: boolean) => TTo;\n};\n\nfunction isLittleEndian(config?: NumberCodecConfig): boolean {\n    return config?.endian === Endian.BIG ? false : true;\n}\n\nexport function numberEncoderFactory<TFrom extends number | bigint, TSize extends number>(\n    input: NumberFactoryEncoderInput<TFrom, TSize>,\n): FixedSizeEncoder<TFrom, TSize> {\n    return createEncoder({\n        fixedSize: input.size,\n        write(value: TFrom, bytes: Uint8Array, offset: Offset): Offset {\n            if (input.range) {\n                assertNumberIsBetweenForCodec(input.name, input.range[0], input.range[1], value);\n            }\n            const arrayBuffer = new ArrayBuffer(input.size);\n            input.set(new DataView(arrayBuffer), value, isLittleEndian(input.config));\n            bytes.set(new Uint8Array(arrayBuffer), offset);\n            return offset + input.size;\n        },\n    });\n}\n\nexport function numberDecoderFactory<TTo extends number | bigint, TSize extends number>(\n    input: NumberFactoryDecoderInput<TTo, TSize>,\n): FixedSizeDecoder<TTo, TSize> {\n    return createDecoder({\n        fixedSize: input.size,\n        read(bytes, offset = 0): [TTo, number] {\n            assertByteArrayIsNotEmptyForCodec(input.name, bytes, offset);\n            assertByteArrayHasEnoughBytesForCodec(input.name, input.size, bytes, offset);\n            const view = new DataView(toArrayBuffer(bytes, offset, input.size));\n            return [input.get(view, isLittleEndian(input.config)), offset + input.size];\n        },\n    });\n}\n\n/**\n * Helper function to ensure that the ArrayBuffer is converted properly from a Uint8Array\n * Source: https://stackoverflow.com/questions/37228285/uint8array-to-arraybuffer\n */\nfunction toArrayBuffer(bytes: Uint8Array, offset?: number, length?: number): ArrayBuffer {\n    const bytesOffset = bytes.byteOffset + (offset ?? 0);\n    const bytesLength = length ?? bytes.byteLength;\n    return bytes.buffer.slice(bytesOffset, bytesOffset + bytesLength);\n}\n", "import { combineCodec, FixedSizeCodec, FixedSizeDecoder, FixedSizeEncoder } from '@solana/codecs-core';\n\nimport { NumberCodecConfig } from './common';\nimport { numberDecoderFactory, numberEncoderFactory } from './utils';\n\nexport const getF64Encoder = (config: NumberCodecConfig = {}): FixedSizeEncoder<number, 8> =>\n    numberEncoderFactory({\n        config,\n        name: 'f64',\n        set: (view, value, le) => view.setFloat64(0, value, le),\n        size: 8,\n    });\n\nexport const getF64Decoder = (config: NumberCodecConfig = {}): FixedSizeDecoder<number, 8> =>\n    numberDecoderFactory({\n        config,\n        get: (view, le) => view.getFloat64(0, le),\n        name: 'f64',\n        size: 8,\n    });\n\nexport const getF64Codec = (config: NumberCodecConfig = {}): FixedSizeCodec<number, number, 8> =>\n    combineCodec(getF64Encoder(config), getF64Decoder(config));\n", "import { combineCodec, FixedSizeCodec, FixedSizeDecoder, FixedSizeEncoder } from '@solana/codecs-core';\n\nimport { NumberCodecConfig } from './common';\nimport { numberDecoderFactory, numberEncoderFactory } from './utils';\n\nexport const getI128Encoder = (config: NumberCodecConfig = {}): FixedSizeEncoder<number | bigint, 16> =>\n    numberEncoderFactory({\n        config,\n        name: 'i128',\n        range: [-BigInt('0x7fffffffffffffffffffffffffffffff') - 1n, BigInt('0x7fffffffffffffffffffffffffffffff')],\n        set: (view, value, le) => {\n            const leftOffset = le ? 8 : 0;\n            const rightOffset = le ? 0 : 8;\n            const rightMask = 0xffffffffffffffffn;\n            view.setBigInt64(leftOffset, BigInt(value) >> 64n, le);\n            view.setBigUint64(rightOffset, BigInt(value) & rightMask, le);\n        },\n        size: 16,\n    });\n\nexport const getI128Decoder = (config: NumberCodecConfig = {}): FixedSizeDecoder<bigint, 16> =>\n    numberDecoderFactory({\n        config,\n        get: (view, le) => {\n            const leftOffset = le ? 8 : 0;\n            const rightOffset = le ? 0 : 8;\n            const left = view.getBigInt64(leftOffset, le);\n            const right = view.getBigUint64(rightOffset, le);\n            return (left << 64n) + right;\n        },\n        name: 'i128',\n        size: 16,\n    });\n\nexport const getI128Codec = (config: NumberCodecConfig = {}): FixedSizeCodec<number | bigint, bigint, 16> =>\n    combineCodec(getI128Encoder(config), getI128Decoder(config));\n", "import { combineCodec, FixedSizeCodec, FixedSizeDecoder, FixedSizeEncoder } from '@solana/codecs-core';\n\nimport { NumberCodecConfig } from './common';\nimport { numberDecoderFactory, numberEncoderFactory } from './utils';\n\nexport const getI16Encoder = (config: NumberCodecConfig = {}): FixedSizeEncoder<number, 2> =>\n    numberEncoderFactory({\n        config,\n        name: 'i16',\n        range: [-Number('0x7fff') - 1, Number('0x7fff')],\n        set: (view, value, le) => view.setInt16(0, value, le),\n        size: 2,\n    });\n\nexport const getI16Decoder = (config: NumberCodecConfig = {}): FixedSizeDecoder<number, 2> =>\n    numberDecoderFactory({\n        config,\n        get: (view, le) => view.getInt16(0, le),\n        name: 'i16',\n        size: 2,\n    });\n\nexport const getI16Codec = (config: NumberCodecConfig = {}): FixedSizeCodec<number, number, 2> =>\n    combineCodec(getI16Encoder(config), getI16Decoder(config));\n", "import { combineCodec, FixedSizeCodec, FixedSizeDecoder, FixedSizeEncoder } from '@solana/codecs-core';\n\nimport { NumberCodecConfig } from './common';\nimport { numberDecoderFactory, numberEncoderFactory } from './utils';\n\nexport const getI32Encoder = (config: NumberCodecConfig = {}): FixedSizeEncoder<number, 4> =>\n    numberEncoderFactory({\n        config,\n        name: 'i32',\n        range: [-Number('0x7fffffff') - 1, Number('0x7fffffff')],\n        set: (view, value, le) => view.setInt32(0, value, le),\n        size: 4,\n    });\n\nexport const getI32Decoder = (config: NumberCodecConfig = {}): FixedSizeDecoder<number, 4> =>\n    numberDecoderFactory({\n        config,\n        get: (view, le) => view.getInt32(0, le),\n        name: 'i32',\n        size: 4,\n    });\n\nexport const getI32Codec = (config: NumberCodecConfig = {}): FixedSizeCodec<number, number, 4> =>\n    combineCodec(getI32Encoder(config), getI32Decoder(config));\n", "import { combineCodec, FixedSizeCodec, FixedSizeDecoder, FixedSizeEncoder } from '@solana/codecs-core';\n\nimport { NumberCodecConfig } from './common';\nimport { numberDecoderFactory, numberEncoderFactory } from './utils';\n\nexport const getI64Encoder = (config: NumberCodecConfig = {}): FixedSizeEncoder<number | bigint, 8> =>\n    numberEncoderFactory({\n        config,\n        name: 'i64',\n        range: [-BigInt('0x7fffffffffffffff') - 1n, BigInt('0x7fffffffffffffff')],\n        set: (view, value, le) => view.setBigInt64(0, BigInt(value), le),\n        size: 8,\n    });\n\nexport const getI64Decoder = (config: NumberCodecConfig = {}): FixedSizeDecoder<bigint, 8> =>\n    numberDecoderFactory({\n        config,\n        get: (view, le) => view.getBigInt64(0, le),\n        name: 'i64',\n        size: 8,\n    });\n\nexport const getI64Codec = (config: NumberCodecConfig = {}): FixedSizeCodec<number | bigint, bigint, 8> =>\n    combineCodec(getI64Encoder(config), getI64Decoder(config));\n", "import { combineCodec, FixedSizeCodec, FixedSizeDecoder, FixedSizeEncoder } from '@solana/codecs-core';\n\nimport { numberDecoderFactory, numberEncoderFactory } from './utils';\n\nexport const getI8Encoder = (): FixedSizeEncoder<number, 1> =>\n    numberEncoderFactory({\n        name: 'i8',\n        range: [-Number('0x7f') - 1, Number('0x7f')],\n        set: (view, value) => view.setInt8(0, value),\n        size: 1,\n    });\n\nexport const getI8Decoder = (): FixedSizeDecoder<number, 1> =>\n    numberDecoderFactory({\n        get: view => view.getInt8(0),\n        name: 'i8',\n        size: 1,\n    });\n\nexport const getI8Codec = (): FixedSizeCodec<number, number, 1> => combineCodec(getI8Encoder(), getI8Decoder());\n", "import {\n    combineCodec,\n    createDecoder,\n    createEncoder,\n    Offset,\n    VariableSizeCodec,\n    VariableSizeDecoder,\n    VariableSizeEncoder,\n} from '@solana/codecs-core';\n\nimport { assertNumberIsBetweenForCodec } from './assertions';\n\n/**\n * Encodes short u16 numbers.\n * @see {@link getShortU16Codec} for a more detailed description.\n */\nexport const getShortU16Encoder = (): VariableSizeEncoder<number> =>\n    createEncoder({\n        getSizeFromValue: (value: number): number => {\n            if (value <= 0b01111111) return 1;\n            if (value <= 0b0011111111111111) return 2;\n            return 3;\n        },\n        maxSize: 3,\n        write: (value: number, bytes: Uint8Array, offset: Offset): Offset => {\n            assertNumberIsBetweenForCodec('shortU16', 0, 65535, value);\n            const shortU16Bytes = [0];\n            for (let ii = 0; ; ii += 1) {\n                // Shift the bits of the value over such that the next 7 bits are at the right edge.\n                const alignedValue = value >> (ii * 7);\n                if (alignedValue === 0) {\n                    // No more bits to consume.\n                    break;\n                }\n                // Extract those 7 bits using a mask.\n                const nextSevenBits = 0b1111111 & alignedValue;\n                shortU16Bytes[ii] = nextSevenBits;\n                if (ii > 0) {\n                    // Set the continuation bit of the previous slice.\n                    shortU16Bytes[ii - 1] |= 0b10000000;\n                }\n            }\n            bytes.set(shortU16Bytes, offset);\n            return offset + shortU16Bytes.length;\n        },\n    });\n\n/**\n * Decodes short u16 numbers.\n * @see {@link getShortU16Codec} for a more detailed description.\n */\nexport const getShortU16Decoder = (): VariableSizeDecoder<number> =>\n    createDecoder({\n        maxSize: 3,\n        read: (bytes: Uint8Array, offset): [number, Offset] => {\n            let value = 0;\n            let byteCount = 0;\n            while (++byteCount) {\n                const byteIndex = byteCount - 1;\n                const currentByte = bytes[offset + byteIndex];\n                const nextSevenBits = 0b1111111 & currentByte;\n                // Insert the next group of seven bits into the correct slot of the output value.\n                value |= nextSevenBits << (byteIndex * 7);\n                if ((currentByte & 0b10000000) === 0) {\n                    // This byte does not have its continuation bit set. We're done.\n                    break;\n                }\n            }\n            return [value, offset + byteCount];\n        },\n    });\n\n/**\n * Encodes and decodes short u16 numbers.\n *\n * Short u16 numbers are the same as u16, but serialized with 1 to 3 bytes.\n * If the value is above 0x7f, the top bit is set and the remaining\n * value is stored in the next bytes. Each byte follows the same\n * pattern until the 3rd byte. The 3rd byte, if needed, uses\n * all 8 bits to store the last byte of the original value.\n */\nexport const getShortU16Codec = (): VariableSizeCodec<number> =>\n    combineCodec(getShortU16Encoder(), getShortU16Decoder());\n", "import { combineCodec, FixedSizeCodec, FixedSizeDecoder, FixedSizeEncoder } from '@solana/codecs-core';\n\nimport { NumberCodecConfig } from './common';\nimport { numberDecoderFactory, numberEncoderFactory } from './utils';\n\nexport const getU128Encoder = (config: NumberCodecConfig = {}): FixedSizeEncoder<number | bigint, 16> =>\n    numberEncoderFactory({\n        config,\n        name: 'u128',\n        range: [0, BigInt('0xffffffffffffffffffffffffffffffff')],\n        set: (view, value, le) => {\n            const leftOffset = le ? 8 : 0;\n            const rightOffset = le ? 0 : 8;\n            const rightMask = 0xffffffffffffffffn;\n            view.setBigUint64(leftOffset, BigInt(value) >> 64n, le);\n            view.setBigUint64(rightOffset, BigInt(value) & rightMask, le);\n        },\n        size: 16,\n    });\n\nexport const getU128Decoder = (config: NumberCodecConfig = {}): FixedSizeDecoder<bigint, 16> =>\n    numberDecoderFactory({\n        config,\n        get: (view, le) => {\n            const leftOffset = le ? 8 : 0;\n            const rightOffset = le ? 0 : 8;\n            const left = view.getBigUint64(leftOffset, le);\n            const right = view.getBigUint64(rightOffset, le);\n            return (left << 64n) + right;\n        },\n        name: 'u128',\n        size: 16,\n    });\n\nexport const getU128Codec = (config: NumberCodecConfig = {}): FixedSizeCodec<number | bigint, bigint, 16> =>\n    combineCodec(getU128Encoder(config), getU128Decoder(config));\n", "import { combineCodec, FixedSizeCodec, FixedSizeDecoder, FixedSizeEncoder } from '@solana/codecs-core';\n\nimport { NumberCodecConfig } from './common';\nimport { numberDecoderFactory, numberEncoderFactory } from './utils';\n\nexport const getU16Encoder = (config: NumberCodecConfig = {}): FixedSizeEncoder<number, 2> =>\n    numberEncoderFactory({\n        config,\n        name: 'u16',\n        range: [0, Number('0xffff')],\n        set: (view, value, le) => view.setUint16(0, value, le),\n        size: 2,\n    });\n\nexport const getU16Decoder = (config: NumberCodecConfig = {}): FixedSizeDecoder<number, 2> =>\n    numberDecoderFactory({\n        config,\n        get: (view, le) => view.getUint16(0, le),\n        name: 'u16',\n        size: 2,\n    });\n\nexport const getU16Codec = (config: NumberCodecConfig = {}): FixedSizeCodec<number, number, 2> =>\n    combineCodec(getU16Encoder(config), getU16Decoder(config));\n", "import { combineCodec, FixedSizeCodec, FixedSizeDecoder, FixedSizeEncoder } from '@solana/codecs-core';\n\nimport { NumberCodecConfig } from './common';\nimport { numberDecoderFactory, numberEncoderFactory } from './utils';\n\nexport const getU32Encoder = (config: NumberCodecConfig = {}): FixedSizeEncoder<number, 4> =>\n    numberEncoderFactory({\n        config,\n        name: 'u32',\n        range: [0, Number('0xffffffff')],\n        set: (view, value, le) => view.setUint32(0, value, le),\n        size: 4,\n    });\n\nexport const getU32Decoder = (config: NumberCodecConfig = {}): FixedSizeDecoder<number, 4> =>\n    numberDecoderFactory({\n        config,\n        get: (view, le) => view.getUint32(0, le),\n        name: 'u32',\n        size: 4,\n    });\n\nexport const getU32Codec = (config: NumberCodecConfig = {}): FixedSizeCodec<number, number, 4> =>\n    combineCodec(getU32Encoder(config), getU32Decoder(config));\n", "import { combineCodec, FixedSizeCodec, FixedSizeDecoder, FixedSizeEncoder } from '@solana/codecs-core';\n\nimport { NumberCodecConfig } from './common';\nimport { numberDecoderFactory, numberEncoderFactory } from './utils';\n\nexport const getU64Encoder = (config: NumberCodecConfig = {}): FixedSizeEncoder<number | bigint, 8> =>\n    numberEncoderFactory({\n        config,\n        name: 'u64',\n        range: [0, BigInt('0xffffffffffffffff')],\n        set: (view, value, le) => view.setBigUint64(0, BigInt(value), le),\n        size: 8,\n    });\n\nexport const getU64Decoder = (config: NumberCodecConfig = {}): FixedSizeDecoder<bigint, 8> =>\n    numberDecoderFactory({\n        config,\n        get: (view, le) => view.getBigUint64(0, le),\n        name: 'u64',\n        size: 8,\n    });\n\nexport const getU64Codec = (config: NumberCodecConfig = {}): FixedSizeCodec<number | bigint, bigint, 8> =>\n    combineCodec(getU64Encoder(config), getU64Decoder(config));\n", "import { combineCodec, FixedSizeCodec, FixedSizeDecoder, FixedSizeEncoder } from '@solana/codecs-core';\n\nimport { numberDecoderFactory, numberEncoderFactory } from './utils';\n\nexport const getU8Encoder = (): FixedSizeEncoder<number, 1> =>\n    numberEncoderFactory({\n        name: 'u8',\n        range: [0, Number('0xff')],\n        set: (view, value) => view.setUint8(0, value),\n        size: 1,\n    });\n\nexport const getU8Decoder = (): FixedSizeDecoder<number, 1> =>\n    numberDecoderFactory({\n        get: view => view.getUint8(0),\n        name: 'u8',\n        size: 1,\n    });\n\nexport const getU8Codec = (): FixedSizeCodec<number, number, 1> => combineCodec(getU8Encoder(), getU8Decoder());\n"]}