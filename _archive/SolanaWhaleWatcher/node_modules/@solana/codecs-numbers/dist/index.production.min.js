this.globalThis = this.globalThis || {};
this.globalThis.solanaWeb3 = (function (exports) {
	'use strict';

	function x(e,r,o,t){if(t<r||t>o)throw new Error(`Codec [${e}] expected number to be in the range [${r}, ${o}], got ${t}.`)}var S=(o=>(o[o.LITTLE=0]="LITTLE",o[o.BIG=1]="BIG",o))(S||{});function s(e,r,o=0){if(r.length-o<=0)throw new Error(`Codec [${e}] cannot decode empty byte arrays.`)}function z(e,r,o,t=0){let n=o.length-t;if(n<r)throw new Error(`Codec [${e}] expected ${r} bytes, got ${n}.`)}function F(e,r){return "fixedSize"in r?r.fixedSize:r.getSizeFromValue(e)}function a(e){return Object.freeze({...e,encode:r=>{let o=new Uint8Array(F(r,e));return e.write(r,o,0),o}})}function g(e){return Object.freeze({...e,decode:(r,o=0)=>e.read(r,o)[0]})}function b(e){return "fixedSize"in e&&typeof e.fixedSize=="number"}function i(e,r){if(b(e)!==b(r))throw new Error("Encoder and decoder must either both be fixed-size or variable-size.");if(b(e)&&b(r)&&e.fixedSize!==r.fixedSize)throw new Error(`Encoder and decoder must have the same fixed size, got [${e.fixedSize}] and [${r.fixedSize}].`);if(!b(e)&&!b(r)&&e.maxSize!==r.maxSize)throw new Error(`Encoder and decoder must have the same max size, got [${e.maxSize}] and [${r.maxSize}].`);return {...r,...e,decode:r.decode,encode:e.encode,read:r.read,write:e.write}}function C(e){return (e==null?void 0:e.endian)!==1}function c(e){return a({fixedSize:e.size,write(r,o,t){e.range&&x(e.name,e.range[0],e.range[1],r);let n=new ArrayBuffer(e.size);return e.set(new DataView(n),r,C(e.config)),o.set(new Uint8Array(n),t),t+e.size}})}function d(e){return g({fixedSize:e.size,read(r,o=0){s(e.name,r,o),z(e.name,e.size,r,o);let t=new DataView(p(r,o,e.size));return [e.get(t,C(e.config)),o+e.size]}})}function p(e,r,o){let t=e.byteOffset+(r!=null?r:0),n=o!=null?o:e.byteLength;return e.buffer.slice(t,t+n)}var E=(e={})=>c({config:e,name:"f32",set:(r,o,t)=>r.setFloat32(0,o,t),size:4}),D=(e={})=>d({config:e,get:(r,o)=>r.getFloat32(0,o),name:"f32",size:4}),xe=(e={})=>i(E(e),D(e));var N=(e={})=>c({config:e,name:"f64",set:(r,o,t)=>r.setFloat64(0,o,t),size:8}),y=(e={})=>d({config:e,get:(r,o)=>r.getFloat64(0,o),name:"f64",size:8}),pe=(e={})=>i(N(e),y(e));var I=(e={})=>c({config:e,name:"i128",range:[-BigInt("0x7fffffffffffffffffffffffffffffff")-1n,BigInt("0x7fffffffffffffffffffffffffffffff")],set:(r,o,t)=>{let n=t?8:0,m=t?0:8,u=0xffffffffffffffffn;r.setBigInt64(n,BigInt(o)>>64n,t),r.setBigUint64(m,BigInt(o)&u,t);},size:16}),l=(e={})=>d({config:e,get:(r,o)=>{let t=o?8:0,n=o?0:8,m=r.getBigInt64(t,o),u=r.getBigUint64(n,o);return (m<<64n)+u},name:"i128",size:16}),we=(e={})=>i(I(e),l(e));var h=(e={})=>c({config:e,name:"i16",range:[-+"0x7fff"-1,+"0x7fff"],set:(r,o,t)=>r.setInt16(0,o,t),size:2}),w=(e={})=>d({config:e,get:(r,o)=>r.getInt16(0,o),name:"i16",size:2}),$e=(e={})=>i(h(e),w(e));var U=(e={})=>c({config:e,name:"i32",range:[-+"0x7fffffff"-1,+"0x7fffffff"],set:(r,o,t)=>r.setInt32(0,o,t),size:4}),B=(e={})=>d({config:e,get:(r,o)=>r.getInt32(0,o),name:"i32",size:4}),qe=(e={})=>i(U(e),B(e));var T=(e={})=>c({config:e,name:"i64",range:[-BigInt("0x7fffffffffffffff")-1n,BigInt("0x7fffffffffffffff")],set:(r,o,t)=>r.setBigInt64(0,BigInt(o),t),size:8}),O=(e={})=>d({config:e,get:(r,o)=>r.getBigInt64(0,o),name:"i64",size:8}),Ye=(e={})=>i(T(e),O(e));var v=()=>c({name:"i8",range:[-+"0x7f"-1,+"0x7f"],set:(e,r)=>e.setInt8(0,r),size:1}),V=()=>d({get:e=>e.getInt8(0),name:"i8",size:1}),cr=()=>i(v(),V());var A=()=>a({getSizeFromValue:e=>e<=127?1:e<=16383?2:3,maxSize:3,write:(e,r,o)=>{x("shortU16",0,65535,e);let t=[0];for(let n=0;;n+=1){let m=e>>n*7;if(m===0)break;let u=127&m;t[n]=u,n>0&&(t[n-1]|=128);}return r.set(t,o),o+t.length}}),$=()=>g({maxSize:3,read:(e,r)=>{let o=0,t=0;for(;++t;){let n=t-1,m=e[r+n],u=127&m;if(o|=u<<n*7,!(m&128))break}return [o,r+t]}}),sr=()=>i(A(),$());var L=(e={})=>c({config:e,name:"u128",range:[0,BigInt("0xffffffffffffffffffffffffffffffff")],set:(r,o,t)=>{let n=t?8:0,m=t?0:8,u=0xffffffffffffffffn;r.setBigUint64(n,BigInt(o)>>64n,t),r.setBigUint64(m,BigInt(o)&u,t);},size:16}),_=(e={})=>d({config:e,get:(r,o)=>{let t=o?8:0,n=o?0:8,m=r.getBigUint64(t,o),u=r.getBigUint64(n,o);return (m<<64n)+u},name:"u128",size:16}),Nr=(e={})=>i(L(e),_(e));var k=(e={})=>c({config:e,name:"u16",range:[0,+"0xffff"],set:(r,o,t)=>r.setUint16(0,o,t),size:2}),j=(e={})=>d({config:e,get:(r,o)=>r.getUint16(0,o),name:"u16",size:2}),Tr=(e={})=>i(k(e),j(e));var G=(e={})=>c({config:e,name:"u32",range:[0,+"0xffffffff"],set:(r,o,t)=>r.setUint32(0,o,t),size:4}),H=(e={})=>d({config:e,get:(r,o)=>r.getUint32(0,o),name:"u32",size:4}),kr=(e={})=>i(G(e),H(e));var M=(e={})=>c({config:e,name:"u64",range:[0,BigInt("0xffffffffffffffff")],set:(r,o,t)=>r.setBigUint64(0,BigInt(o),t),size:8}),q=(e={})=>d({config:e,get:(r,o)=>r.getBigUint64(0,o),name:"u64",size:8}),Pr=(e={})=>i(M(e),q(e));var J=()=>c({name:"u8",range:[0,+"0xff"],set:(e,r)=>e.setUint8(0,r),size:1}),K=()=>d({get:e=>e.getUint8(0),name:"u8",size:1}),ro=()=>i(J(),K());

	exports.Endian = S;
	exports.assertNumberIsBetweenForCodec = x;
	exports.getF32Codec = xe;
	exports.getF32Decoder = D;
	exports.getF32Encoder = E;
	exports.getF64Codec = pe;
	exports.getF64Decoder = y;
	exports.getF64Encoder = N;
	exports.getI128Codec = we;
	exports.getI128Decoder = l;
	exports.getI128Encoder = I;
	exports.getI16Codec = $e;
	exports.getI16Decoder = w;
	exports.getI16Encoder = h;
	exports.getI32Codec = qe;
	exports.getI32Decoder = B;
	exports.getI32Encoder = U;
	exports.getI64Codec = Ye;
	exports.getI64Decoder = O;
	exports.getI64Encoder = T;
	exports.getI8Codec = cr;
	exports.getI8Decoder = V;
	exports.getI8Encoder = v;
	exports.getShortU16Codec = sr;
	exports.getShortU16Decoder = $;
	exports.getShortU16Encoder = A;
	exports.getU128Codec = Nr;
	exports.getU128Decoder = _;
	exports.getU128Encoder = L;
	exports.getU16Codec = Tr;
	exports.getU16Decoder = j;
	exports.getU16Encoder = k;
	exports.getU32Codec = kr;
	exports.getU32Decoder = H;
	exports.getU32Encoder = G;
	exports.getU64Codec = Pr;
	exports.getU64Decoder = q;
	exports.getU64Encoder = M;
	exports.getU8Codec = ro;
	exports.getU8Decoder = K;
	exports.getU8Encoder = J;

	return exports;

})({});
