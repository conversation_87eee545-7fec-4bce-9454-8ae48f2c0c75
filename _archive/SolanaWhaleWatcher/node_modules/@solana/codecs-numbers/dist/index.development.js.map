{"version": 3, "sources": ["../src/assertions.ts", "../src/common.ts", "../../codecs-core/src/assertions.ts", "../../codecs-core/src/codec.ts", "../../codecs-core/src/combine-codec.ts", "../src/utils.ts", "../src/f32.ts", "../src/f64.ts", "../src/i128.ts", "../src/i16.ts", "../src/i32.ts", "../src/i64.ts", "../src/i8.ts", "../src/short-u16.ts", "../src/u128.ts", "../src/u16.ts", "../src/u32.ts", "../src/u64.ts", "../src/u8.ts"], "names": ["<PERSON><PERSON>"], "mappings": ";AAGO,SAAS,8BACZ,kBACA,KACA,KACA,OACF;AACE,MAAI,QAAQ,OAAO,QAAQ,KAAK;AAE5B,UAAM,IAAI;AAAA,MACN,UAAU,gBAAgB,yCAAyC,GAAG,KAAK,GAAG,UAAU,KAAK;AAAA,IACjG;AAAA,EACJ;AACJ;;;ACqBO,IAAK,SAAL,kBAAKA,YAAL;AACH,EAAAA,gBAAA;AACA,EAAAA,gBAAA;AAFQ,SAAAA;AAAA,GAAA;;;ACjCL,SAAS,kCAAkC,kBAA0B,OAAmB,SAAS,GAAG;AACvG,MAAI,MAAM,SAAS,UAAU,GAAG;AAE5B,UAAM,IAAI,MAAM,UAAU,gBAAgB,oCAAoC;EAClF;AACJ;AAKO,SAAS,sCACZ,kBACA,UACA,OACA,SAAS,GACX;AACE,QAAM,cAAc,MAAM,SAAS;AACnC,MAAI,cAAc,UAAU;AAExB,UAAM,IAAI,MAAM,UAAU,gBAAgB,cAAc,QAAQ,eAAe,WAAW,GAAG;EACjG;AACJ;ACuDO,SAAS,eACZ,OACA,SACM;AACN,SAAO,eAAe,UAAU,QAAQ,YAAY,QAAQ,iBAAiB,KAAK;AACtF;AAUO,SAAS,cACZ,SACc;AACd,SAAO,OAAO,OAAO;IACjB,GAAG;IACH,QAAQ,CAAA,UAAS;AACb,YAAM,QAAQ,IAAI,WAAW,eAAe,OAAO,OAAO,CAAC;AAC3D,cAAQ,MAAM,OAAO,OAAO,CAAC;AAC7B,aAAO;IACX;EACJ,CAAC;AACL;AAUO,SAAS,cACZ,SACY;AACZ,SAAO,OAAO,OAAO;IACjB,GAAG;IACH,QAAQ,CAAC,OAAO,SAAS,MAAM,QAAQ,KAAK,OAAO,MAAM,EAAE,CAAC;EAChE,CAAC;AACL;AA0CO,SAAS,YAAY,OAAqF;AAC7G,SAAO,eAAe,SAAS,OAAO,MAAM,cAAc;AAC9D;ACxIO,SAAS,aACZ,SACA,SACiB;AACjB,MAAI,YAAY,OAAO,MAAM,YAAY,OAAO,GAAG;AAE/C,UAAM,IAAI,MAAM,sEAAsE;EAC1F;AAEA,MAAI,YAAY,OAAO,KAAK,YAAY,OAAO,KAAK,QAAQ,cAAc,QAAQ,WAAW;AAEzF,UAAM,IAAI;MACN,2DAA2D,QAAQ,SAAS,UAAU,QAAQ,SAAS;IAC3G;EACJ;AAEA,MAAI,CAAC,YAAY,OAAO,KAAK,CAAC,YAAY,OAAO,KAAK,QAAQ,YAAY,QAAQ,SAAS;AAEvF,UAAM,IAAI;MACN,yDAAyD,QAAQ,OAAO,UAAU,QAAQ,OAAO;IACrG;EACJ;AAEA,SAAO;IACH,GAAG;IACH,GAAG;IACH,QAAQ,QAAQ;IAChB,QAAQ,QAAQ;IAChB,MAAM,QAAQ;IACd,OAAO,QAAQ;EACnB;AACJ;;;ACjCA,SAAS,eAAe,QAAqC;AACzD,UAAO,iCAAQ,0BAAwB,QAAQ;AACnD;AAEO,SAAS,qBACZ,OAC8B;AAC9B,SAAO,cAAc;AAAA,IACjB,WAAW,MAAM;AAAA,IACjB,MAAM,OAAc,OAAmB,QAAwB;AAC3D,UAAI,MAAM,OAAO;AACb,sCAA8B,MAAM,MAAM,MAAM,MAAM,CAAC,GAAG,MAAM,MAAM,CAAC,GAAG,KAAK;AAAA,MACnF;AACA,YAAM,cAAc,IAAI,YAAY,MAAM,IAAI;AAC9C,YAAM,IAAI,IAAI,SAAS,WAAW,GAAG,OAAO,eAAe,MAAM,MAAM,CAAC;AACxE,YAAM,IAAI,IAAI,WAAW,WAAW,GAAG,MAAM;AAC7C,aAAO,SAAS,MAAM;AAAA,IAC1B;AAAA,EACJ,CAAC;AACL;AAEO,SAAS,qBACZ,OAC4B;AAC5B,SAAO,cAAc;AAAA,IACjB,WAAW,MAAM;AAAA,IACjB,KAAK,OAAO,SAAS,GAAkB;AACnC,wCAAkC,MAAM,MAAM,OAAO,MAAM;AAC3D,4CAAsC,MAAM,MAAM,MAAM,MAAM,OAAO,MAAM;AAC3E,YAAM,OAAO,IAAI,SAAS,cAAc,OAAO,QAAQ,MAAM,IAAI,CAAC;AAClE,aAAO,CAAC,MAAM,IAAI,MAAM,eAAe,MAAM,MAAM,CAAC,GAAG,SAAS,MAAM,IAAI;AAAA,IAC9E;AAAA,EACJ,CAAC;AACL;AAMA,SAAS,cAAc,OAAmB,QAAiB,QAA8B;AACrF,QAAM,cAAc,MAAM,cAAc,0BAAU;AAClD,QAAM,cAAc,0BAAU,MAAM;AACpC,SAAO,MAAM,OAAO,MAAM,aAAa,cAAc,WAAW;AACpE;;;AClEO,IAAM,gBAAgB,CAAC,SAA4B,CAAC,MACvD,qBAAqB;AAAA,EACjB;AAAA,EACA,MAAM;AAAA,EACN,KAAK,CAAC,MAAM,OAAO,OAAO,KAAK,WAAW,GAAG,OAAO,EAAE;AAAA,EACtD,MAAM;AACV,CAAC;AAEE,IAAM,gBAAgB,CAAC,SAA4B,CAAC,MACvD,qBAAqB;AAAA,EACjB;AAAA,EACA,KAAK,CAAC,MAAM,OAAO,KAAK,WAAW,GAAG,EAAE;AAAA,EACxC,MAAM;AAAA,EACN,MAAM;AACV,CAAC;AAEE,IAAM,cAAc,CAAC,SAA4B,CAAC,MACrD,aAAa,cAAc,MAAM,GAAG,cAAc,MAAM,CAAC;;;ACjBtD,IAAM,gBAAgB,CAAC,SAA4B,CAAC,MACvD,qBAAqB;AAAA,EACjB;AAAA,EACA,MAAM;AAAA,EACN,KAAK,CAAC,MAAM,OAAO,OAAO,KAAK,WAAW,GAAG,OAAO,EAAE;AAAA,EACtD,MAAM;AACV,CAAC;AAEE,IAAM,gBAAgB,CAAC,SAA4B,CAAC,MACvD,qBAAqB;AAAA,EACjB;AAAA,EACA,KAAK,CAAC,MAAM,OAAO,KAAK,WAAW,GAAG,EAAE;AAAA,EACxC,MAAM;AAAA,EACN,MAAM;AACV,CAAC;AAEE,IAAM,cAAc,CAAC,SAA4B,CAAC,MACrD,aAAa,cAAc,MAAM,GAAG,cAAc,MAAM,CAAC;;;ACjBtD,IAAM,iBAAiB,CAAC,SAA4B,CAAC,MACxD,qBAAqB;AAAA,EACjB;AAAA,EACA,MAAM;AAAA,EACN,OAAO,CAAC,CAAC,OAAO,oCAAoC,IAAI,IAAI,OAAO,oCAAoC,CAAC;AAAA,EACxG,KAAK,CAAC,MAAM,OAAO,OAAO;AACtB,UAAM,aAAa,KAAK,IAAI;AAC5B,UAAM,cAAc,KAAK,IAAI;AAC7B,UAAM,YAAY;AAClB,SAAK,YAAY,YAAY,OAAO,KAAK,KAAK,KAAK,EAAE;AACrD,SAAK,aAAa,aAAa,OAAO,KAAK,IAAI,WAAW,EAAE;AAAA,EAChE;AAAA,EACA,MAAM;AACV,CAAC;AAEE,IAAM,iBAAiB,CAAC,SAA4B,CAAC,MACxD,qBAAqB;AAAA,EACjB;AAAA,EACA,KAAK,CAAC,MAAM,OAAO;AACf,UAAM,aAAa,KAAK,IAAI;AAC5B,UAAM,cAAc,KAAK,IAAI;AAC7B,UAAM,OAAO,KAAK,YAAY,YAAY,EAAE;AAC5C,UAAM,QAAQ,KAAK,aAAa,aAAa,EAAE;AAC/C,YAAQ,QAAQ,OAAO;AAAA,EAC3B;AAAA,EACA,MAAM;AAAA,EACN,MAAM;AACV,CAAC;AAEE,IAAM,eAAe,CAAC,SAA4B,CAAC,MACtD,aAAa,eAAe,MAAM,GAAG,eAAe,MAAM,CAAC;;;AC9BxD,IAAM,gBAAgB,CAAC,SAA4B,CAAC,MACvD,qBAAqB;AAAA,EACjB;AAAA,EACA,MAAM;AAAA,EACN,OAAO,CAAC,CAAC,OAAO,QAAQ,IAAI,GAAG,OAAO,QAAQ,CAAC;AAAA,EAC/C,KAAK,CAAC,MAAM,OAAO,OAAO,KAAK,SAAS,GAAG,OAAO,EAAE;AAAA,EACpD,MAAM;AACV,CAAC;AAEE,IAAM,gBAAgB,CAAC,SAA4B,CAAC,MACvD,qBAAqB;AAAA,EACjB;AAAA,EACA,KAAK,CAAC,MAAM,OAAO,KAAK,SAAS,GAAG,EAAE;AAAA,EACtC,MAAM;AAAA,EACN,MAAM;AACV,CAAC;AAEE,IAAM,cAAc,CAAC,SAA4B,CAAC,MACrD,aAAa,cAAc,MAAM,GAAG,cAAc,MAAM,CAAC;;;AClBtD,IAAM,gBAAgB,CAAC,SAA4B,CAAC,MACvD,qBAAqB;AAAA,EACjB;AAAA,EACA,MAAM;AAAA,EACN,OAAO,CAAC,CAAC,OAAO,YAAY,IAAI,GAAG,OAAO,YAAY,CAAC;AAAA,EACvD,KAAK,CAAC,MAAM,OAAO,OAAO,KAAK,SAAS,GAAG,OAAO,EAAE;AAAA,EACpD,MAAM;AACV,CAAC;AAEE,IAAM,gBAAgB,CAAC,SAA4B,CAAC,MACvD,qBAAqB;AAAA,EACjB;AAAA,EACA,KAAK,CAAC,MAAM,OAAO,KAAK,SAAS,GAAG,EAAE;AAAA,EACtC,MAAM;AAAA,EACN,MAAM;AACV,CAAC;AAEE,IAAM,cAAc,CAAC,SAA4B,CAAC,MACrD,aAAa,cAAc,MAAM,GAAG,cAAc,MAAM,CAAC;;;AClBtD,IAAM,gBAAgB,CAAC,SAA4B,CAAC,MACvD,qBAAqB;AAAA,EACjB;AAAA,EACA,MAAM;AAAA,EACN,OAAO,CAAC,CAAC,OAAO,oBAAoB,IAAI,IAAI,OAAO,oBAAoB,CAAC;AAAA,EACxE,KAAK,CAAC,MAAM,OAAO,OAAO,KAAK,YAAY,GAAG,OAAO,KAAK,GAAG,EAAE;AAAA,EAC/D,MAAM;AACV,CAAC;AAEE,IAAM,gBAAgB,CAAC,SAA4B,CAAC,MACvD,qBAAqB;AAAA,EACjB;AAAA,EACA,KAAK,CAAC,MAAM,OAAO,KAAK,YAAY,GAAG,EAAE;AAAA,EACzC,MAAM;AAAA,EACN,MAAM;AACV,CAAC;AAEE,IAAM,cAAc,CAAC,SAA4B,CAAC,MACrD,aAAa,cAAc,MAAM,GAAG,cAAc,MAAM,CAAC;;;ACnBtD,IAAM,eAAe,MACxB,qBAAqB;AAAA,EACjB,MAAM;AAAA,EACN,OAAO,CAAC,CAAC,OAAO,MAAM,IAAI,GAAG,OAAO,MAAM,CAAC;AAAA,EAC3C,KAAK,CAAC,MAAM,UAAU,KAAK,QAAQ,GAAG,KAAK;AAAA,EAC3C,MAAM;AACV,CAAC;AAEE,IAAM,eAAe,MACxB,qBAAqB;AAAA,EACjB,KAAK,UAAQ,KAAK,QAAQ,CAAC;AAAA,EAC3B,MAAM;AAAA,EACN,MAAM;AACV,CAAC;AAEE,IAAM,aAAa,MAAyC,aAAa,aAAa,GAAG,aAAa,CAAC;;;ACHvG,IAAM,qBAAqB,MAC9B,cAAc;AAAA,EACV,kBAAkB,CAAC,UAA0B;AACzC,QAAI,SAAS;AAAY,aAAO;AAChC,QAAI,SAAS;AAAoB,aAAO;AACxC,WAAO;AAAA,EACX;AAAA,EACA,SAAS;AAAA,EACT,OAAO,CAAC,OAAe,OAAmB,WAA2B;AACjE,kCAA8B,YAAY,GAAG,OAAO,KAAK;AACzD,UAAM,gBAAgB,CAAC,CAAC;AACxB,aAAS,KAAK,KAAK,MAAM,GAAG;AAExB,YAAM,eAAe,SAAU,KAAK;AACpC,UAAI,iBAAiB,GAAG;AAEpB;AAAA,MACJ;AAEA,YAAM,gBAAgB,MAAY;AAClC,oBAAc,EAAE,IAAI;AACpB,UAAI,KAAK,GAAG;AAER,sBAAc,KAAK,CAAC,KAAK;AAAA,MAC7B;AAAA,IACJ;AACA,UAAM,IAAI,eAAe,MAAM;AAC/B,WAAO,SAAS,cAAc;AAAA,EAClC;AACJ,CAAC;AAME,IAAM,qBAAqB,MAC9B,cAAc;AAAA,EACV,SAAS;AAAA,EACT,MAAM,CAAC,OAAmB,WAA6B;AACnD,QAAI,QAAQ;AACZ,QAAI,YAAY;AAChB,WAAO,EAAE,WAAW;AAChB,YAAM,YAAY,YAAY;AAC9B,YAAM,cAAc,MAAM,SAAS,SAAS;AAC5C,YAAM,gBAAgB,MAAY;AAElC,eAAS,iBAAkB,YAAY;AACvC,WAAK,cAAc,SAAgB,GAAG;AAElC;AAAA,MACJ;AAAA,IACJ;AACA,WAAO,CAAC,OAAO,SAAS,SAAS;AAAA,EACrC;AACJ,CAAC;AAWE,IAAM,mBAAmB,MAC5B,aAAa,mBAAmB,GAAG,mBAAmB,CAAC;;;AC7EpD,IAAM,iBAAiB,CAAC,SAA4B,CAAC,MACxD,qBAAqB;AAAA,EACjB;AAAA,EACA,MAAM;AAAA,EACN,OAAO,CAAC,GAAG,OAAO,oCAAoC,CAAC;AAAA,EACvD,KAAK,CAAC,MAAM,OAAO,OAAO;AACtB,UAAM,aAAa,KAAK,IAAI;AAC5B,UAAM,cAAc,KAAK,IAAI;AAC7B,UAAM,YAAY;AAClB,SAAK,aAAa,YAAY,OAAO,KAAK,KAAK,KAAK,EAAE;AACtD,SAAK,aAAa,aAAa,OAAO,KAAK,IAAI,WAAW,EAAE;AAAA,EAChE;AAAA,EACA,MAAM;AACV,CAAC;AAEE,IAAM,iBAAiB,CAAC,SAA4B,CAAC,MACxD,qBAAqB;AAAA,EACjB;AAAA,EACA,KAAK,CAAC,MAAM,OAAO;AACf,UAAM,aAAa,KAAK,IAAI;AAC5B,UAAM,cAAc,KAAK,IAAI;AAC7B,UAAM,OAAO,KAAK,aAAa,YAAY,EAAE;AAC7C,UAAM,QAAQ,KAAK,aAAa,aAAa,EAAE;AAC/C,YAAQ,QAAQ,OAAO;AAAA,EAC3B;AAAA,EACA,MAAM;AAAA,EACN,MAAM;AACV,CAAC;AAEE,IAAM,eAAe,CAAC,SAA4B,CAAC,MACtD,aAAa,eAAe,MAAM,GAAG,eAAe,MAAM,CAAC;;;AC9BxD,IAAM,gBAAgB,CAAC,SAA4B,CAAC,MACvD,qBAAqB;AAAA,EACjB;AAAA,EACA,MAAM;AAAA,EACN,OAAO,CAAC,GAAG,OAAO,QAAQ,CAAC;AAAA,EAC3B,KAAK,CAAC,MAAM,OAAO,OAAO,KAAK,UAAU,GAAG,OAAO,EAAE;AAAA,EACrD,MAAM;AACV,CAAC;AAEE,IAAM,gBAAgB,CAAC,SAA4B,CAAC,MACvD,qBAAqB;AAAA,EACjB;AAAA,EACA,KAAK,CAAC,MAAM,OAAO,KAAK,UAAU,GAAG,EAAE;AAAA,EACvC,MAAM;AAAA,EACN,MAAM;AACV,CAAC;AAEE,IAAM,cAAc,CAAC,SAA4B,CAAC,MACrD,aAAa,cAAc,MAAM,GAAG,cAAc,MAAM,CAAC;;;AClBtD,IAAM,gBAAgB,CAAC,SAA4B,CAAC,MACvD,qBAAqB;AAAA,EACjB;AAAA,EACA,MAAM;AAAA,EACN,OAAO,CAAC,GAAG,OAAO,YAAY,CAAC;AAAA,EAC/B,KAAK,CAAC,MAAM,OAAO,OAAO,KAAK,UAAU,GAAG,OAAO,EAAE;AAAA,EACrD,MAAM;AACV,CAAC;AAEE,IAAM,gBAAgB,CAAC,SAA4B,CAAC,MACvD,qBAAqB;AAAA,EACjB;AAAA,EACA,KAAK,CAAC,MAAM,OAAO,KAAK,UAAU,GAAG,EAAE;AAAA,EACvC,MAAM;AAAA,EACN,MAAM;AACV,CAAC;AAEE,IAAM,cAAc,CAAC,SAA4B,CAAC,MACrD,aAAa,cAAc,MAAM,GAAG,cAAc,MAAM,CAAC;;;AClBtD,IAAM,gBAAgB,CAAC,SAA4B,CAAC,MACvD,qBAAqB;AAAA,EACjB;AAAA,EACA,MAAM;AAAA,EACN,OAAO,CAAC,GAAG,OAAO,oBAAoB,CAAC;AAAA,EACvC,KAAK,CAAC,MAAM,OAAO,OAAO,KAAK,aAAa,GAAG,OAAO,KAAK,GAAG,EAAE;AAAA,EAChE,MAAM;AACV,CAAC;AAEE,IAAM,gBAAgB,CAAC,SAA4B,CAAC,MACvD,qBAAqB;AAAA,EACjB;AAAA,EACA,KAAK,CAAC,MAAM,OAAO,KAAK,aAAa,GAAG,EAAE;AAAA,EAC1C,MAAM;AAAA,EACN,MAAM;AACV,CAAC;AAEE,IAAM,cAAc,CAAC,SAA4B,CAAC,MACrD,aAAa,cAAc,MAAM,GAAG,cAAc,MAAM,CAAC;;;ACnBtD,IAAM,eAAe,MACxB,qBAAqB;AAAA,EACjB,MAAM;AAAA,EACN,OAAO,CAAC,GAAG,OAAO,MAAM,CAAC;AAAA,EACzB,KAAK,CAAC,MAAM,UAAU,KAAK,SAAS,GAAG,KAAK;AAAA,EAC5C,MAAM;AACV,CAAC;AAEE,IAAM,eAAe,MACxB,qBAAqB;AAAA,EACjB,KAAK,UAAQ,KAAK,SAAS,CAAC;AAAA,EAC5B,MAAM;AAAA,EACN,MAAM;AACV,CAAC;AAEE,IAAM,aAAa,MAAyC,aAAa,aAAa,GAAG,aAAa,CAAC", "sourcesContent": ["/**\n * Asserts that a given number is between a given range.\n */\nexport function assertNumberIsBetweenForCodec(\n    codecDescription: string,\n    min: number | bigint,\n    max: number | bigint,\n    value: number | bigint,\n) {\n    if (value < min || value > max) {\n        // TODO: Coded error.\n        throw new Error(\n            `Codec [${codecDescription}] expected number to be in the range [${min}, ${max}], got ${value}.`,\n        );\n    }\n}\n", "import { Codec, Decoder, Encoder, FixedSizeCodec, FixedSizeDecoder, FixedSizeEncoder } from '@solana/codecs-core';\n\n/** Defines a encoder for numbers and bigints. */\nexport type NumberEncoder = Encoder<number> | Encoder<number | bigint>;\n\n/** Defines a fixed-size encoder for numbers and bigints. */\nexport type FixedSizeNumberEncoder<TSize extends number = number> =\n    | FixedSizeEncoder<number, TSize>\n    | FixedSizeEncoder<number | bigint, TSize>;\n\n/** Defines a decoder for numbers and bigints. */\nexport type NumberDecoder = Decoder<number> | Decoder<bigint>;\n\n/** Defines a fixed-size decoder for numbers and bigints. */\nexport type FixedSizeNumberDecoder<TSize extends number = number> =\n    | FixedSizeDecoder<number, TSize>\n    | FixedSizeDecoder<bigint, TSize>;\n\n/** Defines a codec for numbers and bigints. */\nexport type NumberCodec = Codec<number> | Codec<number | bigint, bigint>;\n\n/** Defines a fixed-size codec for numbers and bigints. */\nexport type FixedSizeNumberCodec<TSize extends number = number> =\n    | FixedSizeCodec<number, number, TSize>\n    | FixedSizeCodec<number | bigint, bigint, TSize>;\n\n/** Defines the config for number codecs that use more than one byte. */\nexport type NumberCodecConfig = {\n    /**\n     * Whether the serializer should use little-endian or big-endian encoding.\n     * @defaultValue `Endian.LITTLE`\n     */\n    endian?: Endian;\n};\n\n/** Defines the endianness of a number serializer. */\nexport enum Endian {\n    LITTLE,\n    BIG,\n}\n", "/**\n * Asserts that a given byte array is not empty.\n */\nexport function assertByteArrayIsNotEmptyForCodec(codecDescription: string, bytes: Uint8Array, offset = 0) {\n    if (bytes.length - offset <= 0) {\n        // TODO: Coded error.\n        throw new Error(`Codec [${codecDescription}] cannot decode empty byte arrays.`);\n    }\n}\n\n/**\n * Asserts that a given byte array has enough bytes to decode.\n */\nexport function assertByteArrayHasEnoughBytesForCodec(\n    codecDescription: string,\n    expected: number,\n    bytes: Uint8Array,\n    offset = 0,\n) {\n    const bytesLength = bytes.length - offset;\n    if (bytesLength < expected) {\n        // TODO: Coded error.\n        throw new Error(`Codec [${codecDescription}] expected ${expected} bytes, got ${bytesLength}.`);\n    }\n}\n", "/**\n * Defines an offset in bytes.\n */\nexport type Offset = number;\n\ntype BaseEncoder<TFrom> = {\n    /** Encode the provided value and return the encoded bytes directly. */\n    readonly encode: (value: TFrom) => Uint8Array;\n    /**\n     * Writes the encoded value into the provided byte array at the given offset.\n     * Returns the offset of the next byte after the encoded value.\n     */\n    readonly write: (value: TFrom, bytes: Uint8Array, offset: Offset) => Offset;\n};\n\nexport type FixedSizeEncoder<TFrom, TSize extends number = number> = BaseEncoder<TFrom> & {\n    /** The fixed size of the encoded value in bytes. */\n    readonly fixedSize: TSize;\n};\n\nexport type VariableSizeEncoder<TFrom> = BaseEncoder<TFrom> & {\n    /** The total size of the encoded value in bytes. */\n    readonly getSizeFromValue: (value: TFrom) => number;\n    /** The maximum size an encoded value can be in bytes, if applicable. */\n    readonly maxSize?: number;\n};\n\n/**\n * An object that can encode a value to a `Uint8Array`.\n */\nexport type Encoder<TFrom> = FixedSizeEncoder<TFrom> | VariableSizeEncoder<TFrom>;\n\ntype BaseDecoder<TTo> = {\n    /** Decodes the provided byte array at the given offset (or zero) and returns the value directly. */\n    readonly decode: (bytes: Uint8Array, offset?: Offset) => TTo;\n    /**\n     * Reads the encoded value from the provided byte array at the given offset.\n     * Returns the decoded value and the offset of the next byte after the encoded value.\n     */\n    readonly read: (bytes: Uint8Array, offset: Offset) => [TTo, Offset];\n};\n\nexport type FixedSizeDecoder<TTo, TSize extends number = number> = BaseDecoder<TTo> & {\n    /** The fixed size of the encoded value in bytes. */\n    readonly fixedSize: TSize;\n};\n\nexport type VariableSizeDecoder<TTo> = BaseDecoder<TTo> & {\n    /** The maximum size an encoded value can be in bytes, if applicable. */\n    readonly maxSize?: number;\n};\n\n/**\n * An object that can decode a value from a `Uint8Array`.\n */\nexport type Decoder<TTo> = FixedSizeDecoder<TTo> | VariableSizeDecoder<TTo>;\n\nexport type FixedSizeCodec<TFrom, TTo extends TFrom = TFrom, TSize extends number = number> = FixedSizeEncoder<\n    TFrom,\n    TSize\n> &\n    FixedSizeDecoder<TTo, TSize>;\n\nexport type VariableSizeCodec<TFrom, TTo extends TFrom = TFrom> = VariableSizeEncoder<TFrom> & VariableSizeDecoder<TTo>;\n\n/**\n * An object that can encode and decode a value to and from a `Uint8Array`.\n * It supports encoding looser types than it decodes for convenience.\n * For example, a `bigint` encoder will always decode to a `bigint`\n * but can be used to encode a `number`.\n *\n * @typeParam TFrom - The type of the value to encode.\n * @typeParam TTo - The type of the decoded value. Defaults to `TFrom`.\n */\nexport type Codec<TFrom, TTo extends TFrom = TFrom> = FixedSizeCodec<TFrom, TTo> | VariableSizeCodec<TFrom, TTo>;\n\n/**\n * Get the encoded size of a given value in bytes.\n */\nexport function getEncodedSize<TFrom>(\n    value: TFrom,\n    encoder: { fixedSize: number } | { getSizeFromValue: (value: TFrom) => number },\n): number {\n    return 'fixedSize' in encoder ? encoder.fixedSize : encoder.getSizeFromValue(value);\n}\n\n/** Fills the missing `encode` function using the existing `write` function. */\nexport function createEncoder<TFrom, TSize extends number>(\n    encoder: Omit<FixedSizeEncoder<TFrom, TSize>, 'encode'>,\n): FixedSizeEncoder<TFrom, TSize>;\nexport function createEncoder<TFrom>(encoder: Omit<VariableSizeEncoder<TFrom>, 'encode'>): VariableSizeEncoder<TFrom>;\nexport function createEncoder<TFrom>(\n    encoder: Omit<FixedSizeEncoder<TFrom>, 'encode'> | Omit<VariableSizeEncoder<TFrom>, 'encode'>,\n): Encoder<TFrom>;\nexport function createEncoder<TFrom>(\n    encoder: Omit<FixedSizeEncoder<TFrom>, 'encode'> | Omit<VariableSizeEncoder<TFrom>, 'encode'>,\n): Encoder<TFrom> {\n    return Object.freeze({\n        ...encoder,\n        encode: value => {\n            const bytes = new Uint8Array(getEncodedSize(value, encoder));\n            encoder.write(value, bytes, 0);\n            return bytes;\n        },\n    });\n}\n\n/** Fills the missing `decode` function using the existing `read` function. */\nexport function createDecoder<TTo, TSize extends number>(\n    decoder: Omit<FixedSizeDecoder<TTo, TSize>, 'decode'>,\n): FixedSizeDecoder<TTo, TSize>;\nexport function createDecoder<TTo>(decoder: Omit<VariableSizeDecoder<TTo>, 'decode'>): VariableSizeDecoder<TTo>;\nexport function createDecoder<TTo>(\n    decoder: Omit<FixedSizeDecoder<TTo>, 'decode'> | Omit<VariableSizeDecoder<TTo>, 'decode'>,\n): Decoder<TTo>;\nexport function createDecoder<TTo>(\n    decoder: Omit<FixedSizeDecoder<TTo>, 'decode'> | Omit<VariableSizeDecoder<TTo>, 'decode'>,\n): Decoder<TTo> {\n    return Object.freeze({\n        ...decoder,\n        decode: (bytes, offset = 0) => decoder.read(bytes, offset)[0],\n    });\n}\n\n/** Fills the missing `encode` and `decode` function using the existing `write` and `read` functions. */\nexport function createCodec<TFrom, TTo extends TFrom = TFrom, TSize extends number = number>(\n    codec: Omit<FixedSizeCodec<TFrom, TTo, TSize>, 'encode' | 'decode'>,\n): FixedSizeCodec<TFrom, TTo, TSize>;\nexport function createCodec<TFrom, TTo extends TFrom = TFrom>(\n    codec: Omit<VariableSizeCodec<TFrom, TTo>, 'encode' | 'decode'>,\n): VariableSizeCodec<TFrom, TTo>;\nexport function createCodec<TFrom, TTo extends TFrom = TFrom>(\n    codec:\n        | Omit<FixedSizeCodec<TFrom, TTo>, 'encode' | 'decode'>\n        | Omit<VariableSizeCodec<TFrom, TTo>, 'encode' | 'decode'>,\n): Codec<TFrom, TTo>;\nexport function createCodec<TFrom, TTo extends TFrom = TFrom>(\n    codec:\n        | Omit<FixedSizeCodec<TFrom, TTo>, 'encode' | 'decode'>\n        | Omit<VariableSizeCodec<TFrom, TTo>, 'encode' | 'decode'>,\n): Codec<TFrom, TTo> {\n    return Object.freeze({\n        ...codec,\n        decode: (bytes, offset = 0) => codec.read(bytes, offset)[0],\n        encode: value => {\n            const bytes = new Uint8Array(getEncodedSize(value, codec));\n            codec.write(value, bytes, 0);\n            return bytes;\n        },\n    });\n}\n\nexport function isFixedSize<TFrom, TSize extends number>(\n    encoder: FixedSizeEncoder<TFrom, TSize> | VariableSizeEncoder<TFrom>,\n): encoder is FixedSizeEncoder<TFrom, TSize>;\nexport function isFixedSize<TTo, TSize extends number>(\n    decoder: FixedSizeDecoder<TTo, TSize> | VariableSizeDecoder<TTo>,\n): decoder is FixedSizeDecoder<TTo, TSize>;\nexport function isFixedSize<TFrom, TTo extends TFrom, TSize extends number>(\n    codec: FixedSizeCodec<TFrom, TTo, TSize> | VariableSizeCodec<TFrom, TTo>,\n): codec is FixedSizeCodec<TFrom, TTo, TSize>;\nexport function isFixedSize<TSize extends number>(\n    codec: { fixedSize: TSize } | { maxSize?: number },\n): codec is { fixedSize: TSize };\nexport function isFixedSize(codec: { fixedSize: number } | { maxSize?: number }): codec is { fixedSize: number } {\n    return 'fixedSize' in codec && typeof codec.fixedSize === 'number';\n}\n\nexport function assertIsFixedSize<TFrom, TSize extends number>(\n    encoder: FixedSizeEncoder<TFrom, TSize> | VariableSizeEncoder<TFrom>,\n    message?: string,\n): asserts encoder is FixedSizeEncoder<TFrom, TSize>;\nexport function assertIsFixedSize<TTo, TSize extends number>(\n    decoder: FixedSizeDecoder<TTo, TSize> | VariableSizeDecoder<TTo>,\n    message?: string,\n): asserts decoder is FixedSizeDecoder<TTo, TSize>;\nexport function assertIsFixedSize<TFrom, TTo extends TFrom, TSize extends number>(\n    codec: FixedSizeCodec<TFrom, TTo, TSize> | VariableSizeCodec<TFrom, TTo>,\n    message?: string,\n): asserts codec is FixedSizeCodec<TFrom, TTo, TSize>;\nexport function assertIsFixedSize<TSize extends number>(\n    codec: { fixedSize: TSize } | { maxSize?: number },\n    message?: string,\n): asserts codec is { fixedSize: TSize };\nexport function assertIsFixedSize(\n    codec: { fixedSize: number } | { maxSize?: number },\n    message?: string,\n): asserts codec is { fixedSize: number } {\n    if (!isFixedSize(codec)) {\n        // TODO: Coded error.\n        throw new Error(message ?? 'Expected a fixed-size codec, got a variable-size one.');\n    }\n}\n\nexport function isVariableSize<TFrom>(encoder: Encoder<TFrom>): encoder is VariableSizeEncoder<TFrom>;\nexport function isVariableSize<TTo>(decoder: Decoder<TTo>): decoder is VariableSizeDecoder<TTo>;\nexport function isVariableSize<TFrom, TTo extends TFrom>(\n    codec: Codec<TFrom, TTo>,\n): codec is VariableSizeCodec<TFrom, TTo>;\nexport function isVariableSize(codec: { fixedSize: number } | { maxSize?: number }): codec is { maxSize?: number };\nexport function isVariableSize(codec: { fixedSize: number } | { maxSize?: number }): codec is { maxSize?: number } {\n    return !isFixedSize(codec);\n}\n\nexport function assertIsVariableSize<T>(\n    encoder: Encoder<T>,\n    message?: string,\n): asserts encoder is VariableSizeEncoder<T>;\nexport function assertIsVariableSize<T>(\n    decoder: Decoder<T>,\n    message?: string,\n): asserts decoder is VariableSizeDecoder<T>;\nexport function assertIsVariableSize<TFrom, TTo extends TFrom>(\n    codec: Codec<TFrom, TTo>,\n    message?: string,\n): asserts codec is VariableSizeCodec<TFrom, TTo>;\nexport function assertIsVariableSize(\n    codec: { fixedSize: number } | { maxSize?: number },\n    message?: string,\n): asserts codec is { maxSize?: number };\nexport function assertIsVariableSize(\n    codec: { fixedSize: number } | { maxSize?: number },\n    message?: string,\n): asserts codec is { maxSize?: number } {\n    if (!isVariableSize(codec)) {\n        // TODO: Coded error.\n        throw new Error(message ?? 'Expected a variable-size codec, got a fixed-size one.');\n    }\n}\n", "import {\n    Codec,\n    Decoder,\n    Encoder,\n    FixedSizeCodec,\n    FixedSizeDecoder,\n    FixedSizeEncoder,\n    isFixedSize,\n    VariableSizeCodec,\n    VariableSizeDecoder,\n    VariableSizeEncoder,\n} from './codec';\n\n/**\n * Combines an encoder and a decoder into a codec.\n * The encoder and decoder must have the same fixed size, max size and description.\n * If a description is provided, it will override the encoder and decoder descriptions.\n */\nexport function combineCodec<TFrom, TTo extends TFrom, TSize extends number>(\n    encoder: FixedSizeEncoder<TFrom, TSize>,\n    decoder: FixedSizeDecoder<TTo, TSize>,\n): FixedSizeCodec<TFrom, TTo, TSize>;\nexport function combineCodec<TFrom, TTo extends TFrom>(\n    encoder: VariableSizeEncoder<TFrom>,\n    decoder: VariableSizeDecoder<TTo>,\n): VariableSizeCodec<TFrom, TTo>;\nexport function combineCodec<TFrom, TTo extends TFrom>(\n    encoder: Encoder<TFrom>,\n    decoder: Decoder<TTo>,\n): Codec<TFrom, TTo>;\nexport function combineCodec<TFrom, TTo extends TFrom>(\n    encoder: Encoder<TFrom>,\n    decoder: Decoder<TTo>,\n): Codec<TFrom, TTo> {\n    if (isFixedSize(encoder) !== isFixedSize(decoder)) {\n        // TODO: Coded error.\n        throw new Error(`Encoder and decoder must either both be fixed-size or variable-size.`);\n    }\n\n    if (isFixedSize(encoder) && isFixedSize(decoder) && encoder.fixedSize !== decoder.fixedSize) {\n        // TODO: Coded error.\n        throw new Error(\n            `Encoder and decoder must have the same fixed size, got [${encoder.fixedSize}] and [${decoder.fixedSize}].`,\n        );\n    }\n\n    if (!isFixedSize(encoder) && !isFixedSize(decoder) && encoder.maxSize !== decoder.maxSize) {\n        // TODO: Coded error.\n        throw new Error(\n            `Encoder and decoder must have the same max size, got [${encoder.maxSize}] and [${decoder.maxSize}].`,\n        );\n    }\n\n    return {\n        ...decoder,\n        ...encoder,\n        decode: decoder.decode,\n        encode: encoder.encode,\n        read: decoder.read,\n        write: encoder.write,\n    };\n}\n", "import {\n    assertByteArrayHasEnoughBytesForCodec,\n    assertByteArrayIsNotEmptyForCodec,\n    createDecoder,\n    createEncoder,\n    FixedSizeDecoder,\n    FixedSizeEncoder,\n    Offset,\n} from '@solana/codecs-core';\n\nimport { assertNumberIsBetweenForCodec } from './assertions';\nimport { Endian, NumberCodecConfig } from './common';\n\ntype NumberFactorySharedInput<TSize extends number> = {\n    name: string;\n    size: TSize;\n    config?: NumberCodecConfig;\n};\n\ntype NumberFactoryEncoderInput<TFrom, TSize extends number> = NumberFactorySharedInput<TSize> & {\n    range?: [number | bigint, number | bigint];\n    set: (view: DataView, value: TFrom, littleEndian?: boolean) => void;\n};\n\ntype NumberFactoryDecoderInput<TTo, TSize extends number> = NumberFactorySharedInput<TSize> & {\n    get: (view: DataView, littleEndian?: boolean) => TTo;\n};\n\nfunction isLittleEndian(config?: NumberCodecConfig): boolean {\n    return config?.endian === Endian.BIG ? false : true;\n}\n\nexport function numberEncoderFactory<TFrom extends number | bigint, TSize extends number>(\n    input: NumberFactoryEncoderInput<TFrom, TSize>,\n): FixedSizeEncoder<TFrom, TSize> {\n    return createEncoder({\n        fixedSize: input.size,\n        write(value: TFrom, bytes: Uint8Array, offset: Offset): Offset {\n            if (input.range) {\n                assertNumberIsBetweenForCodec(input.name, input.range[0], input.range[1], value);\n            }\n            const arrayBuffer = new ArrayBuffer(input.size);\n            input.set(new DataView(arrayBuffer), value, isLittleEndian(input.config));\n            bytes.set(new Uint8Array(arrayBuffer), offset);\n            return offset + input.size;\n        },\n    });\n}\n\nexport function numberDecoderFactory<TTo extends number | bigint, TSize extends number>(\n    input: NumberFactoryDecoderInput<TTo, TSize>,\n): FixedSizeDecoder<TTo, TSize> {\n    return createDecoder({\n        fixedSize: input.size,\n        read(bytes, offset = 0): [TTo, number] {\n            assertByteArrayIsNotEmptyForCodec(input.name, bytes, offset);\n            assertByteArrayHasEnoughBytesForCodec(input.name, input.size, bytes, offset);\n            const view = new DataView(toArrayBuffer(bytes, offset, input.size));\n            return [input.get(view, isLittleEndian(input.config)), offset + input.size];\n        },\n    });\n}\n\n/**\n * Helper function to ensure that the ArrayBuffer is converted properly from a Uint8Array\n * Source: https://stackoverflow.com/questions/37228285/uint8array-to-arraybuffer\n */\nfunction toArrayBuffer(bytes: Uint8Array, offset?: number, length?: number): ArrayBuffer {\n    const bytesOffset = bytes.byteOffset + (offset ?? 0);\n    const bytesLength = length ?? bytes.byteLength;\n    return bytes.buffer.slice(bytesOffset, bytesOffset + bytesLength);\n}\n", "import { combineCodec, FixedSizeCodec, FixedSizeDecoder, FixedSizeEncoder } from '@solana/codecs-core';\n\nimport { NumberCodecConfig } from './common';\nimport { numberDecoderFactory, numberEncoderFactory } from './utils';\n\nexport const getF32Encoder = (config: NumberCodecConfig = {}): FixedSizeEncoder<number, 4> =>\n    numberEncoderFactory({\n        config,\n        name: 'f32',\n        set: (view, value, le) => view.setFloat32(0, value, le),\n        size: 4,\n    });\n\nexport const getF32Decoder = (config: NumberCodecConfig = {}): FixedSizeDecoder<number, 4> =>\n    numberDecoderFactory({\n        config,\n        get: (view, le) => view.getFloat32(0, le),\n        name: 'f32',\n        size: 4,\n    });\n\nexport const getF32Codec = (config: NumberCodecConfig = {}): FixedSizeCodec<number, number, 4> =>\n    combineCodec(getF32Encoder(config), getF32Decoder(config));\n", "import { combineCodec, FixedSizeCodec, FixedSizeDecoder, FixedSizeEncoder } from '@solana/codecs-core';\n\nimport { NumberCodecConfig } from './common';\nimport { numberDecoderFactory, numberEncoderFactory } from './utils';\n\nexport const getF64Encoder = (config: NumberCodecConfig = {}): FixedSizeEncoder<number, 8> =>\n    numberEncoderFactory({\n        config,\n        name: 'f64',\n        set: (view, value, le) => view.setFloat64(0, value, le),\n        size: 8,\n    });\n\nexport const getF64Decoder = (config: NumberCodecConfig = {}): FixedSizeDecoder<number, 8> =>\n    numberDecoderFactory({\n        config,\n        get: (view, le) => view.getFloat64(0, le),\n        name: 'f64',\n        size: 8,\n    });\n\nexport const getF64Codec = (config: NumberCodecConfig = {}): FixedSizeCodec<number, number, 8> =>\n    combineCodec(getF64Encoder(config), getF64Decoder(config));\n", "import { combineCodec, FixedSizeCodec, FixedSizeDecoder, FixedSizeEncoder } from '@solana/codecs-core';\n\nimport { NumberCodecConfig } from './common';\nimport { numberDecoderFactory, numberEncoderFactory } from './utils';\n\nexport const getI128Encoder = (config: NumberCodecConfig = {}): FixedSizeEncoder<number | bigint, 16> =>\n    numberEncoderFactory({\n        config,\n        name: 'i128',\n        range: [-BigInt('0x7fffffffffffffffffffffffffffffff') - 1n, BigInt('0x7fffffffffffffffffffffffffffffff')],\n        set: (view, value, le) => {\n            const leftOffset = le ? 8 : 0;\n            const rightOffset = le ? 0 : 8;\n            const rightMask = 0xffffffffffffffffn;\n            view.setBigInt64(leftOffset, BigInt(value) >> 64n, le);\n            view.setBigUint64(rightOffset, BigInt(value) & rightMask, le);\n        },\n        size: 16,\n    });\n\nexport const getI128Decoder = (config: NumberCodecConfig = {}): FixedSizeDecoder<bigint, 16> =>\n    numberDecoderFactory({\n        config,\n        get: (view, le) => {\n            const leftOffset = le ? 8 : 0;\n            const rightOffset = le ? 0 : 8;\n            const left = view.getBigInt64(leftOffset, le);\n            const right = view.getBigUint64(rightOffset, le);\n            return (left << 64n) + right;\n        },\n        name: 'i128',\n        size: 16,\n    });\n\nexport const getI128Codec = (config: NumberCodecConfig = {}): FixedSizeCodec<number | bigint, bigint, 16> =>\n    combineCodec(getI128Encoder(config), getI128Decoder(config));\n", "import { combineCodec, FixedSizeCodec, FixedSizeDecoder, FixedSizeEncoder } from '@solana/codecs-core';\n\nimport { NumberCodecConfig } from './common';\nimport { numberDecoderFactory, numberEncoderFactory } from './utils';\n\nexport const getI16Encoder = (config: NumberCodecConfig = {}): FixedSizeEncoder<number, 2> =>\n    numberEncoderFactory({\n        config,\n        name: 'i16',\n        range: [-Number('0x7fff') - 1, Number('0x7fff')],\n        set: (view, value, le) => view.setInt16(0, value, le),\n        size: 2,\n    });\n\nexport const getI16Decoder = (config: NumberCodecConfig = {}): FixedSizeDecoder<number, 2> =>\n    numberDecoderFactory({\n        config,\n        get: (view, le) => view.getInt16(0, le),\n        name: 'i16',\n        size: 2,\n    });\n\nexport const getI16Codec = (config: NumberCodecConfig = {}): FixedSizeCodec<number, number, 2> =>\n    combineCodec(getI16Encoder(config), getI16Decoder(config));\n", "import { combineCodec, FixedSizeCodec, FixedSizeDecoder, FixedSizeEncoder } from '@solana/codecs-core';\n\nimport { NumberCodecConfig } from './common';\nimport { numberDecoderFactory, numberEncoderFactory } from './utils';\n\nexport const getI32Encoder = (config: NumberCodecConfig = {}): FixedSizeEncoder<number, 4> =>\n    numberEncoderFactory({\n        config,\n        name: 'i32',\n        range: [-Number('0x7fffffff') - 1, Number('0x7fffffff')],\n        set: (view, value, le) => view.setInt32(0, value, le),\n        size: 4,\n    });\n\nexport const getI32Decoder = (config: NumberCodecConfig = {}): FixedSizeDecoder<number, 4> =>\n    numberDecoderFactory({\n        config,\n        get: (view, le) => view.getInt32(0, le),\n        name: 'i32',\n        size: 4,\n    });\n\nexport const getI32Codec = (config: NumberCodecConfig = {}): FixedSizeCodec<number, number, 4> =>\n    combineCodec(getI32Encoder(config), getI32Decoder(config));\n", "import { combineCodec, FixedSizeCodec, FixedSizeDecoder, FixedSizeEncoder } from '@solana/codecs-core';\n\nimport { NumberCodecConfig } from './common';\nimport { numberDecoderFactory, numberEncoderFactory } from './utils';\n\nexport const getI64Encoder = (config: NumberCodecConfig = {}): FixedSizeEncoder<number | bigint, 8> =>\n    numberEncoderFactory({\n        config,\n        name: 'i64',\n        range: [-BigInt('0x7fffffffffffffff') - 1n, BigInt('0x7fffffffffffffff')],\n        set: (view, value, le) => view.setBigInt64(0, BigInt(value), le),\n        size: 8,\n    });\n\nexport const getI64Decoder = (config: NumberCodecConfig = {}): FixedSizeDecoder<bigint, 8> =>\n    numberDecoderFactory({\n        config,\n        get: (view, le) => view.getBigInt64(0, le),\n        name: 'i64',\n        size: 8,\n    });\n\nexport const getI64Codec = (config: NumberCodecConfig = {}): FixedSizeCodec<number | bigint, bigint, 8> =>\n    combineCodec(getI64Encoder(config), getI64Decoder(config));\n", "import { combineCodec, FixedSizeCodec, FixedSizeDecoder, FixedSizeEncoder } from '@solana/codecs-core';\n\nimport { numberDecoderFactory, numberEncoderFactory } from './utils';\n\nexport const getI8Encoder = (): FixedSizeEncoder<number, 1> =>\n    numberEncoderFactory({\n        name: 'i8',\n        range: [-Number('0x7f') - 1, Number('0x7f')],\n        set: (view, value) => view.setInt8(0, value),\n        size: 1,\n    });\n\nexport const getI8Decoder = (): FixedSizeDecoder<number, 1> =>\n    numberDecoderFactory({\n        get: view => view.getInt8(0),\n        name: 'i8',\n        size: 1,\n    });\n\nexport const getI8Codec = (): FixedSizeCodec<number, number, 1> => combineCodec(getI8Encoder(), getI8Decoder());\n", "import {\n    combineCodec,\n    createDecoder,\n    createEncoder,\n    Offset,\n    VariableSizeCodec,\n    VariableSizeDecoder,\n    VariableSizeEncoder,\n} from '@solana/codecs-core';\n\nimport { assertNumberIsBetweenForCodec } from './assertions';\n\n/**\n * Encodes short u16 numbers.\n * @see {@link getShortU16Codec} for a more detailed description.\n */\nexport const getShortU16Encoder = (): VariableSizeEncoder<number> =>\n    createEncoder({\n        getSizeFromValue: (value: number): number => {\n            if (value <= 0b01111111) return 1;\n            if (value <= 0b0011111111111111) return 2;\n            return 3;\n        },\n        maxSize: 3,\n        write: (value: number, bytes: Uint8Array, offset: Offset): Offset => {\n            assertNumberIsBetweenForCodec('shortU16', 0, 65535, value);\n            const shortU16Bytes = [0];\n            for (let ii = 0; ; ii += 1) {\n                // Shift the bits of the value over such that the next 7 bits are at the right edge.\n                const alignedValue = value >> (ii * 7);\n                if (alignedValue === 0) {\n                    // No more bits to consume.\n                    break;\n                }\n                // Extract those 7 bits using a mask.\n                const nextSevenBits = 0b1111111 & alignedValue;\n                shortU16Bytes[ii] = nextSevenBits;\n                if (ii > 0) {\n                    // Set the continuation bit of the previous slice.\n                    shortU16Bytes[ii - 1] |= 0b10000000;\n                }\n            }\n            bytes.set(shortU16Bytes, offset);\n            return offset + shortU16Bytes.length;\n        },\n    });\n\n/**\n * Decodes short u16 numbers.\n * @see {@link getShortU16Codec} for a more detailed description.\n */\nexport const getShortU16Decoder = (): VariableSizeDecoder<number> =>\n    createDecoder({\n        maxSize: 3,\n        read: (bytes: Uint8Array, offset): [number, Offset] => {\n            let value = 0;\n            let byteCount = 0;\n            while (++byteCount) {\n                const byteIndex = byteCount - 1;\n                const currentByte = bytes[offset + byteIndex];\n                const nextSevenBits = 0b1111111 & currentByte;\n                // Insert the next group of seven bits into the correct slot of the output value.\n                value |= nextSevenBits << (byteIndex * 7);\n                if ((currentByte & 0b10000000) === 0) {\n                    // This byte does not have its continuation bit set. We're done.\n                    break;\n                }\n            }\n            return [value, offset + byteCount];\n        },\n    });\n\n/**\n * Encodes and decodes short u16 numbers.\n *\n * Short u16 numbers are the same as u16, but serialized with 1 to 3 bytes.\n * If the value is above 0x7f, the top bit is set and the remaining\n * value is stored in the next bytes. Each byte follows the same\n * pattern until the 3rd byte. The 3rd byte, if needed, uses\n * all 8 bits to store the last byte of the original value.\n */\nexport const getShortU16Codec = (): VariableSizeCodec<number> =>\n    combineCodec(getShortU16Encoder(), getShortU16Decoder());\n", "import { combineCodec, FixedSizeCodec, FixedSizeDecoder, FixedSizeEncoder } from '@solana/codecs-core';\n\nimport { NumberCodecConfig } from './common';\nimport { numberDecoderFactory, numberEncoderFactory } from './utils';\n\nexport const getU128Encoder = (config: NumberCodecConfig = {}): FixedSizeEncoder<number | bigint, 16> =>\n    numberEncoderFactory({\n        config,\n        name: 'u128',\n        range: [0, BigInt('0xffffffffffffffffffffffffffffffff')],\n        set: (view, value, le) => {\n            const leftOffset = le ? 8 : 0;\n            const rightOffset = le ? 0 : 8;\n            const rightMask = 0xffffffffffffffffn;\n            view.setBigUint64(leftOffset, BigInt(value) >> 64n, le);\n            view.setBigUint64(rightOffset, BigInt(value) & rightMask, le);\n        },\n        size: 16,\n    });\n\nexport const getU128Decoder = (config: NumberCodecConfig = {}): FixedSizeDecoder<bigint, 16> =>\n    numberDecoderFactory({\n        config,\n        get: (view, le) => {\n            const leftOffset = le ? 8 : 0;\n            const rightOffset = le ? 0 : 8;\n            const left = view.getBigUint64(leftOffset, le);\n            const right = view.getBigUint64(rightOffset, le);\n            return (left << 64n) + right;\n        },\n        name: 'u128',\n        size: 16,\n    });\n\nexport const getU128Codec = (config: NumberCodecConfig = {}): FixedSizeCodec<number | bigint, bigint, 16> =>\n    combineCodec(getU128Encoder(config), getU128Decoder(config));\n", "import { combineCodec, FixedSizeCodec, FixedSizeDecoder, FixedSizeEncoder } from '@solana/codecs-core';\n\nimport { NumberCodecConfig } from './common';\nimport { numberDecoderFactory, numberEncoderFactory } from './utils';\n\nexport const getU16Encoder = (config: NumberCodecConfig = {}): FixedSizeEncoder<number, 2> =>\n    numberEncoderFactory({\n        config,\n        name: 'u16',\n        range: [0, Number('0xffff')],\n        set: (view, value, le) => view.setUint16(0, value, le),\n        size: 2,\n    });\n\nexport const getU16Decoder = (config: NumberCodecConfig = {}): FixedSizeDecoder<number, 2> =>\n    numberDecoderFactory({\n        config,\n        get: (view, le) => view.getUint16(0, le),\n        name: 'u16',\n        size: 2,\n    });\n\nexport const getU16Codec = (config: NumberCodecConfig = {}): FixedSizeCodec<number, number, 2> =>\n    combineCodec(getU16Encoder(config), getU16Decoder(config));\n", "import { combineCodec, FixedSizeCodec, FixedSizeDecoder, FixedSizeEncoder } from '@solana/codecs-core';\n\nimport { NumberCodecConfig } from './common';\nimport { numberDecoderFactory, numberEncoderFactory } from './utils';\n\nexport const getU32Encoder = (config: NumberCodecConfig = {}): FixedSizeEncoder<number, 4> =>\n    numberEncoderFactory({\n        config,\n        name: 'u32',\n        range: [0, Number('0xffffffff')],\n        set: (view, value, le) => view.setUint32(0, value, le),\n        size: 4,\n    });\n\nexport const getU32Decoder = (config: NumberCodecConfig = {}): FixedSizeDecoder<number, 4> =>\n    numberDecoderFactory({\n        config,\n        get: (view, le) => view.getUint32(0, le),\n        name: 'u32',\n        size: 4,\n    });\n\nexport const getU32Codec = (config: NumberCodecConfig = {}): FixedSizeCodec<number, number, 4> =>\n    combineCodec(getU32Encoder(config), getU32Decoder(config));\n", "import { combineCodec, FixedSizeCodec, FixedSizeDecoder, FixedSizeEncoder } from '@solana/codecs-core';\n\nimport { NumberCodecConfig } from './common';\nimport { numberDecoderFactory, numberEncoderFactory } from './utils';\n\nexport const getU64Encoder = (config: NumberCodecConfig = {}): FixedSizeEncoder<number | bigint, 8> =>\n    numberEncoderFactory({\n        config,\n        name: 'u64',\n        range: [0, BigInt('0xffffffffffffffff')],\n        set: (view, value, le) => view.setBigUint64(0, BigInt(value), le),\n        size: 8,\n    });\n\nexport const getU64Decoder = (config: NumberCodecConfig = {}): FixedSizeDecoder<bigint, 8> =>\n    numberDecoderFactory({\n        config,\n        get: (view, le) => view.getBigUint64(0, le),\n        name: 'u64',\n        size: 8,\n    });\n\nexport const getU64Codec = (config: NumberCodecConfig = {}): FixedSizeCodec<number | bigint, bigint, 8> =>\n    combineCodec(getU64Encoder(config), getU64Decoder(config));\n", "import { combineCodec, FixedSizeCodec, FixedSizeDecoder, FixedSizeEncoder } from '@solana/codecs-core';\n\nimport { numberDecoderFactory, numberEncoderFactory } from './utils';\n\nexport const getU8Encoder = (): FixedSizeEncoder<number, 1> =>\n    numberEncoderFactory({\n        name: 'u8',\n        range: [0, Number('0xff')],\n        set: (view, value) => view.setUint8(0, value),\n        size: 1,\n    });\n\nexport const getU8Decoder = (): FixedSizeDecoder<number, 1> =>\n    numberDecoderFactory({\n        get: view => view.getUint8(0),\n        name: 'u8',\n        size: 1,\n    });\n\nexport const getU8Codec = (): FixedSizeCodec<number, number, 1> => combineCodec(getU8Encoder(), getU8Decoder());\n"]}