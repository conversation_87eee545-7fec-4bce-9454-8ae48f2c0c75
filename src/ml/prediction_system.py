"""
NEXUS Real-Time Prediction System
Adapted from rife-ai backend for NEXUS trading system
"""
import asyncio
import logging
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
import numpy as np
from dataclasses import dataclass

from .model_architecture import create_trading_model, create_lightweight_model, ModelConfig
from ..data.models import SignalModel, MarketDataModel
from ..data.phase1_data_pipeline_unification import UnifiedDataService

@dataclass
class PredictionResult:
    """Structured prediction result"""
    timestamp: datetime
    token: str
    direction: str  # 'BUY', 'HOLD', 'SELL'
    confidence: float
    suggested_size: float
    market_price: float
    features_used: Dict[str, Any]
    model_version: str

class RealTimePredictionSystem:
    """
    Real-time ML prediction system for NEXUS trading
    Integrates with ALPHA agent for enhanced signal generation
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = ModelConfig.get_config(config)
        self.model = None
        self.data_service = None
        self.sequence_length = self.config['sequence_length']
        self.prediction_buffer: Dict[str, List[Dict]] = {}
        self.is_initialized = False
        self.model_version = "nexus_v1.0"
        
        # Feature extraction configuration
        self.feature_extractors = {
            'price': self._extract_price_features,
            'volume': self._extract_volume_features,
            'technical': self._extract_technical_features,
            'orderbook': self._extract_orderbook_features
        }
        
        logging.info("Initialized NEXUS RealTimePredictionSystem")
        
    async def initialize(self, data_service: UnifiedDataService):
        """Initialize the prediction system"""
        try:
            self.data_service = data_service
            
            # Create appropriate model based on configuration
            if self.config.get('model_type') == 'lightweight':
                self.model = create_lightweight_model(self.config)
            else:
                self.model = create_trading_model(self.config)
                
            if self.model is None:
                logging.warning("ML model not available. Falling back to rule-based predictions.")
                
            # Load pre-trained weights if available
            await self._load_latest_model()
            
            self.is_initialized = True
            logging.info("NEXUS prediction system initialized successfully")
            
        except Exception as e:
            logging.error(f"Failed to initialize prediction system: {e}")
            self.is_initialized = False
            
    async def get_prediction(self, token: str) -> Optional[PredictionResult]:
        """Generate real-time trading prediction for a specific token"""
        if not self.is_initialized:
            logging.warning("Prediction system not initialized")
            return None
            
        try:
            # Get latest market data
            latest_data = await self._get_latest_market_data(token)
            if not latest_data:
                return None
                
            # Update prediction buffer for this token
            if token not in self.prediction_buffer:
                self.prediction_buffer[token] = []
                
            self.prediction_buffer[token].append(latest_data)
            
            # Maintain buffer size
            if len(self.prediction_buffer[token]) > self.sequence_length:
                self.prediction_buffer[token].pop(0)
                
            # Generate prediction if we have enough data
            if len(self.prediction_buffer[token]) >= self.sequence_length:
                return await self._generate_ml_prediction(token, latest_data)
            else:
                # Use rule-based prediction for insufficient data
                return self._generate_rule_based_prediction(token, latest_data)
                
        except Exception as e:
            logging.error(f"Error generating prediction for {token}: {e}")
            return None
            
    async def _get_latest_market_data(self, token: str) -> Optional[Dict[str, Any]]:
        """Get latest market data for a token"""
        try:
            if not self.data_service:
                return None
                
            # Get price data
            price_data = await self.data_service.get_price(token)
            if not price_data:
                return None
                
            # Get additional market data
            market_data = {
                'timestamp': datetime.now(),
                'token': token,
                'price': price_data.get('price', 0),
                'volume_24h': price_data.get('volume_24h', 0),
                'price_change_24h': price_data.get('price_change_24h', 0),
                'market_cap': price_data.get('market_cap', 0)
            }
            
            # Add technical indicators if available
            technical_data = await self._get_technical_indicators(token)
            if technical_data:
                market_data.update(technical_data)
                
            return market_data
            
        except Exception as e:
            logging.error(f"Error getting market data for {token}: {e}")
            return None
            
    async def _generate_ml_prediction(self, token: str, latest_data: Dict) -> Optional[PredictionResult]:
        """Generate ML-based prediction"""
        if not self.model:
            return self._generate_rule_based_prediction(token, latest_data)
            
        try:
            # Prepare features
            features = self._prepare_features(self.prediction_buffer[token])
            if features is None:
                return None
                
            # Generate prediction
            prediction = self.model.predict(features, verbose=0)
            
            # Format prediction result
            return self._format_prediction(prediction, token, latest_data)
            
        except Exception as e:
            logging.error(f"Error in ML prediction for {token}: {e}")
            return self._generate_rule_based_prediction(token, latest_data)
            
    def _generate_rule_based_prediction(self, token: str, latest_data: Dict) -> PredictionResult:
        """Generate rule-based prediction as fallback"""
        try:
            price_change = latest_data.get('price_change_24h', 0)
            volume = latest_data.get('volume_24h', 0)
            
            # Simple rule-based logic
            if price_change > 5 and volume > 1000000:  # Strong upward momentum
                direction = 'BUY'
                confidence = min(0.8, abs(price_change) / 10)
                size = 0.3
            elif price_change < -5 and volume > 1000000:  # Strong downward momentum
                direction = 'SELL'
                confidence = min(0.8, abs(price_change) / 10)
                size = 0.3
            else:
                direction = 'HOLD'
                confidence = 0.5
                size = 0.0
                
            return PredictionResult(
                timestamp=datetime.now(),
                token=token,
                direction=direction,
                confidence=confidence,
                suggested_size=size,
                market_price=latest_data.get('price', 0),
                features_used={'rule_based': True, 'price_change': price_change, 'volume': volume},
                model_version=f"{self.model_version}_rule_based"
            )
            
        except Exception as e:
            logging.error(f"Error in rule-based prediction: {e}")
            return PredictionResult(
                timestamp=datetime.now(),
                token=token,
                direction='HOLD',
                confidence=0.0,
                suggested_size=0.0,
                market_price=latest_data.get('price', 0),
                features_used={'error': str(e)},
                model_version=f"{self.model_version}_error"
            )
            
    def _prepare_features(self, data: List[Dict]) -> Optional[np.ndarray]:
        """Prepare features for ML model prediction"""
        try:
            if len(data) < self.sequence_length:
                return None
                
            # Extract features for each data point
            feature_matrix = []
            for data_point in data[-self.sequence_length:]:
                features = []
                
                # Extract different types of features
                for feature_type, extractor in self.feature_extractors.items():
                    if feature_type != 'orderbook':  # Skip orderbook for now
                        extracted = extractor(data_point)
                        features.extend(extracted)
                        
                feature_matrix.append(features)
                
            # Convert to numpy array and reshape for model
            features_array = np.array(feature_matrix, dtype=np.float32)
            
            # Handle NaN values
            features_array = np.nan_to_num(features_array, nan=0.0, posinf=1.0, neginf=-1.0)
            
            # Reshape for model input
            market_features = features_array.reshape(1, self.sequence_length, -1)
            
            # Create dummy orderbook data for now
            orderbook_features = np.zeros((1, self.config.get('orderbook_levels', 20), 2), dtype=np.float32)
            
            return [market_features, orderbook_features]
            
        except Exception as e:
            logging.error(f"Error preparing features: {e}")
            return None

    def _extract_price_features(self, data_point: Dict) -> List[float]:
        """Extract price-related features"""
        return [
            data_point.get('price', 0),
            data_point.get('price_change_24h', 0),
            data_point.get('market_cap', 0)
        ]

    def _extract_volume_features(self, data_point: Dict) -> List[float]:
        """Extract volume-related features"""
        return [
            data_point.get('volume_24h', 0),
            data_point.get('volume_change_24h', 0)
        ]

    def _extract_technical_features(self, data_point: Dict) -> List[float]:
        """Extract technical indicator features"""
        return [
            data_point.get('rsi', 50),
            data_point.get('macd', 0),
            data_point.get('bb_upper', 0),
            data_point.get('bb_lower', 0),
            data_point.get('sma_20', 0)
        ]

    def _extract_orderbook_features(self, data_point: Dict) -> List[float]:
        """Extract orderbook features (placeholder)"""
        return [0.0, 0.0]  # Placeholder for orderbook data

    def _format_prediction(self, prediction: Any, token: str, latest_data: Dict) -> PredictionResult:
        """Format the ML model prediction output"""
        try:
            if isinstance(prediction, list) and len(prediction) >= 2:
                direction_probs = prediction[0][0]
                confidence = float(prediction[1][0][0])
                size = float(prediction[2][0][0]) if len(prediction) > 2 else 0.3
            else:
                # Fallback for unexpected prediction format
                direction_probs = [0.33, 0.34, 0.33]  # Equal probabilities
                confidence = 0.5
                size = 0.2

            # Convert direction probabilities to decision
            direction_idx = np.argmax(direction_probs)
            directions = ['SELL', 'HOLD', 'BUY']
            direction = directions[direction_idx]

            # Adjust confidence based on prediction strength
            max_prob = float(np.max(direction_probs))
            adjusted_confidence = confidence * max_prob

            return PredictionResult(
                timestamp=datetime.now(),
                token=token,
                direction=direction,
                confidence=adjusted_confidence,
                suggested_size=size,
                market_price=latest_data.get('price', 0),
                features_used={
                    'ml_model': True,
                    'direction_probs': direction_probs.tolist(),
                    'raw_confidence': confidence
                },
                model_version=self.model_version
            )

        except Exception as e:
            logging.error(f"Error formatting prediction: {e}")
            return self._generate_rule_based_prediction(token, latest_data)

    async def _get_technical_indicators(self, token: str) -> Optional[Dict[str, float]]:
        """Get technical indicators for a token"""
        try:
            # This would integrate with our technical analysis module
            # For now, return placeholder values
            return {
                'rsi': 50.0,
                'macd': 0.0,
                'bb_upper': 0.0,
                'bb_lower': 0.0,
                'sma_20': 0.0
            }
        except Exception as e:
            logging.error(f"Error getting technical indicators: {e}")
            return None

    async def _load_latest_model(self):
        """Load the latest trained model weights"""
        try:
            if self.model is None:
                return

            # Placeholder for model loading logic
            # In production, this would load from a model registry or file system
            logging.info("Model weights loading not implemented yet")

        except Exception as e:
            logging.error(f"Error loading model weights: {e}")

    def to_signal_model(self, prediction: PredictionResult) -> Optional[SignalModel]:
        """Convert prediction result to SignalModel for ALPHA agent"""
        try:
            if prediction.direction == 'HOLD' or prediction.confidence < 0.3:
                return None

            return SignalModel(
                token=prediction.token,
                entry_price=prediction.market_price,
                direction=prediction.direction,
                confidence=prediction.confidence,
                timestamp=prediction.timestamp,
                source=f"ml_prediction_{self.model_version}"
            )

        except Exception as e:
            logging.error(f"Error converting to SignalModel: {e}")
            return None

    def get_system_health(self) -> Dict[str, Any]:
        """Get system health information"""
        return {
            'initialized': self.is_initialized,
            'model_available': self.model is not None,
            'data_service_available': self.data_service is not None,
            'active_tokens': len(self.prediction_buffer),
            'model_version': self.model_version,
            'config': self.config
        }
