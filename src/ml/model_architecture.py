"""
NEXUS ML Model Architecture
Adapted from rife-ai backend for NEXUS trading system
"""
import numpy as np
from typing import Dict, Optional, Any
import logging

try:
    import tensorflow as tf
    from tensorflow.keras import layers, Model
    TF_AVAILABLE = True
except ImportError:
    TF_AVAILABLE = False
    logging.warning("TensorFlow not available. ML features will be limited.")

def create_trading_model(config: Dict[str, Any]) -> Optional[Any]:
    """
    Create the deep learning model for trading decisions
    Adapted for NEXUS architecture
    """
    if not TF_AVAILABLE:
        logging.warning("TensorFlow not available. Cannot create ML model.")
        return None
        
    try:
        # Input layers for different data types
        market_input = layers.Input(
            shape=(config.get('sequence_length', 60), config.get('market_features', 10)),
            name='market_data'
        )
        orderbook_input = layers.Input(
            shape=(config.get('orderbook_levels', 20), 2),
            name='orderbook_data'
        )
        
        # Process market data with LSTM
        x1 = layers.LSTM(128, return_sequences=True, name='lstm_1')(market_input)
        x1 = layers.LSTM(64, name='lstm_2')(x1)
        x1 = layers.Dropout(0.2)(x1)
        
        # Process orderbook data with Conv1D
        x2 = layers.Conv1D(64, 3, activation='relu', name='conv_1')(orderbook_input)
        x2 = layers.Conv1D(32, 3, activation='relu', name='conv_2')(x2)
        x2 = layers.GlobalAveragePooling1D()(x2)
        x2 = layers.Dropout(0.2)(x2)
        
        # Combine features
        combined = layers.concatenate([x1, x2], name='feature_fusion')
        
        # Decision layers
        x = layers.Dense(128, activation='relu', name='decision_1')(combined)
        x = layers.Dropout(0.3)(x)
        x = layers.Dense(64, activation='relu', name='decision_2')(x)
        x = layers.Dropout(0.2)(x)
        
        # Output layers for NEXUS signal generation
        direction = layers.Dense(3, activation='softmax', name='direction')(x)  # BUY, HOLD, SELL
        confidence = layers.Dense(1, activation='sigmoid', name='confidence')(x)
        position_size = layers.Dense(1, activation='sigmoid', name='position_size')(x)
        
        model = Model(
            inputs=[market_input, orderbook_input],
            outputs=[direction, confidence, position_size],
            name='nexus_trading_model'
        )
        
        # Compile with appropriate losses
        model.compile(
            optimizer=tf.keras.optimizers.Adam(learning_rate=config.get('learning_rate', 0.001)),
            loss={
                'direction': 'categorical_crossentropy',
                'confidence': 'binary_crossentropy',
                'position_size': 'mse'
            },
            loss_weights={
                'direction': 1.0,
                'confidence': 0.5,
                'position_size': 0.3
            },
            metrics={
                'direction': 'accuracy',
                'confidence': 'mae',
                'position_size': 'mae'
            }
        )
        
        logging.info(f"Created NEXUS trading model with {model.count_params()} parameters")
        return model
        
    except Exception as e:
        logging.error(f"Failed to create trading model: {e}")
        return None

def create_lightweight_model(config: Dict[str, Any]) -> Optional[Any]:
    """
    Create a lightweight model for environments with limited resources
    """
    if not TF_AVAILABLE:
        return None
        
    try:
        # Simplified architecture
        market_input = layers.Input(
            shape=(config.get('sequence_length', 30), config.get('market_features', 5)),
            name='market_data'
        )
        
        # Simple LSTM processing
        x = layers.LSTM(32, name='lstm_simple')(market_input)
        x = layers.Dropout(0.2)(x)
        
        # Decision layers
        x = layers.Dense(32, activation='relu', name='decision')(x)
        x = layers.Dropout(0.2)(x)
        
        # Output layers
        direction = layers.Dense(3, activation='softmax', name='direction')(x)
        confidence = layers.Dense(1, activation='sigmoid', name='confidence')(x)
        
        model = Model(
            inputs=market_input,
            outputs=[direction, confidence],
            name='nexus_lightweight_model'
        )
        
        model.compile(
            optimizer='adam',
            loss={
                'direction': 'categorical_crossentropy',
                'confidence': 'binary_crossentropy'
            }
        )
        
        logging.info(f"Created lightweight NEXUS model with {model.count_params()} parameters")
        return model
        
    except Exception as e:
        logging.error(f"Failed to create lightweight model: {e}")
        return None

class ModelConfig:
    """Configuration class for NEXUS ML models"""
    
    DEFAULT_CONFIG = {
        'sequence_length': 60,
        'market_features': 10,
        'orderbook_levels': 20,
        'learning_rate': 0.001,
        'batch_size': 32,
        'epochs': 100,
        'validation_split': 0.2,
        'early_stopping_patience': 10,
        'model_type': 'full'  # 'full' or 'lightweight'
    }
    
    @classmethod
    def get_config(cls, overrides: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Get model configuration with optional overrides"""
        config = cls.DEFAULT_CONFIG.copy()
        if overrides:
            config.update(overrides)
        return config
    
    @classmethod
    def validate_config(cls, config: Dict[str, Any]) -> bool:
        """Validate model configuration"""
        required_keys = ['sequence_length', 'market_features']
        return all(key in config for key in required_keys)
