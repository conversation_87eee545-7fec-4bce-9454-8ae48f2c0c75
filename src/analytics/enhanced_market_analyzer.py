"""
Enhanced Market Analyzer for NEXUS Trading System
Adapted from rife-ai backend for comprehensive market analysis
"""
import asyncio
import logging
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
import numpy as np
from datetime import datetime, timedelta

@dataclass
class AnalysisConfig:
    """Configuration for market analysis parameters"""
    window_sizes: List[int] = None
    volatility_periods: List[int] = None
    correlation_lookback: int = 100
    liquidity_threshold: float = 1000000.0
    volume_impact_threshold: float = 0.05
    price_impact_threshold: float = 0.02
    
    def __post_init__(self):
        if self.window_sizes is None:
            self.window_sizes = [5, 10, 20, 50, 100]
        if self.volatility_periods is None:
            self.volatility_periods = [5, 10, 20, 30]

@dataclass
class MarketAnalysisResult:
    """Structured result from market analysis"""
    timestamp: datetime
    market_id: str
    market_metrics: Dict[str, Any]
    technical_indicators: Dict[str, Any]
    liquidity_analysis: Dict[str, Any]
    volatility_metrics: Dict[str, Any]
    correlation_data: Dict[str, Any]
    market_impact: Dict[str, Any]
    risk_metrics: Dict[str, Any]
    overall_score: float
    recommendations: List[str]

class EnhancedMarketAnalyzer:
    """
    Enhanced market analyzer with comprehensive analysis capabilities
    Integrates with NEXUS data pipeline and agent system
    """
    
    def __init__(self, config: Optional[AnalysisConfig] = None):
        self.config = config or AnalysisConfig()
        self.price_history: Dict[str, List[float]] = {}
        self.volume_history: Dict[str, List[float]] = {}
        self.liquidity_history: Dict[str, List[float]] = {}
        self.analysis_cache: Dict[str, MarketAnalysisResult] = {}
        self.correlation_matrix: Dict[str, Dict[str, float]] = {}
        self.max_history_length = 1000  # Limit memory usage
        
        logging.info("Initialized Enhanced Market Analyzer")
        
    async def analyze_market_conditions(self, market_data: Dict[str, Any]) -> MarketAnalysisResult:
        """Perform comprehensive market analysis"""
        try:
            market_id = market_data.get('market_id') or market_data.get('token', 'unknown')
            self._update_market_history(market_id, market_data)
            
            # Perform all analysis components
            market_metrics = await self._calculate_market_metrics(market_id)
            technical_indicators = await self._calculate_technical_indicators(market_id)
            liquidity_analysis = await self._analyze_liquidity(market_id)
            volatility_metrics = self._calculate_volatility_metrics(market_id)
            correlation_data = await self._update_correlation_matrix(market_id)
            market_impact = self._estimate_market_impact(market_data)
            risk_metrics = self._calculate_risk_metrics(market_id)
            
            # Calculate overall score and recommendations
            overall_score = self._calculate_overall_score(
                market_metrics, technical_indicators, liquidity_analysis, 
                volatility_metrics, risk_metrics
            )
            recommendations = self._generate_recommendations(
                market_metrics, technical_indicators, risk_metrics, overall_score
            )
            
            analysis = MarketAnalysisResult(
                timestamp=datetime.utcnow(),
                market_id=market_id,
                market_metrics=market_metrics,
                technical_indicators=technical_indicators,
                liquidity_analysis=liquidity_analysis,
                volatility_metrics=volatility_metrics,
                correlation_data=correlation_data,
                market_impact=market_impact,
                risk_metrics=risk_metrics,
                overall_score=overall_score,
                recommendations=recommendations
            )
            
            self.analysis_cache[market_id] = analysis
            return analysis
            
        except Exception as e:
            logging.error(f"Error in market analysis for {market_data}: {e}")
            return self._create_error_result(market_data, str(e))
            
    def _update_market_history(self, market_id: str, market_data: Dict[str, Any]):
        """Update historical data for a market"""
        try:
            # Initialize history if not exists
            if market_id not in self.price_history:
                self.price_history[market_id] = []
                self.volume_history[market_id] = []
                self.liquidity_history[market_id] = []
            
            # Add new data points
            price = float(market_data.get('price', 0))
            volume = float(market_data.get('volume_24h', 0))
            liquidity = float(market_data.get('liquidity', volume))  # Use volume as proxy if no liquidity
            
            self.price_history[market_id].append(price)
            self.volume_history[market_id].append(volume)
            self.liquidity_history[market_id].append(liquidity)
            
            # Limit history length to prevent memory issues
            for history in [self.price_history[market_id], self.volume_history[market_id], self.liquidity_history[market_id]]:
                if len(history) > self.max_history_length:
                    history.pop(0)
                    
        except Exception as e:
            logging.error(f"Error updating market history for {market_id}: {e}")
            
    async def _calculate_market_metrics(self, market_id: str) -> Dict[str, Any]:
        """Calculate core market metrics"""
        try:
            if market_id not in self.price_history or len(self.price_history[market_id]) < 2:
                return self._get_default_metrics()
                
            price_data = np.array(self.price_history[market_id])
            volume_data = np.array(self.volume_history[market_id])
            
            return {
                'price_momentum': self._calculate_momentum(price_data),
                'volume_profile': self._analyze_volume_profile(volume_data),
                'price_levels': self._identify_price_levels(price_data),
                'market_efficiency': self._calculate_market_efficiency(price_data),
                'trend_strength': self._calculate_trend_strength(price_data),
                'price_velocity': self._calculate_price_velocity(price_data),
                'volume_trend': self._calculate_volume_trend(volume_data)
            }
            
        except Exception as e:
            logging.error(f"Error calculating market metrics for {market_id}: {e}")
            return self._get_default_metrics()
            
    async def _calculate_technical_indicators(self, market_id: str) -> Dict[str, Any]:
        """Calculate technical analysis indicators"""
        try:
            if market_id not in self.price_history or len(self.price_history[market_id]) < 20:
                return self._get_default_technical_indicators()
                
            price_data = np.array(self.price_history[market_id])
            
            return {
                'moving_averages': self._calculate_moving_averages(price_data),
                'rsi': self._calculate_rsi(price_data),
                'macd': self._calculate_macd(price_data),
                'bollinger_bands': self._calculate_bollinger_bands(price_data),
                'support_resistance': self._find_support_resistance(price_data),
                'momentum_indicators': self._calculate_momentum_indicators(price_data)
            }
            
        except Exception as e:
            logging.error(f"Error calculating technical indicators for {market_id}: {e}")
            return self._get_default_technical_indicators()
            
    async def _analyze_liquidity(self, market_id: str) -> Dict[str, Any]:
        """Analyze market liquidity conditions"""
        try:
            if market_id not in self.liquidity_history or len(self.liquidity_history[market_id]) < 5:
                return {'liquidity_score': 0.5, 'liquidity_trend': 'unknown', 'depth_analysis': {}}
                
            liquidity_data = np.array(self.liquidity_history[market_id])
            volume_data = np.array(self.volume_history[market_id])
            
            return {
                'liquidity_score': self._calculate_liquidity_score(liquidity_data),
                'liquidity_trend': self._analyze_liquidity_trend(liquidity_data),
                'depth_analysis': self._analyze_market_depth(liquidity_data, volume_data),
                'liquidity_risk': self._assess_liquidity_risk(liquidity_data)
            }
            
        except Exception as e:
            logging.error(f"Error analyzing liquidity for {market_id}: {e}")
            return {'liquidity_score': 0.5, 'liquidity_trend': 'unknown', 'depth_analysis': {}}
            
    def _calculate_volatility_metrics(self, market_id: str) -> Dict[str, Any]:
        """Calculate volatility metrics"""
        try:
            if market_id not in self.price_history or len(self.price_history[market_id]) < 10:
                return {'volatility': 0.0, 'volatility_trend': 'stable'}
                
            price_data = np.array(self.price_history[market_id])
            returns = np.diff(np.log(price_data + 1e-8))  # Add small value to avoid log(0)
            
            volatility_metrics = {}
            for period in self.config.volatility_periods:
                if len(returns) >= period:
                    period_returns = returns[-period:]
                    volatility_metrics[f'volatility_{period}d'] = np.std(period_returns) * np.sqrt(365)
                    
            return {
                'current_volatility': np.std(returns[-20:]) * np.sqrt(365) if len(returns) >= 20 else 0.0,
                'volatility_trend': self._analyze_volatility_trend(returns),
                'period_volatilities': volatility_metrics,
                'volatility_percentile': self._calculate_volatility_percentile(returns)
            }
            
        except Exception as e:
            logging.error(f"Error calculating volatility metrics for {market_id}: {e}")
            return {'volatility': 0.0, 'volatility_trend': 'stable'}
            
    async def _update_correlation_matrix(self, market_id: str) -> Dict[str, Any]:
        """Update correlation matrix with other markets"""
        try:
            # For now, return placeholder correlation data
            # In production, this would calculate correlations with other tracked markets
            return {
                'correlation_with_btc': 0.5,
                'correlation_with_eth': 0.4,
                'correlation_with_sol': 0.6,
                'market_beta': 1.2
            }
            
        except Exception as e:
            logging.error(f"Error updating correlation matrix for {market_id}: {e}")
            return {}
            
    def _estimate_market_impact(self, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """Estimate market impact of potential trades"""
        try:
            volume_24h = float(market_data.get('volume_24h', 0))
            market_cap = float(market_data.get('market_cap', 0))
            
            # Estimate impact for different trade sizes
            trade_sizes = [1000, 5000, 10000, 50000, 100000]  # USD values
            impact_estimates = {}
            
            for size in trade_sizes:
                if volume_24h > 0:
                    volume_ratio = size / volume_24h
                    # Simple impact model: impact increases with square root of volume ratio
                    estimated_impact = min(0.5, np.sqrt(volume_ratio) * 0.1)
                else:
                    estimated_impact = 0.5  # High impact for low volume
                    
                impact_estimates[f'impact_${size}'] = estimated_impact
                
            return {
                'impact_estimates': impact_estimates,
                'liquidity_rating': 'high' if volume_24h > 1000000 else 'medium' if volume_24h > 100000 else 'low',
                'recommended_max_size': volume_24h * 0.01 if volume_24h > 0 else 1000  # 1% of daily volume
            }
            
        except Exception as e:
            logging.error(f"Error estimating market impact: {e}")
            return {'impact_estimates': {}, 'liquidity_rating': 'unknown'}
            
    def _calculate_risk_metrics(self, market_id: str) -> Dict[str, Any]:
        """Calculate comprehensive risk metrics"""
        try:
            if market_id not in self.price_history or len(self.price_history[market_id]) < 20:
                return self._get_default_risk_metrics()
                
            price_data = np.array(self.price_history[market_id])
            volume_data = np.array(self.volume_history[market_id])
            returns = np.diff(np.log(price_data + 1e-8))
            
            return {
                'value_at_risk_95': self._calculate_var(returns, 0.95),
                'value_at_risk_99': self._calculate_var(returns, 0.99),
                'expected_shortfall': self._calculate_expected_shortfall(returns),
                'liquidity_risk': self._calculate_liquidity_risk(volume_data),
                'volatility_risk': self._calculate_volatility_risk(returns),
                'tail_risk': self._calculate_tail_risk(returns),
                'drawdown_risk': self._calculate_drawdown_risk(price_data)
            }
            
        except Exception as e:
            logging.error(f"Error calculating risk metrics for {market_id}: {e}")
            return self._get_default_risk_metrics()

    # Helper methods for calculations
    def _calculate_momentum(self, price_data: np.ndarray) -> Dict[str, float]:
        """Calculate price momentum indicators"""
        if len(price_data) < 10:
            return {'short_momentum': 0.0, 'medium_momentum': 0.0, 'long_momentum': 0.0}

        try:
            short_momentum = (price_data[-1] / price_data[-5] - 1) if len(price_data) >= 5 else 0.0
            medium_momentum = (price_data[-1] / price_data[-20] - 1) if len(price_data) >= 20 else 0.0
            long_momentum = (price_data[-1] / price_data[-50] - 1) if len(price_data) >= 50 else 0.0

            return {
                'short_momentum': float(short_momentum),
                'medium_momentum': float(medium_momentum),
                'long_momentum': float(long_momentum)
            }
        except Exception:
            return {'short_momentum': 0.0, 'medium_momentum': 0.0, 'long_momentum': 0.0}

    def _analyze_volume_profile(self, volume_data: np.ndarray) -> Dict[str, Any]:
        """Analyze volume profile and patterns"""
        if len(volume_data) < 5:
            return {'average_volume': 0.0, 'volume_trend': 'unknown'}

        try:
            recent_avg = np.mean(volume_data[-5:])
            historical_avg = np.mean(volume_data[:-5]) if len(volume_data) > 5 else recent_avg

            volume_ratio = recent_avg / historical_avg if historical_avg > 0 else 1.0

            if volume_ratio > 1.5:
                trend = 'increasing'
            elif volume_ratio < 0.7:
                trend = 'decreasing'
            else:
                trend = 'stable'

            return {
                'average_volume': float(np.mean(volume_data)),
                'recent_volume': float(recent_avg),
                'volume_trend': trend,
                'volume_ratio': float(volume_ratio)
            }
        except Exception:
            return {'average_volume': 0.0, 'volume_trend': 'unknown'}

    def _identify_price_levels(self, price_data: np.ndarray) -> Dict[str, float]:
        """Identify key price levels"""
        if len(price_data) < 20:
            current_price = price_data[-1] if len(price_data) > 0 else 0.0
            return {
                'current_price': float(current_price),
                'support': float(current_price * 0.95),
                'resistance': float(current_price * 1.05)
            }

        try:
            current_price = price_data[-1]
            recent_high = np.max(price_data[-20:])
            recent_low = np.min(price_data[-20:])

            return {
                'current_price': float(current_price),
                'recent_high': float(recent_high),
                'recent_low': float(recent_low),
                'support': float(recent_low),
                'resistance': float(recent_high)
            }
        except Exception:
            current_price = price_data[-1] if len(price_data) > 0 else 0.0
            return {
                'current_price': float(current_price),
                'support': float(current_price * 0.95),
                'resistance': float(current_price * 1.05)
            }

    def _calculate_market_efficiency(self, price_data: np.ndarray) -> float:
        """Calculate market efficiency score"""
        if len(price_data) < 10:
            return 0.5

        try:
            returns = np.diff(np.log(price_data + 1e-8))
            # Simple efficiency measure: inverse of autocorrelation
            if len(returns) > 1:
                autocorr = np.corrcoef(returns[:-1], returns[1:])[0, 1]
                efficiency = 1.0 - abs(autocorr) if not np.isnan(autocorr) else 0.5
            else:
                efficiency = 0.5

            return float(np.clip(efficiency, 0.0, 1.0))
        except Exception:
            return 0.5

    def _calculate_trend_strength(self, price_data: np.ndarray) -> float:
        """Calculate trend strength"""
        if len(price_data) < 20:
            return 0.0

        try:
            # Use linear regression slope as trend strength indicator
            x = np.arange(len(price_data))
            slope, _ = np.polyfit(x, price_data, 1)

            # Normalize by price level
            normalized_slope = slope / np.mean(price_data) if np.mean(price_data) > 0 else 0.0

            return float(np.clip(abs(normalized_slope) * 100, 0.0, 1.0))
        except Exception:
            return 0.0

    def _calculate_moving_averages(self, price_data: np.ndarray) -> Dict[str, float]:
        """Calculate moving averages"""
        mas = {}
        for window in self.config.window_sizes:
            if len(price_data) >= window:
                ma = np.mean(price_data[-window:])
                mas[f'ma_{window}'] = float(ma)
            else:
                mas[f'ma_{window}'] = float(price_data[-1]) if len(price_data) > 0 else 0.0

        return mas

    def _calculate_rsi(self, price_data: np.ndarray, period: int = 14) -> float:
        """Calculate RSI indicator"""
        if len(price_data) < period + 1:
            return 50.0

        try:
            deltas = np.diff(price_data)
            gains = np.where(deltas > 0, deltas, 0)
            losses = np.where(deltas < 0, -deltas, 0)

            avg_gain = np.mean(gains[-period:])
            avg_loss = np.mean(losses[-period:])

            if avg_loss == 0:
                return 100.0

            rs = avg_gain / avg_loss
            rsi = 100 - (100 / (1 + rs))

            return float(rsi)
        except Exception:
            return 50.0

    def _calculate_var(self, returns: np.ndarray, confidence: float) -> float:
        """Calculate Value at Risk"""
        if len(returns) < 10:
            return 0.0

        try:
            percentile = (1 - confidence) * 100
            var = np.percentile(returns, percentile)
            return float(abs(var))
        except Exception:
            return 0.0

    def _calculate_expected_shortfall(self, returns: np.ndarray, confidence: float = 0.95) -> float:
        """Calculate Expected Shortfall (Conditional VaR)"""
        if len(returns) < 10:
            return 0.0

        try:
            var_threshold = np.percentile(returns, (1 - confidence) * 100)
            tail_losses = returns[returns <= var_threshold]

            if len(tail_losses) > 0:
                es = np.mean(tail_losses)
                return float(abs(es))
            else:
                return 0.0
        except Exception:
            return 0.0

    def _calculate_overall_score(self, market_metrics: Dict, technical_indicators: Dict,
                               liquidity_analysis: Dict, volatility_metrics: Dict,
                               risk_metrics: Dict) -> float:
        """Calculate overall market attractiveness score"""
        try:
            # Weight different components
            weights = {
                'trend_strength': 0.25,
                'liquidity_score': 0.20,
                'volatility_score': 0.20,
                'technical_score': 0.20,
                'risk_score': 0.15
            }

            # Calculate component scores
            trend_score = market_metrics.get('trend_strength', 0.0)
            liquidity_score = liquidity_analysis.get('liquidity_score', 0.5)

            # Volatility score (moderate volatility is preferred)
            volatility = volatility_metrics.get('current_volatility', 0.0)
            volatility_score = 1.0 - min(1.0, volatility / 2.0)  # Penalize high volatility

            # Technical score based on RSI (prefer values between 30-70)
            rsi = technical_indicators.get('rsi', 50.0)
            if 30 <= rsi <= 70:
                technical_score = 1.0
            else:
                technical_score = max(0.0, 1.0 - abs(rsi - 50) / 50.0)

            # Risk score (lower risk is better)
            var_95 = risk_metrics.get('value_at_risk_95', 0.0)
            risk_score = max(0.0, 1.0 - var_95 * 10)  # Scale VaR

            # Calculate weighted score
            overall_score = (
                trend_score * weights['trend_strength'] +
                liquidity_score * weights['liquidity_score'] +
                volatility_score * weights['volatility_score'] +
                technical_score * weights['technical_score'] +
                risk_score * weights['risk_score']
            )

            return float(np.clip(overall_score, 0.0, 1.0))

        except Exception as e:
            logging.error(f"Error calculating overall score: {e}")
            return 0.5

    def _generate_recommendations(self, market_metrics: Dict, technical_indicators: Dict,
                                risk_metrics: Dict, overall_score: float) -> List[str]:
        """Generate trading recommendations based on analysis"""
        recommendations = []

        try:
            # Trend-based recommendations
            trend_strength = market_metrics.get('trend_strength', 0.0)
            if trend_strength > 0.7:
                recommendations.append("Strong trend detected - consider trend-following strategy")
            elif trend_strength < 0.3:
                recommendations.append("Weak trend - consider range-bound strategy")

            # RSI-based recommendations
            rsi = technical_indicators.get('rsi', 50.0)
            if rsi > 70:
                recommendations.append("RSI indicates overbought conditions - consider selling")
            elif rsi < 30:
                recommendations.append("RSI indicates oversold conditions - consider buying")

            # Risk-based recommendations
            var_95 = risk_metrics.get('value_at_risk_95', 0.0)
            if var_95 > 0.05:
                recommendations.append("High risk detected - reduce position size")

            # Overall score recommendations
            if overall_score > 0.8:
                recommendations.append("Excellent market conditions - consider increasing allocation")
            elif overall_score < 0.3:
                recommendations.append("Poor market conditions - consider avoiding or reducing exposure")

            if not recommendations:
                recommendations.append("Market conditions are neutral - proceed with standard strategy")

        except Exception as e:
            logging.error(f"Error generating recommendations: {e}")
            recommendations = ["Unable to generate recommendations due to analysis error"]

        return recommendations

    # Default value methods
    def _get_default_metrics(self) -> Dict[str, Any]:
        """Get default market metrics when insufficient data"""
        return {
            'price_momentum': {'short_momentum': 0.0, 'medium_momentum': 0.0, 'long_momentum': 0.0},
            'volume_profile': {'average_volume': 0.0, 'volume_trend': 'unknown'},
            'price_levels': {'current_price': 0.0, 'support': 0.0, 'resistance': 0.0},
            'market_efficiency': 0.5,
            'trend_strength': 0.0,
            'price_velocity': 0.0,
            'volume_trend': 0.0
        }

    def _get_default_technical_indicators(self) -> Dict[str, Any]:
        """Get default technical indicators when insufficient data"""
        return {
            'moving_averages': {f'ma_{w}': 0.0 for w in self.config.window_sizes},
            'rsi': 50.0,
            'macd': {'macd': 0.0, 'signal': 0.0, 'histogram': 0.0},
            'bollinger_bands': {'upper': 0.0, 'middle': 0.0, 'lower': 0.0},
            'support_resistance': {'support': 0.0, 'resistance': 0.0},
            'momentum_indicators': {'momentum': 0.0}
        }

    def _get_default_risk_metrics(self) -> Dict[str, Any]:
        """Get default risk metrics when insufficient data"""
        return {
            'value_at_risk_95': 0.0,
            'value_at_risk_99': 0.0,
            'expected_shortfall': 0.0,
            'liquidity_risk': 0.5,
            'volatility_risk': 0.5,
            'tail_risk': 0.0,
            'drawdown_risk': 0.0
        }

    def _create_error_result(self, market_data: Dict, error_msg: str) -> MarketAnalysisResult:
        """Create error result when analysis fails"""
        market_id = market_data.get('market_id', 'unknown')
        return MarketAnalysisResult(
            timestamp=datetime.utcnow(),
            market_id=market_id,
            market_metrics=self._get_default_metrics(),
            technical_indicators=self._get_default_technical_indicators(),
            liquidity_analysis={'liquidity_score': 0.0, 'liquidity_trend': 'unknown'},
            volatility_metrics={'volatility': 0.0, 'volatility_trend': 'unknown'},
            correlation_data={},
            market_impact={'impact_estimates': {}, 'liquidity_rating': 'unknown'},
            risk_metrics=self._get_default_risk_metrics(),
            overall_score=0.0,
            recommendations=[f"Analysis failed: {error_msg}"]
        )
