"""
global_risk_manager.py
Agent responsible for monitoring aggregate risk and enforcing global risk cutoffs.
Enhanced with advanced risk controller from rife-ai backend.
Integrates with monitoring and metrics for observability.
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta

from src.core.monitoring import monitoring
from src.risk.advanced_risk_controller import AdvancedRiskController, RiskAssessment
from src.data.models import SignalModel

class GlobalRiskManager:
    """
    Enhanced Global Risk Manager with advanced risk control capabilities
    Integrates with AdvancedRiskController for comprehensive risk management
    """

    def __init__(self, risk_limits: Optional[Dict[str, float]] = None,
                 advanced_config: Optional[Dict[str, Any]] = None):
        # Legacy risk limits for backward compatibility
        self.risk_limits = risk_limits or {"max_drawdown": 0.2, "max_position": 100000}
        self.current_risk: Dict[str, float] = {}

        # Initialize advanced risk controller
        self.advanced_controller = AdvancedRiskController(advanced_config)

        # Risk management state
        self.emergency_stop_active = False
        self.risk_assessments: List[RiskAssessment] = []
        self.portfolio_data = {
            'total_value': 0.0,
            'daily_pnl': 0.0,
            'positions': {}
        }

        monitoring.log_event("GlobalRiskManager",
                           f"Enhanced Global Risk Manager initialized with limits: {self.risk_limits}")
        logging.info("Enhanced Global Risk Manager initialized with advanced risk controller")

    async def validate_trade_comprehensive(self, trade_signal: SignalModel,
                                         market_data: Dict[str, Any]) -> RiskAssessment:
        """
        Comprehensive trade validation using advanced risk controller
        Returns detailed risk assessment
        """
        if self.emergency_stop_active:
            return RiskAssessment(
                approved=False,
                risk_score=0.0,
                reasons=["Emergency stop is active"],
                adjustments={},
                risk_metrics={},
                timestamp=datetime.now()
            )

        try:
            # Use advanced risk controller for comprehensive validation
            assessment = await self.advanced_controller.validate_trade(
                trade_signal, market_data, self.portfolio_data
            )

            # Store assessment for tracking
            self.risk_assessments.append(assessment)
            if len(self.risk_assessments) > 1000:  # Limit history
                self.risk_assessments.pop(0)

            # Emit metrics
            monitoring.emit_metric("risk_score", assessment.risk_score,
                                 labels={"token": trade_signal.token})
            monitoring.emit_metric("trade_approved", 1 if assessment.approved else 0,
                                 labels={"token": trade_signal.token})

            # Log significant events
            if not assessment.approved:
                monitoring.log_event("GlobalRiskManager",
                                   f"Trade rejected for {trade_signal.token}: {assessment.reasons}")
            elif assessment.risk_score < 0.5:
                monitoring.log_event("GlobalRiskManager",
                                   f"High risk trade approved for {trade_signal.token}: score={assessment.risk_score:.2f}")

            return assessment

        except Exception as e:
            logging.error(f"Error in comprehensive trade validation: {e}")
            return RiskAssessment(
                approved=False,
                risk_score=0.0,
                reasons=[f"Validation error: {str(e)}"],
                adjustments={},
                risk_metrics={},
                timestamp=datetime.now()
            )

    def update_risk(self, risk_type: str, value: float):
        self.current_risk[risk_type] = value
        monitoring.emit_metric("risk_metric", value, labels={"type": risk_type})
        self.check_limits(risk_type)

    def check_limits(self, risk_type: str):
        limit = self.risk_limits.get(risk_type)
        value = self.current_risk.get(risk_type)
        if limit is not None and value is not None and value > limit:
            monitoring.alert("GlobalRiskManager", f"{risk_type} exceeded limit: {value} > {limit}", severity="critical")
            return False
        return True

    def update_portfolio_data(self, portfolio_data: Dict[str, Any]):
        """Update portfolio data for risk calculations"""
        try:
            self.portfolio_data.update(portfolio_data)

            # Update advanced controller
            if 'total_value' in portfolio_data:
                self.advanced_controller.portfolio_value = portfolio_data['total_value']
            if 'daily_pnl' in portfolio_data:
                self.advanced_controller.daily_pnl = portfolio_data['daily_pnl']

            # Update positions in advanced controller
            if 'positions' in portfolio_data:
                for token, position_data in portfolio_data['positions'].items():
                    self.advanced_controller.update_position(token, position_data)

            monitoring.log_event("GlobalRiskManager", "Portfolio data updated")

        except Exception as e:
            logging.error(f"Error updating portfolio data: {e}")

    def activate_emergency_stop(self, reason: str = "Manual activation") -> Dict[str, Any]:
        """Activate emergency stop mechanism"""
        try:
            self.emergency_stop_active = True

            # Get emergency actions from advanced controller
            emergency_result = self.advanced_controller.emergency_stop()

            monitoring.alert("GlobalRiskManager",
                           f"EMERGENCY STOP ACTIVATED: {reason}",
                           severity="critical")

            logging.critical(f"Emergency stop activated: {reason}")

            return {
                'emergency_stop_active': True,
                'reason': reason,
                'timestamp': datetime.now(),
                'advanced_controller_result': emergency_result
            }

        except Exception as e:
            logging.error(f"Error activating emergency stop: {e}")
            return {'error': str(e)}

    def deactivate_emergency_stop(self, reason: str = "Manual deactivation") -> Dict[str, Any]:
        """Deactivate emergency stop mechanism"""
        try:
            self.emergency_stop_active = False

            monitoring.log_event("GlobalRiskManager",
                               f"Emergency stop deactivated: {reason}")

            logging.info(f"Emergency stop deactivated: {reason}")

            return {
                'emergency_stop_active': False,
                'reason': reason,
                'timestamp': datetime.now()
            }

        except Exception as e:
            logging.error(f"Error deactivating emergency stop: {e}")
            return {'error': str(e)}

    def get_risk_status(self) -> Dict[str, Any]:
        """Get comprehensive risk status"""
        try:
            # Get basic risk status
            basic_status = {
                'current_risk': self.current_risk,
                'risk_limits': self.risk_limits,
                'emergency_stop_active': self.emergency_stop_active
            }

            # Get advanced risk summary
            advanced_summary = self.advanced_controller.get_risk_summary()

            # Get recent assessment statistics
            recent_assessments = self.risk_assessments[-20:] if self.risk_assessments else []
            assessment_stats = {
                'total_assessments': len(self.risk_assessments),
                'recent_assessments': len(recent_assessments),
                'recent_approval_rate': sum(1 for a in recent_assessments if a.approved) / len(recent_assessments) if recent_assessments else 0,
                'avg_recent_risk_score': sum(a.risk_score for a in recent_assessments) / len(recent_assessments) if recent_assessments else 0
            }

            return {
                **basic_status,
                'advanced_risk_summary': advanced_summary,
                'assessment_statistics': assessment_stats,
                'portfolio_data': self.portfolio_data
            }

        except Exception as e:
            logging.error(f"Error getting risk status: {e}")
            return {
                'current_risk': self.current_risk,
                'error': str(e)
            }

    def get_risk_recommendations(self) -> List[str]:
        """Get risk management recommendations"""
        recommendations = []

        try:
            # Check recent assessment trends
            recent_assessments = self.risk_assessments[-10:] if self.risk_assessments else []

            if recent_assessments:
                avg_risk_score = sum(a.risk_score for a in recent_assessments) / len(recent_assessments)
                approval_rate = sum(1 for a in recent_assessments if a.approved) / len(recent_assessments)

                if avg_risk_score < 0.3:
                    recommendations.append("Risk scores are consistently low - consider reviewing risk parameters")
                elif avg_risk_score > 0.8:
                    recommendations.append("Risk scores are high - market conditions may be favorable")

                if approval_rate < 0.5:
                    recommendations.append("Low trade approval rate - consider adjusting strategy or risk thresholds")
                elif approval_rate > 0.9:
                    recommendations.append("Very high approval rate - consider if risk controls are sufficient")

            # Check portfolio concentration
            if self.portfolio_data.get('total_value', 0) > 0:
                positions = self.portfolio_data.get('positions', {})
                if positions:
                    max_position = max(abs(pos.get('size', 0)) for pos in positions.values())
                    concentration = max_position / self.portfolio_data['total_value']

                    if concentration > 0.5:
                        recommendations.append("High portfolio concentration detected - consider diversification")

            # Check emergency stop status
            if self.emergency_stop_active:
                recommendations.append("Emergency stop is active - review conditions before resuming trading")

            if not recommendations:
                recommendations.append("Risk management status appears normal")

        except Exception as e:
            logging.error(f"Error generating risk recommendations: {e}")
            recommendations = ["Unable to generate recommendations due to error"]

        return recommendations
