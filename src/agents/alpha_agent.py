"""
ALPHA Agent: Market Intelligence and Signal Generation
Consolidates analytics and intelligence to produce actionable trading signals.
Enhanced with ML prediction capabilities from rife-ai backend.
"""
import asyncio
import logging
from typing import Optional, Dict, List, Any
from datetime import datetime

from src.data.models import SignalModel
from src.analytics.technical_analysis import generate_technical_signal
# from src.analytics.whale_tracking import detect_whale_activity  # COMMENTED OUT: TypeScript module, not importable in Python
from src.analytics.rug_detector import RugDetector
from src.core.monitoring.monitoring import log_event, health_check
from src.ml.prediction_system import RealTimePredictionSystem, PredictionResult
from src.data.phase1_data_pipeline_unification import UnifiedDataService

class AlphaAgent:
    def __init__(self,
                 rug_detector: Optional[RugDetector] = None,
                 data_service: Optional[UnifiedDataService] = None,
                 enable_ml: bool = True):
        self.rug_detector = rug_detector
        self.data_service = data_service
        self.enable_ml = enable_ml
        self.ml_system: Optional[RealTimePredictionSystem] = None
        self.is_initialized = False

        log_event("AlphaAgent", "Initialized AlphaAgent with ML capabilities")

    async def initialize(self):
        """Initialize the ALPHA agent with ML capabilities"""
        try:
            if self.enable_ml and self.data_service:
                self.ml_system = RealTimePredictionSystem()
                await self.ml_system.initialize(self.data_service)
                log_event("AlphaAgent", "ML prediction system initialized")

            self.is_initialized = True
            log_event("AlphaAgent", "ALPHA agent initialization complete")

        except Exception as e:
            logging.error(f"Failed to initialize ALPHA agent: {e}")
            self.is_initialized = False

    async def generate_signal(self, market_data, token: Optional[str] = None) -> Optional[SignalModel]:
        """
        Generate a trading signal using analytics modules, ML predictions, and token safety check.
        Enhanced with ML capabilities from rife-ai backend.
        Returns a SignalModel or None if no actionable signal.
        """
        log_event("AlphaAgent", "Generating enhanced signal with ML")

        # Extract token from market_data if not provided
        if not token:
            token = getattr(market_data, 'token', None) or market_data.get('token') if isinstance(market_data, dict) else None

        if not token:
            log_event("AlphaAgent", "No token specified for signal generation", level="warning")
            return None

        # Generate traditional technical analysis signal
        ta_signal = generate_technical_signal(market_data)

        # Generate ML prediction if available
        ml_signal = None
        if self.ml_system and self.is_initialized:
            try:
                ml_prediction = await self.ml_system.get_prediction(token)
                if ml_prediction:
                    ml_signal = self.ml_system.to_signal_model(ml_prediction)
                    log_event("AlphaAgent", f"ML prediction generated for {token}: {ml_prediction.direction}")
            except Exception as e:
                logging.error(f"Error getting ML prediction: {e}")

        # Check token safety if rug detector is available
        if self.rug_detector and token:
            try:
                safety = await self.rug_detector.analyze_token_safety(token)
                if not safety.is_safe:
                    log_event("AlphaAgent", f"Unsafe token detected: {token}", level="warning")
                    return None  # Do not trade unsafe tokens
            except Exception as e:
                logging.error(f"Error checking token safety: {e}")

        # Combine signals using weighted approach
        final_signal = self._combine_signals(ta_signal, ml_signal, token)

        if final_signal:
            log_event("AlphaAgent", f"Generated signal for {token}: {final_signal.direction} (confidence: {final_signal.confidence:.2f})")
        else:
            log_event("AlphaAgent", f"No actionable signal generated for {token}", level="info")

        return final_signal

    def _combine_signals(self, ta_signal: Optional[SignalModel], ml_signal: Optional[SignalModel], token: str) -> Optional[SignalModel]:
        """
        Combine technical analysis and ML signals using weighted approach
        """
        try:
            # If no signals available, return None
            if not ta_signal and not ml_signal:
                return None

            # If only one signal available, return it (with adjusted confidence)
            if ta_signal and not ml_signal:
                ta_signal.confidence *= 0.8  # Reduce confidence when no ML confirmation
                return ta_signal

            if ml_signal and not ta_signal:
                ml_signal.confidence *= 0.7  # ML alone gets lower weight
                return ml_signal

            # Both signals available - combine them
            # Weight: TA = 0.4, ML = 0.6 (ML gets higher weight)
            ta_weight = 0.4
            ml_weight = 0.6

            # Check if signals agree on direction
            if ta_signal.direction == ml_signal.direction:
                # Signals agree - boost confidence
                combined_confidence = (ta_signal.confidence * ta_weight + ml_signal.confidence * ml_weight) * 1.2
                combined_confidence = min(combined_confidence, 1.0)  # Cap at 1.0

                return SignalModel(
                    token=token,
                    entry_price=ta_signal.entry_price,  # Use TA price as base
                    direction=ta_signal.direction,
                    confidence=combined_confidence,
                    timestamp=datetime.now(),
                    source="combined_ta_ml"
                )
            else:
                # Signals disagree - reduce confidence or return None if too low
                combined_confidence = (ta_signal.confidence * ta_weight + ml_signal.confidence * ml_weight) * 0.6

                if combined_confidence < 0.3:
                    return None  # Too uncertain

                # Use the signal with higher confidence
                if ta_signal.confidence > ml_signal.confidence:
                    ta_signal.confidence = combined_confidence
                    ta_signal.source = "ta_dominant_combined"
                    return ta_signal
                else:
                    ml_signal.confidence = combined_confidence
                    ml_signal.source = "ml_dominant_combined"
                    return ml_signal

        except Exception as e:
            logging.error(f"Error combining signals: {e}")
            return ta_signal or ml_signal  # Return any available signal as fallback

    def health(self) -> bool:
        """Enhanced health check for AlphaAgent with ML capabilities."""
        try:
            base_health = health_check("AlphaAgent", lambda: True)

            # Check ML system health if enabled
            if self.enable_ml and self.ml_system:
                ml_health = self.ml_system.get_system_health()
                ml_ok = ml_health.get('initialized', False)
                log_event("AlphaAgent", f"ML system health: {ml_ok}")
                return base_health and ml_ok

            return base_health

        except Exception as e:
            logging.error(f"Error in health check: {e}")
            return False

    def get_system_status(self) -> Dict[str, Any]:
        """Get detailed system status"""
        status = {
            'initialized': self.is_initialized,
            'rug_detector_available': self.rug_detector is not None,
            'data_service_available': self.data_service is not None,
            'ml_enabled': self.enable_ml,
            'ml_system_available': self.ml_system is not None
        }

        if self.ml_system:
            status['ml_system_health'] = self.ml_system.get_system_health()

        return status
