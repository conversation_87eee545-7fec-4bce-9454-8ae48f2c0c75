"""
Enhanced Order Manager for NEXUS Trading System
Adapted from rife-ai backend for comprehensive order management
"""
import asyncio
import logging
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from datetime import datetime, timed<PERSON>ta
from enum import Enum

from ..data.models import TradeOrderModel, SignalModel
from ..bridges.jupiter_bridge import JupiterBridge
from ..bridges.orca_bridge import OrcaBridge
from ..bridges.raydium_bridge import RaydiumBridge
from ..bridges.pumpfun_bridge import PumpfunBridge
from ..core.monitoring.monitoring import log_event

class OrderStatus(Enum):
    PENDING = "pending"
    SUBMITTED = "submitted"
    FILLED = "filled"
    PARTIALLY_FILLED = "partially_filled"
    CANCELLED = "cancelled"
    FAILED = "failed"

class OrderType(Enum):
    MARKET = "market"
    LIMIT = "limit"
    STOP_LOSS = "stop_loss"
    TAKE_PROFIT = "take_profit"

@dataclass
class OrderResult:
    """Result of order execution"""
    order_id: str
    status: OrderStatus
    filled_size: float
    filled_price: float
    transaction_signature: Optional[str]
    dex_used: str
    timestamp: datetime
    error_message: Optional[str] = None
    fees_paid: float = 0.0
    slippage: float = 0.0

@dataclass
class OrderRequest:
    """Order request structure"""
    token: str
    direction: str  # 'BUY' or 'SELL'
    size: float
    order_type: OrderType = OrderType.MARKET
    limit_price: Optional[float] = None
    stop_price: Optional[float] = None
    max_slippage: float = 0.05
    timeout_seconds: int = 30

class EnhancedOrderManager:
    """
    Enhanced order manager with multi-DEX support and advanced order types
    Integrates with NEXUS execution layer and THETA agent
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or {}
        self.active_orders: Dict[str, Dict] = {}
        self.order_history: List[OrderResult] = []
        self.dex_bridges = {}
        self.is_initialized = False
        self.order_counter = 0
        
        # Performance tracking
        self.dex_performance = {
            'jupiter': {'success_rate': 0.0, 'avg_slippage': 0.0, 'avg_execution_time': 0.0},
            'orca': {'success_rate': 0.0, 'avg_slippage': 0.0, 'avg_execution_time': 0.0},
            'raydium': {'success_rate': 0.0, 'avg_slippage': 0.0, 'avg_execution_time': 0.0},
            'pumpfun': {'success_rate': 0.0, 'avg_slippage': 0.0, 'avg_execution_time': 0.0}
        }
        
        logging.info("Initialized Enhanced Order Manager")
        
    async def initialize(self):
        """Initialize DEX bridges and connections"""
        try:
            # Initialize DEX bridges
            self.dex_bridges = {
                'jupiter': JupiterBridge(),
                'orca': OrcaBridge(),
                'raydium': RaydiumBridge(),
                'pumpfun': PumpfunBridge()
            }
            
            # Initialize each bridge
            for dex_name, bridge in self.dex_bridges.items():
                try:
                    if hasattr(bridge, 'initialize'):
                        await bridge.initialize()
                    logging.info(f"Initialized {dex_name} bridge")
                except Exception as e:
                    logging.error(f"Failed to initialize {dex_name} bridge: {e}")
            
            self.is_initialized = True
            log_event("OrderManager", "Enhanced Order Manager initialized successfully")
            
        except Exception as e:
            logging.error(f"Failed to initialize Enhanced Order Manager: {e}")
            self.is_initialized = False
            
    async def execute_order(self, order_request: OrderRequest) -> OrderResult:
        """
        Execute order with optimal DEX selection and advanced order handling
        """
        if not self.is_initialized:
            return OrderResult(
                order_id="",
                status=OrderStatus.FAILED,
                filled_size=0.0,
                filled_price=0.0,
                transaction_signature=None,
                dex_used="none",
                timestamp=datetime.now(),
                error_message="Order manager not initialized"
            )
        
        order_id = self._generate_order_id()
        start_time = datetime.now()
        
        try:
            log_event("OrderManager", f"Executing order {order_id} for {order_request.token}")
            
            # Select optimal DEX
            optimal_dex = await self._select_optimal_dex(order_request)
            
            # Execute based on order type
            if order_request.order_type == OrderType.MARKET:
                result = await self._execute_market_order(order_id, order_request, optimal_dex)
            elif order_request.order_type == OrderType.LIMIT:
                result = await self._execute_limit_order(order_id, order_request, optimal_dex)
            else:
                result = OrderResult(
                    order_id=order_id,
                    status=OrderStatus.FAILED,
                    filled_size=0.0,
                    filled_price=0.0,
                    transaction_signature=None,
                    dex_used=optimal_dex,
                    timestamp=datetime.now(),
                    error_message=f"Order type {order_request.order_type} not yet implemented"
                )
            
            # Update performance metrics
            execution_time = (datetime.now() - start_time).total_seconds()
            self._update_dex_performance(optimal_dex, result, execution_time)
            
            # Store in history
            self.order_history.append(result)
            if len(self.order_history) > 10000:  # Limit history size
                self.order_history.pop(0)
            
            log_event("OrderManager", 
                     f"Order {order_id} completed: {result.status.value}, filled: {result.filled_size}")
            
            return result
            
        except Exception as e:
            logging.error(f"Error executing order {order_id}: {e}")
            return OrderResult(
                order_id=order_id,
                status=OrderStatus.FAILED,
                filled_size=0.0,
                filled_price=0.0,
                transaction_signature=None,
                dex_used="unknown",
                timestamp=datetime.now(),
                error_message=str(e)
            )
            
    async def _select_optimal_dex(self, order_request: OrderRequest) -> str:
        """Select optimal DEX based on liquidity, fees, and performance"""
        try:
            dex_scores = {}
            
            for dex_name, bridge in self.dex_bridges.items():
                try:
                    # Get price quote to assess liquidity and fees
                    if hasattr(bridge, 'get_price'):
                        price_data = await bridge.get_price(order_request.token)
                        if price_data:
                            # Score based on multiple factors
                            liquidity_score = min(1.0, price_data.get('volume_24h', 0) / 1000000)  # Normalize by 1M
                            performance_score = self.dex_performance[dex_name]['success_rate']
                            slippage_score = 1.0 - self.dex_performance[dex_name]['avg_slippage']
                            
                            # Weighted score
                            total_score = (
                                liquidity_score * 0.4 +
                                performance_score * 0.3 +
                                slippage_score * 0.3
                            )
                            
                            dex_scores[dex_name] = total_score
                        else:
                            dex_scores[dex_name] = 0.0
                    else:
                        dex_scores[dex_name] = 0.5  # Default score
                        
                except Exception as e:
                    logging.error(f"Error scoring {dex_name}: {e}")
                    dex_scores[dex_name] = 0.0
            
            # Select DEX with highest score
            if dex_scores:
                optimal_dex = max(dex_scores, key=dex_scores.get)
                logging.info(f"Selected {optimal_dex} for order (score: {dex_scores[optimal_dex]:.2f})")
                return optimal_dex
            else:
                return 'jupiter'  # Default fallback
                
        except Exception as e:
            logging.error(f"Error selecting optimal DEX: {e}")
            return 'jupiter'  # Default fallback
            
    async def _execute_market_order(self, order_id: str, order_request: OrderRequest, dex_name: str) -> OrderResult:
        """Execute market order on selected DEX"""
        try:
            bridge = self.dex_bridges[dex_name]
            
            # Execute swap
            if hasattr(bridge, 'execute_swap'):
                swap_result = await bridge.execute_swap(
                    token=order_request.token,
                    amount=order_request.size,
                    direction=order_request.direction,
                    max_slippage=order_request.max_slippage
                )
                
                if swap_result.get('success', False):
                    return OrderResult(
                        order_id=order_id,
                        status=OrderStatus.FILLED,
                        filled_size=swap_result.get('executed_amount', order_request.size),
                        filled_price=swap_result.get('executed_price', 0.0),
                        transaction_signature=swap_result.get('transaction_signature'),
                        dex_used=dex_name,
                        timestamp=datetime.now(),
                        fees_paid=swap_result.get('fees', 0.0),
                        slippage=swap_result.get('slippage', 0.0)
                    )
                else:
                    return OrderResult(
                        order_id=order_id,
                        status=OrderStatus.FAILED,
                        filled_size=0.0,
                        filled_price=0.0,
                        transaction_signature=None,
                        dex_used=dex_name,
                        timestamp=datetime.now(),
                        error_message=swap_result.get('error', 'Unknown swap error')
                    )
            else:
                return OrderResult(
                    order_id=order_id,
                    status=OrderStatus.FAILED,
                    filled_size=0.0,
                    filled_price=0.0,
                    transaction_signature=None,
                    dex_used=dex_name,
                    timestamp=datetime.now(),
                    error_message=f"{dex_name} bridge does not support execute_swap"
                )
                
        except Exception as e:
            logging.error(f"Error executing market order on {dex_name}: {e}")
            return OrderResult(
                order_id=order_id,
                status=OrderStatus.FAILED,
                filled_size=0.0,
                filled_price=0.0,
                transaction_signature=None,
                dex_used=dex_name,
                timestamp=datetime.now(),
                error_message=str(e)
            )

    async def _execute_limit_order(self, order_id: str, order_request: OrderRequest, dex_name: str) -> OrderResult:
        """Execute limit order (placeholder - would need DEX-specific implementation)"""
        # For now, return not implemented
        return OrderResult(
            order_id=order_id,
            status=OrderStatus.FAILED,
            filled_size=0.0,
            filled_price=0.0,
            transaction_signature=None,
            dex_used=dex_name,
            timestamp=datetime.now(),
            error_message="Limit orders not yet implemented"
        )

    def _generate_order_id(self) -> str:
        """Generate unique order ID"""
        self.order_counter += 1
        timestamp = int(datetime.now().timestamp() * 1000)
        return f"NEXUS_{timestamp}_{self.order_counter:06d}"

    def _update_dex_performance(self, dex_name: str, result: OrderResult, execution_time: float):
        """Update DEX performance metrics"""
        try:
            if dex_name not in self.dex_performance:
                self.dex_performance[dex_name] = {
                    'success_rate': 0.0, 'avg_slippage': 0.0, 'avg_execution_time': 0.0,
                    'total_orders': 0, 'successful_orders': 0
                }

            perf = self.dex_performance[dex_name]
            perf['total_orders'] = perf.get('total_orders', 0) + 1

            if result.status == OrderStatus.FILLED:
                perf['successful_orders'] = perf.get('successful_orders', 0) + 1

                # Update running averages
                total_successful = perf['successful_orders']
                perf['avg_slippage'] = (
                    (perf['avg_slippage'] * (total_successful - 1) + result.slippage) / total_successful
                )
                perf['avg_execution_time'] = (
                    (perf['avg_execution_time'] * (total_successful - 1) + execution_time) / total_successful
                )

            perf['success_rate'] = perf['successful_orders'] / perf['total_orders']

        except Exception as e:
            logging.error(f"Error updating DEX performance for {dex_name}: {e}")

    def get_order_status(self, order_id: str) -> Optional[OrderResult]:
        """Get status of specific order"""
        for order in reversed(self.order_history):
            if order.order_id == order_id:
                return order
        return None

    def get_recent_orders(self, limit: int = 50) -> List[OrderResult]:
        """Get recent order history"""
        return self.order_history[-limit:] if self.order_history else []

    def get_dex_performance_summary(self) -> Dict[str, Any]:
        """Get DEX performance summary"""
        return {
            'dex_performance': self.dex_performance.copy(),
            'total_orders': len(self.order_history),
            'successful_orders': sum(1 for order in self.order_history if order.status == OrderStatus.FILLED),
            'overall_success_rate': (
                sum(1 for order in self.order_history if order.status == OrderStatus.FILLED) /
                len(self.order_history) if self.order_history else 0
            )
        }
