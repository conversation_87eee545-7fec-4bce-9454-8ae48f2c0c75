"""
Advanced Risk Controller for NEXUS Trading System
Adapted from rife-ai backend for comprehensive risk management
"""
import asyncio
import logging
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta
import numpy as np

from ..data.models import SignalModel, TradeOrderModel
from ..core.monitoring.monitoring import log_event

@dataclass
class RiskThresholds:
    """Risk threshold configuration"""
    max_position_size: float = 0.1  # 10% of portfolio
    max_daily_drawdown: float = 0.05  # 5% daily drawdown limit
    max_concentration: float = 0.3  # 30% max concentration in single asset
    volatility_limit: float = 2.0  # 200% annualized volatility limit
    correlation_limit: float = 0.8  # 80% correlation limit
    max_leverage: float = 1.0  # No leverage by default
    min_liquidity: float = 100000.0  # Minimum daily volume
    max_spread: float = 0.05  # 5% max bid-ask spread
    var_limit: float = 0.02  # 2% VaR limit
    
@dataclass
class RiskAssessment:
    """Risk assessment result"""
    approved: bool
    risk_score: float
    reasons: List[str]
    adjustments: Dict[str, Any]
    risk_metrics: Dict[str, Any]
    timestamp: datetime

class AdvancedRiskController:
    """
    Advanced risk controller with comprehensive risk management
    Integrates with NEXUS global risk manager and SIGMA agent
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or {}
        self.thresholds = RiskThresholds(**self.config.get('risk_thresholds', {}))
        self.position_cache: Dict[str, Dict] = {}
        self.risk_metrics: Dict[str, Any] = {}
        self.portfolio_value = 0.0
        self.daily_pnl = 0.0
        self.risk_history: List[RiskAssessment] = []
        self.max_history_length = 1000
        
        logging.info("Initialized Advanced Risk Controller")
        
    async def validate_trade(self, trade_signal: SignalModel, market_data: Dict[str, Any], 
                           portfolio_data: Optional[Dict[str, Any]] = None) -> RiskAssessment:
        """
        Validate trade against comprehensive risk parameters
        Returns detailed risk assessment
        """
        try:
            log_event("RiskController", f"Validating trade for {trade_signal.token}")
            
            # Update portfolio data if provided
            if portfolio_data:
                self.portfolio_value = portfolio_data.get('total_value', self.portfolio_value)
                self.daily_pnl = portfolio_data.get('daily_pnl', self.daily_pnl)
            
            # Perform all risk checks
            risk_checks = await asyncio.gather(
                self._check_position_limits(trade_signal),
                self._check_portfolio_risk(trade_signal),
                self._check_market_risk(market_data),
                self._check_correlation_risk(trade_signal.token, market_data),
                self._check_liquidity_risk(market_data),
                self._check_volatility_risk(market_data),
                return_exceptions=True
            )
            
            # Process risk check results
            failed_checks = []
            risk_score = 1.0
            
            for check in risk_checks:
                if isinstance(check, Exception):
                    logging.error(f"Risk check failed with exception: {check}")
                    failed_checks.append(f"Risk check error: {str(check)}")
                    risk_score *= 0.5
                elif not check.get('passed', False):
                    failed_checks.append(check.get('reason', 'Unknown risk'))
                    risk_score *= check.get('risk_multiplier', 0.5)
            
            # Calculate position adjustments
            adjustments = self._calculate_position_adjustments(trade_signal, risk_score)
            
            # Create risk assessment
            assessment = RiskAssessment(
                approved=len(failed_checks) == 0 and risk_score > 0.3,
                risk_score=risk_score,
                reasons=failed_checks,
                adjustments=adjustments,
                risk_metrics=self.risk_metrics.copy(),
                timestamp=datetime.now()
            )
            
            # Store in history
            self.risk_history.append(assessment)
            if len(self.risk_history) > self.max_history_length:
                self.risk_history.pop(0)
            
            log_event("RiskController", 
                     f"Risk assessment complete: approved={assessment.approved}, score={assessment.risk_score:.2f}")
            
            return assessment
            
        except Exception as e:
            logging.error(f"Error in trade validation: {e}")
            return RiskAssessment(
                approved=False,
                risk_score=0.0,
                reasons=[f"Validation error: {str(e)}"],
                adjustments={},
                risk_metrics={},
                timestamp=datetime.now()
            )
            
    async def _check_position_limits(self, trade_signal: SignalModel) -> Dict[str, Any]:
        """Check position size limits"""
        try:
            token = trade_signal.token
            current_position = self.position_cache.get(token, {}).get('size', 0.0)
            
            # Calculate proposed position size
            if hasattr(trade_signal, 'position_size'):
                proposed_size = trade_signal.position_size
            else:
                # Default position size based on confidence
                proposed_size = trade_signal.confidence * self.thresholds.max_position_size
            
            # Check against maximum position size
            total_position = abs(current_position) + abs(proposed_size)
            max_allowed = self.thresholds.max_position_size * self.portfolio_value
            
            if total_position > max_allowed:
                return {
                    'passed': False,
                    'reason': f'Position size limit exceeded: {total_position:.2f} > {max_allowed:.2f}',
                    'risk_multiplier': 0.3
                }
            
            # Check concentration limits
            concentration = total_position / self.portfolio_value if self.portfolio_value > 0 else 0
            if concentration > self.thresholds.max_concentration:
                return {
                    'passed': False,
                    'reason': f'Concentration limit exceeded: {concentration:.2%} > {self.thresholds.max_concentration:.2%}',
                    'risk_multiplier': 0.4
                }
            
            return {'passed': True, 'concentration': concentration}
            
        except Exception as e:
            logging.error(f"Error checking position limits: {e}")
            return {'passed': False, 'reason': f'Position check error: {str(e)}', 'risk_multiplier': 0.2}
            
    async def _check_portfolio_risk(self, trade_signal: SignalModel) -> Dict[str, Any]:
        """Check portfolio-level risk metrics"""
        try:
            # Check daily drawdown
            if self.portfolio_value > 0:
                drawdown_ratio = abs(self.daily_pnl) / self.portfolio_value
                if self.daily_pnl < 0 and drawdown_ratio > self.thresholds.max_daily_drawdown:
                    return {
                        'passed': False,
                        'reason': f'Daily drawdown limit exceeded: {drawdown_ratio:.2%} > {self.thresholds.max_daily_drawdown:.2%}',
                        'risk_multiplier': 0.1
                    }
            
            # Check leverage
            total_exposure = sum(abs(pos.get('size', 0)) for pos in self.position_cache.values())
            leverage = total_exposure / self.portfolio_value if self.portfolio_value > 0 else 0
            
            if leverage > self.thresholds.max_leverage:
                return {
                    'passed': False,
                    'reason': f'Leverage limit exceeded: {leverage:.2f}x > {self.thresholds.max_leverage:.2f}x',
                    'risk_multiplier': 0.3
                }
            
            self.risk_metrics['portfolio_risk'] = {
                'daily_pnl': self.daily_pnl,
                'drawdown_ratio': drawdown_ratio if self.portfolio_value > 0 else 0,
                'leverage': leverage
            }
            
            return {'passed': True, 'metrics': self.risk_metrics['portfolio_risk']}
            
        except Exception as e:
            logging.error(f"Error checking portfolio risk: {e}")
            return {'passed': False, 'reason': f'Portfolio risk check error: {str(e)}', 'risk_multiplier': 0.2}
            
    async def _check_market_risk(self, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """Check market-specific risk factors"""
        try:
            # Calculate volatility if price history available
            volatility = 0.0
            if 'price_history' in market_data and len(market_data['price_history']) > 1:
                prices = np.array(market_data['price_history'])
                returns = np.diff(np.log(prices + 1e-8))
                volatility = np.std(returns) * np.sqrt(365) if len(returns) > 0 else 0.0
            
            # Get other market metrics
            liquidity = market_data.get('volume_24h', 0)
            spread = market_data.get('spread', 0)
            
            self.risk_metrics['market_risk'] = {
                'volatility': volatility,
                'liquidity': liquidity,
                'spread': spread
            }
            
            # Check volatility limit
            if volatility > self.thresholds.volatility_limit:
                return {
                    'passed': False,
                    'reason': f'High volatility: {volatility:.2f} > {self.thresholds.volatility_limit:.2f}',
                    'risk_multiplier': 0.4
                }
            
            # Check liquidity
            if liquidity < self.thresholds.min_liquidity:
                return {
                    'passed': False,
                    'reason': f'Low liquidity: ${liquidity:,.0f} < ${self.thresholds.min_liquidity:,.0f}',
                    'risk_multiplier': 0.3
                }
            
            # Check spread
            if spread > self.thresholds.max_spread:
                return {
                    'passed': False,
                    'reason': f'High spread: {spread:.2%} > {self.thresholds.max_spread:.2%}',
                    'risk_multiplier': 0.5
                }
            
            return {'passed': True, 'metrics': self.risk_metrics['market_risk']}
            
        except Exception as e:
            logging.error(f"Error checking market risk: {e}")
            return {'passed': False, 'reason': f'Market risk check error: {str(e)}', 'risk_multiplier': 0.2}
            
    async def _check_correlation_risk(self, token: str, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """Check correlation risk with existing positions"""
        try:
            # For now, implement basic correlation check
            # In production, this would calculate actual correlations
            correlation_risk = 0.0
            
            # Check if we already have positions in similar tokens
            similar_positions = 0
            for existing_token in self.position_cache.keys():
                if existing_token != token:
                    # Simple heuristic: tokens with similar names might be correlated
                    if any(word in token.lower() for word in existing_token.lower().split()):
                        similar_positions += 1
            
            if similar_positions > 3:  # Arbitrary threshold
                correlation_risk = 0.7
                
            self.risk_metrics['correlation_risk'] = {
                'similar_positions': similar_positions,
                'estimated_correlation': correlation_risk
            }
            
            if correlation_risk > self.thresholds.correlation_limit:
                return {
                    'passed': False,
                    'reason': f'High correlation risk: {correlation_risk:.2f} > {self.thresholds.correlation_limit:.2f}',
                    'risk_multiplier': 0.6
                }
            
            return {'passed': True, 'metrics': self.risk_metrics['correlation_risk']}
            
        except Exception as e:
            logging.error(f"Error checking correlation risk: {e}")
            return {'passed': True, 'metrics': {}}  # Don't fail on correlation check errors

    async def _check_liquidity_risk(self, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """Check liquidity-specific risks"""
        try:
            volume_24h = market_data.get('volume_24h', 0)
            market_cap = market_data.get('market_cap', 0)

            # Calculate liquidity metrics
            liquidity_ratio = volume_24h / market_cap if market_cap > 0 else 0

            self.risk_metrics['liquidity_risk'] = {
                'volume_24h': volume_24h,
                'market_cap': market_cap,
                'liquidity_ratio': liquidity_ratio
            }

            # Check minimum liquidity
            if volume_24h < self.thresholds.min_liquidity:
                return {
                    'passed': False,
                    'reason': f'Insufficient liquidity: ${volume_24h:,.0f} < ${self.thresholds.min_liquidity:,.0f}',
                    'risk_multiplier': 0.3
                }

            return {'passed': True, 'metrics': self.risk_metrics['liquidity_risk']}

        except Exception as e:
            logging.error(f"Error checking liquidity risk: {e}")
            return {'passed': True, 'metrics': {}}

    async def _check_volatility_risk(self, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """Check volatility-specific risks"""
        try:
            # Get volatility from market data or calculate
            volatility = market_data.get('volatility', 0)

            if volatility == 0 and 'price_history' in market_data:
                prices = np.array(market_data['price_history'])
                if len(prices) > 1:
                    returns = np.diff(np.log(prices + 1e-8))
                    volatility = np.std(returns) * np.sqrt(365)

            self.risk_metrics['volatility_risk'] = {
                'volatility': volatility,
                'volatility_percentile': self._calculate_volatility_percentile(volatility)
            }

            # Check volatility limits
            if volatility > self.thresholds.volatility_limit:
                return {
                    'passed': False,
                    'reason': f'Excessive volatility: {volatility:.2f} > {self.thresholds.volatility_limit:.2f}',
                    'risk_multiplier': 0.4
                }

            return {'passed': True, 'metrics': self.risk_metrics['volatility_risk']}

        except Exception as e:
            logging.error(f"Error checking volatility risk: {e}")
            return {'passed': True, 'metrics': {}}

    def _calculate_position_adjustments(self, trade_signal: SignalModel, risk_score: float) -> Dict[str, Any]:
        """Calculate position size adjustments based on risk assessment"""
        try:
            # Base position size from signal confidence
            base_size = trade_signal.confidence * self.thresholds.max_position_size

            # Adjust based on risk score
            adjusted_size = base_size * risk_score

            # Apply additional constraints
            max_portfolio_allocation = self.thresholds.max_position_size * self.portfolio_value
            if self.portfolio_value > 0:
                adjusted_size = min(adjusted_size, max_portfolio_allocation / self.portfolio_value)

            return {
                'original_size': base_size,
                'adjusted_size': adjusted_size,
                'risk_adjustment_factor': risk_score,
                'max_allowed_size': max_portfolio_allocation,
                'recommended_action': self._get_recommended_action(risk_score)
            }

        except Exception as e:
            logging.error(f"Error calculating position adjustments: {e}")
            return {
                'original_size': 0.0,
                'adjusted_size': 0.0,
                'risk_adjustment_factor': 0.0,
                'error': str(e)
            }

    def _get_recommended_action(self, risk_score: float) -> str:
        """Get recommended action based on risk score"""
        if risk_score > 0.8:
            return "PROCEED_FULL"
        elif risk_score > 0.6:
            return "PROCEED_REDUCED"
        elif risk_score > 0.4:
            return "PROCEED_MINIMAL"
        elif risk_score > 0.2:
            return "MONITOR_ONLY"
        else:
            return "REJECT"

    def _calculate_volatility_percentile(self, volatility: float) -> float:
        """Calculate volatility percentile (placeholder implementation)"""
        # In production, this would compare against historical volatility distribution
        if volatility < 0.5:
            return 0.2  # Low volatility
        elif volatility < 1.0:
            return 0.5  # Medium volatility
        elif volatility < 2.0:
            return 0.8  # High volatility
        else:
            return 0.95  # Very high volatility

    def update_position(self, token: str, position_data: Dict[str, Any]):
        """Update position cache with new position data"""
        try:
            self.position_cache[token] = {
                'size': position_data.get('size', 0.0),
                'entry_price': position_data.get('entry_price', 0.0),
                'current_price': position_data.get('current_price', 0.0),
                'pnl': position_data.get('pnl', 0.0),
                'timestamp': datetime.now()
            }
            log_event("RiskController", f"Updated position for {token}")

        except Exception as e:
            logging.error(f"Error updating position for {token}: {e}")

    def get_risk_summary(self) -> Dict[str, Any]:
        """Get comprehensive risk summary"""
        try:
            total_positions = len(self.position_cache)
            total_exposure = sum(abs(pos.get('size', 0)) for pos in self.position_cache.values())

            recent_assessments = self.risk_history[-10:] if self.risk_history else []
            avg_risk_score = np.mean([a.risk_score for a in recent_assessments]) if recent_assessments else 0.0

            return {
                'total_positions': total_positions,
                'total_exposure': total_exposure,
                'portfolio_value': self.portfolio_value,
                'daily_pnl': self.daily_pnl,
                'leverage': total_exposure / self.portfolio_value if self.portfolio_value > 0 else 0,
                'avg_risk_score': avg_risk_score,
                'recent_assessments': len(recent_assessments),
                'risk_thresholds': {
                    'max_position_size': self.thresholds.max_position_size,
                    'max_daily_drawdown': self.thresholds.max_daily_drawdown,
                    'max_concentration': self.thresholds.max_concentration,
                    'volatility_limit': self.thresholds.volatility_limit
                },
                'current_metrics': self.risk_metrics
            }

        except Exception as e:
            logging.error(f"Error generating risk summary: {e}")
            return {'error': str(e)}

    def emergency_stop(self) -> Dict[str, Any]:
        """Emergency stop mechanism"""
        try:
            log_event("RiskController", "EMERGENCY STOP ACTIVATED", level="critical")

            # Clear all positions (in production, this would close positions)
            emergency_actions = []
            for token, position in self.position_cache.items():
                if abs(position.get('size', 0)) > 0:
                    emergency_actions.append({
                        'action': 'CLOSE_POSITION',
                        'token': token,
                        'size': position.get('size', 0),
                        'reason': 'EMERGENCY_STOP'
                    })

            return {
                'emergency_stop_activated': True,
                'timestamp': datetime.now(),
                'actions_required': emergency_actions,
                'total_positions_to_close': len(emergency_actions)
            }

        except Exception as e:
            logging.error(f"Error in emergency stop: {e}")
            return {'error': str(e), 'emergency_stop_activated': False}
