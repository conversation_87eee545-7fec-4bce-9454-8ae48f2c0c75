"""
Test suite for rife-ai backend integration
Validates all extracted and enhanced components
"""
import pytest
import asyncio
from datetime import datetime
from unittest.mock import Mock, AsyncMock

# Test imports for all new components
from src.ml.prediction_system import RealTimePredictionSystem, PredictionResult
from src.ml.model_architecture import ModelConfig, create_lightweight_model
from src.analytics.enhanced_market_analyzer import EnhancedMarketAnalyzer, AnalysisConfig
from src.risk.advanced_risk_controller import AdvancedRiskController, RiskThresholds, RiskAssessment
from src.execution.enhanced_order_manager import EnhancedOrderManager, OrderRequest, OrderType
from src.agents.alpha_agent import AlphaAgent
from src.agents.global_risk_manager import GlobalRiskManager
from src.data.models import SignalModel

class TestMLIntegration:
    """Test ML prediction system integration"""
    
    def test_model_config_creation(self):
        """Test model configuration creation"""
        config = ModelConfig.get_config()
        assert config is not None
        assert 'sequence_length' in config
        assert 'market_features' in config
        
    def test_model_config_validation(self):
        """Test model configuration validation"""
        valid_config = {'sequence_length': 60, 'market_features': 10}
        assert ModelConfig.validate_config(valid_config)
        
        invalid_config = {'sequence_length': 60}  # Missing market_features
        assert not ModelConfig.validate_config(invalid_config)
        
    def test_lightweight_model_creation(self):
        """Test lightweight model creation"""
        config = ModelConfig.get_config({'model_type': 'lightweight'})
        model = create_lightweight_model(config)
        # Model creation might fail without TensorFlow, which is expected
        assert model is None or hasattr(model, 'predict')
        
    @pytest.mark.asyncio
    async def test_prediction_system_initialization(self):
        """Test prediction system initialization"""
        prediction_system = RealTimePredictionSystem()
        assert prediction_system is not None
        assert not prediction_system.is_initialized
        
        # Mock data service
        mock_data_service = Mock()
        await prediction_system.initialize(mock_data_service)
        
        # Should be initialized even without ML model
        assert prediction_system.is_initialized
        
    @pytest.mark.asyncio
    async def test_prediction_generation(self):
        """Test prediction generation with fallback"""
        prediction_system = RealTimePredictionSystem()
        mock_data_service = Mock()
        await prediction_system.initialize(mock_data_service)
        
        # Test rule-based prediction (fallback)
        test_data = {
            'price': 100.0,
            'price_change_24h': 5.0,
            'volume_24h': 2000000
        }
        
        prediction = prediction_system._generate_rule_based_prediction('TEST_TOKEN', test_data)
        assert isinstance(prediction, PredictionResult)
        assert prediction.token == 'TEST_TOKEN'
        assert prediction.direction in ['BUY', 'SELL', 'HOLD']

class TestEnhancedAnalytics:
    """Test enhanced market analyzer"""
    
    def test_analysis_config_creation(self):
        """Test analysis configuration"""
        config = AnalysisConfig()
        assert config.window_sizes is not None
        assert config.volatility_periods is not None
        assert config.correlation_lookback > 0
        
    def test_market_analyzer_initialization(self):
        """Test market analyzer initialization"""
        analyzer = EnhancedMarketAnalyzer()
        assert analyzer is not None
        assert analyzer.config is not None
        
    @pytest.mark.asyncio
    async def test_market_analysis(self):
        """Test market analysis with sample data"""
        analyzer = EnhancedMarketAnalyzer()
        
        test_market_data = {
            'market_id': 'TEST_MARKET',
            'token': 'TEST_TOKEN',
            'price': 100.0,
            'volume_24h': 1000000,
            'market_cap': 50000000
        }
        
        result = await analyzer.analyze_market_conditions(test_market_data)
        assert result is not None
        assert result.market_id == 'TEST_MARKET'
        assert result.overall_score >= 0.0
        assert len(result.recommendations) > 0

class TestAdvancedRiskManagement:
    """Test advanced risk management"""
    
    def test_risk_thresholds_creation(self):
        """Test risk thresholds configuration"""
        thresholds = RiskThresholds()
        assert thresholds.max_position_size > 0
        assert thresholds.max_daily_drawdown > 0
        assert thresholds.volatility_limit > 0
        
    def test_risk_controller_initialization(self):
        """Test risk controller initialization"""
        controller = AdvancedRiskController()
        assert controller is not None
        assert controller.thresholds is not None
        
    @pytest.mark.asyncio
    async def test_trade_validation(self):
        """Test trade validation"""
        controller = AdvancedRiskController()
        
        # Create test signal
        test_signal = SignalModel(
            token='TEST_TOKEN',
            entry_price=100.0,
            direction='BUY',
            confidence=0.8
        )
        
        test_market_data = {
            'price': 100.0,
            'volume_24h': 1000000,
            'volatility': 0.5
        }
        
        assessment = await controller.validate_trade(test_signal, test_market_data)
        assert isinstance(assessment, RiskAssessment)
        assert assessment.risk_score >= 0.0
        assert assessment.risk_score <= 1.0

class TestEnhancedOrderManagement:
    """Test enhanced order management"""
    
    def test_order_manager_initialization(self):
        """Test order manager initialization"""
        order_manager = EnhancedOrderManager()
        assert order_manager is not None
        assert not order_manager.is_initialized
        
    def test_order_request_creation(self):
        """Test order request creation"""
        order_request = OrderRequest(
            token='TEST_TOKEN',
            direction='BUY',
            size=100.0,
            order_type=OrderType.MARKET
        )
        assert order_request.token == 'TEST_TOKEN'
        assert order_request.direction == 'BUY'
        assert order_request.order_type == OrderType.MARKET
        
    def test_order_id_generation(self):
        """Test order ID generation"""
        order_manager = EnhancedOrderManager()
        order_id1 = order_manager._generate_order_id()
        order_id2 = order_manager._generate_order_id()
        
        assert order_id1 != order_id2
        assert 'NEXUS_' in order_id1
        assert 'NEXUS_' in order_id2

class TestEnhancedAgents:
    """Test enhanced agent integration"""
    
    @pytest.mark.asyncio
    async def test_enhanced_alpha_agent(self):
        """Test enhanced ALPHA agent"""
        alpha_agent = AlphaAgent(enable_ml=False)  # Disable ML for testing
        await alpha_agent.initialize()
        
        assert alpha_agent.is_initialized
        
        # Test system status
        status = alpha_agent.get_system_status()
        assert 'initialized' in status
        assert 'ml_enabled' in status
        
    def test_enhanced_global_risk_manager(self):
        """Test enhanced global risk manager"""
        risk_manager = GlobalRiskManager()
        assert risk_manager is not None
        assert risk_manager.advanced_controller is not None
        
        # Test risk status
        status = risk_manager.get_risk_status()
        assert 'current_risk' in status
        assert 'advanced_risk_summary' in status
        
        # Test recommendations
        recommendations = risk_manager.get_risk_recommendations()
        assert isinstance(recommendations, list)
        assert len(recommendations) > 0

class TestIntegrationFlow:
    """Test end-to-end integration flow"""
    
    @pytest.mark.asyncio
    async def test_signal_generation_flow(self):
        """Test complete signal generation flow"""
        # Initialize components
        alpha_agent = AlphaAgent(enable_ml=False)
        await alpha_agent.initialize()
        
        risk_manager = GlobalRiskManager()
        
        # Test market data
        test_market_data = {
            'token': 'TEST_TOKEN',
            'price': 100.0,
            'volume_24h': 1000000,
            'price_change_24h': 5.0
        }
        
        # Generate signal
        signal = await alpha_agent.generate_signal(test_market_data, 'TEST_TOKEN')
        
        if signal:  # Signal might be None if conditions aren't met
            # Validate with risk manager
            assessment = await risk_manager.validate_trade_comprehensive(signal, test_market_data)
            assert isinstance(assessment, RiskAssessment)
            
    def test_system_health_checks(self):
        """Test system health checks for all components"""
        # Test all components can be instantiated
        prediction_system = RealTimePredictionSystem()
        market_analyzer = EnhancedMarketAnalyzer()
        risk_controller = AdvancedRiskController()
        order_manager = EnhancedOrderManager()
        alpha_agent = AlphaAgent(enable_ml=False)
        risk_manager = GlobalRiskManager()
        
        # All should be created successfully
        assert all([
            prediction_system is not None,
            market_analyzer is not None,
            risk_controller is not None,
            order_manager is not None,
            alpha_agent is not None,
            risk_manager is not None
        ])
        
        # Test health methods where available
        assert alpha_agent.health() in [True, False]  # Should return boolean
        
if __name__ == "__main__":
    # Run basic tests
    print("Running rife-ai integration tests...")
    
    # Test basic imports
    try:
        from src.ml.prediction_system import RealTimePredictionSystem
        from src.analytics.enhanced_market_analyzer import EnhancedMarketAnalyzer
        from src.risk.advanced_risk_controller import AdvancedRiskController
        from src.execution.enhanced_order_manager import EnhancedOrderManager
        print("✅ All imports successful")
    except ImportError as e:
        print(f"❌ Import error: {e}")
        
    # Test basic instantiation
    try:
        prediction_system = RealTimePredictionSystem()
        market_analyzer = EnhancedMarketAnalyzer()
        risk_controller = AdvancedRiskController()
        order_manager = EnhancedOrderManager()
        print("✅ All components can be instantiated")
    except Exception as e:
        print(f"❌ Instantiation error: {e}")
        
    print("Integration validation complete!")
