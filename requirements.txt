# 20 PIP Challenge System Orchestrator Dependencies

# Core Python packages
asyncio-mqtt==0.16.1
aiofiles==23.2.1
aiohttp==3.8.3
python-dotenv==1.0.1

# Solana blockchain
solana==0.32.0
solders==0.19.0
anchorpy==0.20.1

# Data processing
pandas==2.0.0
numpy==1.24.0
pyyaml==6.0.1

# Logging and monitoring
loguru==0.7.2
tabulate==0.9.0

# HTTP requests
requests==2.31.0

# Scheduling
apscheduler==3.10.1

# Performance
uvloop==0.17.0

# Quantitative finance and backtesting
pypfopt==1.5.5
vectorbt==0.25.0

# Machine Learning (optional - for enhanced capabilities)
tensorflow>=2.12.0,<3.0.0
scikit-learn>=1.3.0
scipy>=1.10.0

# Enhanced analytics
ta-lib>=0.4.25  # Technical analysis library

# Development and testing
pytest==7.4.0
pytest-asyncio==0.21.1
